import com.pic.o2o.common.HTTPSSOAPDiagnostic;

/**
 * SOAP 連線診斷測試工具
 * 專門診斷 stage-posapi2.tixpress.tw 的連線問題
 */
public class diagnose_soap_connection {
    
    public static void main(String[] args) {
        System.out.println("=== SOAP 連線問題診斷工具 ===");
        System.out.println("目標：解決 2001 系統連線失敗錯誤");
        System.out.println("=============================");
        System.out.println();
        
        // 目標服務資訊
        String serviceUrl = "https://stage-posapi2.tixpress.tw/POSProxyService.svc";
        int timeout = 23; // 與錯誤日誌中的設定一致
        
        System.out.println("診斷目標:");
        System.out.println("URL: " + serviceUrl);
        System.out.println("逾時: " + timeout + " 秒");
        System.out.println("錯誤: 2001 系統連線失敗");
        System.out.println();
        
        // 執行診斷
        try {
            System.out.println("=== 階段 1：標準診斷 ===");
            HTTPSSOAPDiagnostic.DiagnosticResult result1 = 
                HTTPSSOAPDiagnostic.diagnoseHTTPS(serviceUrl, timeout);
            
            if (!result1.isSuccess()) {
                System.out.println();
                System.out.println("=== 階段 2：SSL 憑證問題診斷 ===");
                System.out.println("嘗試跳過 SSL 憑證驗證進行測試...");
                
                // 設定信任所有憑證（僅用於診斷）
                HTTPSSOAPDiagnostic.setupTrustAllCertificates();
                
                System.out.println("重新執行診斷...");
                HTTPSSOAPDiagnostic.DiagnosticResult result2 = 
                    HTTPSSOAPDiagnostic.diagnoseHTTPS(serviceUrl, timeout);
                
                if (result2.isSuccess()) {
                    System.out.println();
                    System.out.println("🔍 診斷結論：SSL 憑證問題");
                    System.out.println("跳過憑證驗證後連線成功，表示問題出在 SSL 憑證驗證。");
                    System.out.println();
                    System.out.println("解決方案：");
                    System.out.println("1. 將服務的 SSL 憑證加入 Java 信任庫");
                    System.out.println("2. 更新 Java 版本以支援最新的 CA 憑證");
                    System.out.println("3. 檢查系統時間是否正確");
                    System.out.println("4. 聯絡服務提供者確認憑證狀態");
                } else {
                    System.out.println();
                    System.out.println("🔍 診斷結論：非 SSL 憑證問題");
                    System.out.println("即使跳過憑證驗證仍然失敗，問題可能是：");
                    System.out.println("1. 網路連線問題");
                    System.out.println("2. 防火牆阻擋");
                    System.out.println("3. 服務端點不可用");
                    System.out.println("4. DNS 解析問題");
                }
            } else {
                System.out.println();
                System.out.println("🎉 診斷結論：連線正常");
                System.out.println("診斷顯示連線正常，問題可能在於：");
                System.out.println("1. SOAP 請求格式");
                System.out.println("2. 認證或授權問題");
                System.out.println("3. 服務端的業務邏輯錯誤");
            }
            
        } catch (Exception e) {
            System.out.println("診斷過程發生錯誤: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
        System.out.println("=== 額外診斷建議 ===");
        System.out.println("1. 檢查 transfile.txt 中的 URL 設定");
        System.out.println("2. 使用瀏覽器訪問 " + serviceUrl + " 檢查服務狀態");
        System.out.println("3. 使用 telnet stage-posapi2.tixpress.tw 443 測試連線");
        System.out.println("4. 使用 nslookup stage-posapi2.tixpress.tw 測試 DNS");
        System.out.println("5. 檢查公司防火牆和代理伺服器設定");
        System.out.println();
        System.out.println("=== 診斷完成 ===");
    }
}
