@echo off
echo ===== SOAP 相關檔案編譯測試 =====
echo.

REM 設定 CLASSPATH
set CLASSPATH=libs\*;bin

echo 1. 編譯 SOAP 相關檔案...
echo.

echo 編譯 SOAPXMLValidator.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\SOAPXMLValidator.java
if %ERRORLEVEL% neq 0 (
    echo ❌ SOAPXMLValidator.java 編譯失敗
    goto :error
) else (
    echo ✅ SOAPXMLValidator.java 編譯成功
)

echo.
echo 編譯 SOAPConnectionDiagnostic.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\SOAPConnectionDiagnostic.java
if %ERRORLEVEL% neq 0 (
    echo ❌ SOAPConnectionDiagnostic.java 編譯失敗
    goto :error
) else (
    echo ✅ SOAPConnectionDiagnostic.java 編譯成功
)

echo.
echo 編譯 SimpleSOAPClient.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\SimpleSOAPClient.java
if %ERRORLEVEL% neq 0 (
    echo ❌ SimpleSOAPClient.java 編譯失敗
    goto :error
) else (
    echo ✅ SimpleSOAPClient.java 編譯成功
)

echo.
echo 編譯 SOAPClient.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\SOAPClient.java
if %ERRORLEVEL% neq 0 (
    echo ❌ SOAPClient.java 編譯失敗
    goto :error
) else (
    echo ✅ SOAPClient.java 編譯成功
)

echo.
echo 編譯 SOAPService.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\SOAPService.java
if %ERRORLEVEL% neq 0 (
    echo ❌ SOAPService.java 編譯失敗
    goto :error
) else (
    echo ✅ SOAPService.java 編譯成功
)

echo.
echo 2. 重新編譯 OLTP.java（驗證相依性）...
javac -cp "%CLASSPATH%" -d bin src\com\pic\oltp\OLTP.java
if %ERRORLEVEL% neq 0 (
    echo ❌ OLTP.java 編譯失敗（SOAP 相依性問題）
    goto :error
) else (
    echo ✅ OLTP.java 編譯成功（SOAP 相依性正常）
)

echo.
echo 3. 檢查編譯後的 class 檔案...
echo.

if exist "bin\com\pic\o2o\common\SOAPXMLValidator.class" (
    echo ✅ SOAPXMLValidator.class 存在
) else (
    echo ❌ SOAPXMLValidator.class 不存在
)

if exist "bin\com\pic\o2o\common\SOAPConnectionDiagnostic.class" (
    echo ✅ SOAPConnectionDiagnostic.class 存在
) else (
    echo ❌ SOAPConnectionDiagnostic.class 不存在
)

if exist "bin\com\pic\o2o\common\SimpleSOAPClient.class" (
    echo ✅ SimpleSOAPClient.class 存在
) else (
    echo ❌ SimpleSOAPClient.class 不存在
)

if exist "bin\com\pic\o2o\common\SOAPClient.class" (
    echo ✅ SOAPClient.class 存在
) else (
    echo ❌ SOAPClient.class 不存在
)

if exist "bin\com\pic\o2o\common\SOAPService.class" (
    echo ✅ SOAPService.class 存在
) else (
    echo ❌ SOAPService.class 不存在
)

if exist "bin\com\pic\oltp\OLTP.class" (
    echo ✅ OLTP.class 存在
) else (
    echo ❌ OLTP.class 不存在
)

echo.
echo ===== 編譯測試完成 =====
echo ✅ 所有 SOAP 相關檔案編譯成功！
echo ✅ 檔案位置和 import 語句都正確！
echo.
goto :end

:error
echo.
echo ===== 編譯測試失敗 =====
echo ❌ 請檢查錯誤訊息並修正問題
echo.
exit /b 1

:end
echo 建議：執行 'java -cp "libs/*;bin" com.pic.oltp.OLTP' 來測試 SOAP 功能
echo.
pause
