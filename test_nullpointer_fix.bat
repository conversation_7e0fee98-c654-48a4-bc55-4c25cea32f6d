@echo off
echo ===== NullPointerException 修復測試 =====
echo.
echo 測試目標：修復 QueueListener.java 第186行的 NullPointerException
echo 錯誤：java.lang.NullPointerException at QueueListener.java:186
echo 修復內容：
echo   1. 添加 oltpData null 檢查
echo   2. 實作安全的 FROM 欄位獲取方法
echo   3. 添加 SOAP 回應的狀態碼和描述提取
echo   4. 改進錯誤處理和調試資訊
echo ==========================================
echo.

REM 設定 CLASSPATH
set CLASSPATH=libs\*;bin

echo 1. 檢查必要目錄...
if not exist "bin" mkdir bin
if not exist "bin\com\pic\o2o\common" mkdir bin\com\pic\o2o\common

echo.
echo 2. 編譯修復後的程式碼...

echo 編譯 QueueListener.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\QueueListener.java
if %ERRORLEVEL% neq 0 (
    echo ❌ QueueListener.java 編譯失敗
    echo 可能是 NullPointerException 修復代碼有問題
    goto :error
) else (
    echo ✅ QueueListener.java 編譯成功
)

echo.
echo 3. 創建 NullPointerException 修復測試程式...

REM 創建測試程式
echo import com.pic.o2o.common.QueueListener; > TestNullPointerFix.java
echo import org.dom4j.Element; >> TestNullPointerFix.java
echo import org.dom4j.DocumentHelper; >> TestNullPointerFix.java
echo import java.lang.reflect.Method; >> TestNullPointerFix.java
echo. >> TestNullPointerFix.java
echo public class TestNullPointerFix { >> TestNullPointerFix.java
echo     public static void main(String[] args) { >> TestNullPointerFix.java
echo         try { >> TestNullPointerFix.java
echo             System.out.println("=== NullPointerException 修復測試 ==="); >> TestNullPointerFix.java
echo             System.out.println(); >> TestNullPointerFix.java
echo. >> TestNullPointerFix.java
echo             // 創建 QueueListener 實例 >> TestNullPointerFix.java
echo             QueueListener listener = new QueueListener(); >> TestNullPointerFix.java
echo. >> TestNullPointerFix.java
echo             // 測試案例 1：null oltpData >> TestNullPointerFix.java
echo             System.out.println("=== 測試案例 1：null oltpData ==="); >> TestNullPointerFix.java
echo             testGetFromValue(listener, null); >> TestNullPointerFix.java
echo             testGetStatCode(listener, null); >> TestNullPointerFix.java
echo             testGetStatDesc(listener, null); >> TestNullPointerFix.java
echo             System.out.println(); >> TestNullPointerFix.java
echo. >> TestNullPointerFix.java
echo             // 測試案例 2：沒有 HEADER 的 XML（模擬 SOAP 回應） >> TestNullPointerFix.java
echo             System.out.println("=== 測試案例 2：SOAP 回應格式 ==="); >> TestNullPointerFix.java
echo             String soapXml = "^<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\"^>" + >> TestNullPointerFix.java
echo                              "^<s:Body^>" + >> TestNullPointerFix.java
echo                              "^<ManageTerminalResponse^>" + >> TestNullPointerFix.java
echo                              "^<ResultCode^>0000^</ResultCode^>" + >> TestNullPointerFix.java
echo                              "^<ResultMessage^>Success^</ResultMessage^>" + >> TestNullPointerFix.java
echo                              "^</ManageTerminalResponse^>" + >> TestNullPointerFix.java
echo                              "^</s:Body^>" + >> TestNullPointerFix.java
echo                              "^</s:Envelope^>"; >> TestNullPointerFix.java
echo             Element soapElement = DocumentHelper.parseText(soapXml).getRootElement(); >> TestNullPointerFix.java
echo             testGetFromValue(listener, soapElement); >> TestNullPointerFix.java
echo             testGetStatCode(listener, soapElement); >> TestNullPointerFix.java
echo             testGetStatDesc(listener, soapElement); >> TestNullPointerFix.java
echo             System.out.println(); >> TestNullPointerFix.java
echo. >> TestNullPointerFix.java
echo             // 測試案例 3：正常的業務 XML >> TestNullPointerFix.java
echo             System.out.println("=== 測試案例 3：正常業務 XML ==="); >> TestNullPointerFix.java
echo             String businessXml = "^<ROOT^>" + >> TestNullPointerFix.java
echo                                  "^<HEADER^>" + >> TestNullPointerFix.java
echo                                  "^<FROM^>TEST_SYSTEM^</FROM^>" + >> TestNullPointerFix.java
echo                                  "^<STATCODE^>0000^</STATCODE^>" + >> TestNullPointerFix.java
echo                                  "^<STATDESC^>Success^</STATDESC^>" + >> TestNullPointerFix.java
echo                                  "^</HEADER^>" + >> TestNullPointerFix.java
echo                                  "^</ROOT^>"; >> TestNullPointerFix.java
echo             Element businessElement = DocumentHelper.parseText(businessXml).getRootElement(); >> TestNullPointerFix.java
echo             testGetFromValue(listener, businessElement); >> TestNullPointerFix.java
echo             testGetStatCode(listener, businessElement); >> TestNullPointerFix.java
echo             testGetStatDesc(listener, businessElement); >> TestNullPointerFix.java
echo             System.out.println(); >> TestNullPointerFix.java
echo. >> TestNullPointerFix.java
echo             // 測試案例 4：缺少 FROM 欄位的 XML >> TestNullPointerFix.java
echo             System.out.println("=== 測試案例 4：缺少 FROM 欄位 ==="); >> TestNullPointerFix.java
echo             String incompleteXml = "^<ROOT^>" + >> TestNullPointerFix.java
echo                                   "^<HEADER^>" + >> TestNullPointerFix.java
echo                                   "^<STATCODE^>1001^</STATCODE^>" + >> TestNullPointerFix.java
echo                                   "^<STATDESC^>Error^</STATDESC^>" + >> TestNullPointerFix.java
echo                                   "^</HEADER^>" + >> TestNullPointerFix.java
echo                                   "^</ROOT^>"; >> TestNullPointerFix.java
echo             Element incompleteElement = DocumentHelper.parseText(incompleteXml).getRootElement(); >> TestNullPointerFix.java
echo             testGetFromValue(listener, incompleteElement); >> TestNullPointerFix.java
echo             testGetStatCode(listener, incompleteElement); >> TestNullPointerFix.java
echo             testGetStatDesc(listener, incompleteElement); >> TestNullPointerFix.java
echo             System.out.println(); >> TestNullPointerFix.java
echo. >> TestNullPointerFix.java
echo             System.out.println("=== 測試完成 ==="); >> TestNullPointerFix.java
echo             System.out.println("✅ 所有測試案例都沒有拋出 NullPointerException"); >> TestNullPointerFix.java
echo. >> TestNullPointerFix.java
echo         } catch (Exception e) { >> TestNullPointerFix.java
echo             System.out.println("❌ 測試失敗: " + e.getMessage()); >> TestNullPointerFix.java
echo             e.printStackTrace(); >> TestNullPointerFix.java
echo         } >> TestNullPointerFix.java
echo     } >> TestNullPointerFix.java
echo. >> TestNullPointerFix.java
echo     private static void testGetFromValue(QueueListener listener, Element element) { >> TestNullPointerFix.java
echo         try { >> TestNullPointerFix.java
echo             Method method = QueueListener.class.getDeclaredMethod("getFromValue", Element.class); >> TestNullPointerFix.java
echo             method.setAccessible(true); >> TestNullPointerFix.java
echo             String result = (String) method.invoke(listener, element); >> TestNullPointerFix.java
echo             System.out.println("getFromValue 結果: " + result); >> TestNullPointerFix.java
echo         } catch (Exception e) { >> TestNullPointerFix.java
echo             System.out.println("❌ getFromValue 測試失敗: " + e.getMessage()); >> TestNullPointerFix.java
echo         } >> TestNullPointerFix.java
echo     } >> TestNullPointerFix.java
echo. >> TestNullPointerFix.java
echo     private static void testGetStatCode(QueueListener listener, Element element) { >> TestNullPointerFix.java
echo         try { >> TestNullPointerFix.java
echo             Method method = QueueListener.class.getDeclaredMethod("getStatCode", Element.class); >> TestNullPointerFix.java
echo             method.setAccessible(true); >> TestNullPointerFix.java
echo             String result = (String) method.invoke(listener, element); >> TestNullPointerFix.java
echo             System.out.println("getStatCode 結果: " + result); >> TestNullPointerFix.java
echo         } catch (Exception e) { >> TestNullPointerFix.java
echo             System.out.println("❌ getStatCode 測試失敗: " + e.getMessage()); >> TestNullPointerFix.java
echo         } >> TestNullPointerFix.java
echo     } >> TestNullPointerFix.java
echo. >> TestNullPointerFix.java
echo     private static void testGetStatDesc(QueueListener listener, Element element) { >> TestNullPointerFix.java
echo         try { >> TestNullPointerFix.java
echo             Method method = QueueListener.class.getDeclaredMethod("getStatDesc", Element.class); >> TestNullPointerFix.java
echo             method.setAccessible(true); >> TestNullPointerFix.java
echo             String result = (String) method.invoke(listener, element); >> TestNullPointerFix.java
echo             System.out.println("getStatDesc 結果: " + result); >> TestNullPointerFix.java
echo         } catch (Exception e) { >> TestNullPointerFix.java
echo             System.out.println("❌ getStatDesc 測試失敗: " + e.getMessage()); >> TestNullPointerFix.java
echo         } >> TestNullPointerFix.java
echo     } >> TestNullPointerFix.java
echo } >> TestNullPointerFix.java

echo 編譯測試程式...
javac -cp "%CLASSPATH%" TestNullPointerFix.java
if %ERRORLEVEL% equ 0 (
    echo.
    echo 4. 執行 NullPointerException 修復測試...
    echo.
    java -cp "%CLASSPATH%;." TestNullPointerFix
) else (
    echo ⚠️ 測試程式編譯失敗，跳過測試
)

echo.
echo 5. 修復前後對比分析...
echo.
echo 修復前的問題：
echo   ❌ 第186行：oltpData.element("HEADER").elementText("FROM")
echo   ❌ 第213行：oltpData.element("HEADER").elementText("STATCODE")  
echo   ❌ 第219行：oltpData.element("HEADER").elementText("STATDESC")
echo   ❌ 第238行：oltpData.element("HEADER").elementText("STATCODE")
echo   ❌ 第239行：oltpData.element("HEADER").elementText("STATDESC")
echo   ❌ 沒有 null 檢查，導致 NullPointerException
echo.
echo 修復後的改進：
echo   ✅ 添加 oltpData null 檢查
echo   ✅ 實作 getFromValue() 安全獲取 FROM 欄位
echo   ✅ 實作 getStatCode() 安全獲取狀態碼
echo   ✅ 實作 getStatDesc() 安全獲取狀態描述
echo   ✅ 支援從 SOAP 回應中提取相關資訊
echo   ✅ 提供預設值避免 null 問題
echo   ✅ 詳細的錯誤處理和調試資訊
echo.

echo 6. 預期效果...
echo.
echo 修復後應該看到：
echo   ✅ getFromValue 結果: UNKNOWN_SOURCE（null 情況）
echo   ✅ getFromValue 結果: SOAP_ENVELOPE（SOAP 回應）
echo   ✅ getFromValue 結果: TEST_SYSTEM（正常業務 XML）
echo   ✅ getStatCode 結果: 9999（null 情況）
echo   ✅ getStatCode 結果: 0000（SOAP 回應）
echo   ✅ getStatCode 結果: 0000（正常業務 XML）
echo   ✅ 所有測試案例都沒有拋出 NullPointerException
echo.
echo 不應該再看到：
echo   ❌ java.lang.NullPointerException at QueueListener.java:186
echo   ❌ 任何與 oltpData.element("HEADER") 相關的 null 錯誤
echo.

echo ==========================================
echo.
echo ✅ NullPointerException 修復完成
echo.
echo 主要修復項目：
echo   1. ✅ 添加 oltpData null 檢查和早期返回
echo   2. ✅ 實作 getFromValue() 安全方法
echo   3. ✅ 實作 getStatCode() 和 getStatDesc() 安全方法
echo   4. ✅ 添加 SOAP 回應的資訊提取邏輯
echo   5. ✅ 改進錯誤處理和調試資訊
echo   6. ✅ 提供合理的預設值
echo.
echo 下一步：
echo 1. 重啟應用程式
echo 2. 重新測試 SOAP 連線和回應處理
echo 3. 檢查是否不再出現 NullPointerException
echo 4. 驗證日誌記錄是否正常
echo.
goto :end

:error
echo.
echo ❌ NullPointerException 修復測試失敗
echo 請檢查編譯錯誤並修正問題
echo.
exit /b 1

:end
echo 如需協助，請檢查 Console 輸出中的詳細測試結果
if exist "TestNullPointerFix.java" del "TestNullPointerFix.java"
if exist "TestNullPointerFix.class" del "TestNullPointerFix.class"
pause
