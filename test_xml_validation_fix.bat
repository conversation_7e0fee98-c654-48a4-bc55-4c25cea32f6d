@echo off
echo ===== XML 結構驗證修復測試 =====
echo.
echo 測試目標：修復兩個關鍵問題
echo 問題 1：validateXMLStructure 方法標籤計數邏輯錯誤
echo 問題 2：檔案寫入架構不一致（QueueListener vs OLTP.java）
echo 修復內容：
echo   1. 修復標籤計數邏輯，使用正確的正則表達式
echo   2. 優先使用 XML 解析測試作為驗證標準
echo   3. 移除 QueueListener 中重複的檔案寫入邏輯
echo   4. 統一由 OLTP.java 處理檔案寫入
echo ==========================================
echo.

REM 設定 CLASSPATH
set CLASSPATH=libs\*;bin

echo 1. 檢查必要目錄...
if not exist "bin" mkdir bin
if not exist "bin\com\pic\o2o\common" mkdir bin\com\pic\o2o\common

echo.
echo 2. 編譯修復後的程式碼...

echo 編譯 QueueListener.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\QueueListener.java
if %ERRORLEVEL% neq 0 (
    echo ❌ QueueListener.java 編譯失敗
    echo 可能是 XML 驗證修復代碼有問題
    goto :error
) else (
    echo ✅ QueueListener.java 編譯成功
)

echo.
echo 3. 創建 XML 結構驗證修復測試程式...

REM 創建測試程式
echo import com.pic.o2o.common.QueueListener; > TestXMLValidationFix.java
echo import java.lang.reflect.Method; >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo public class TestXMLValidationFix { >> TestXMLValidationFix.java
echo     public static void main(String[] args) { >> TestXMLValidationFix.java
echo         try { >> TestXMLValidationFix.java
echo             System.out.println("=== XML 結構驗證修復測試 ==="); >> TestXMLValidationFix.java
echo             System.out.println(); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             // 創建 QueueListener 實例 >> TestXMLValidationFix.java
echo             QueueListener listener = new QueueListener(); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             // 測試案例 1：已清理的乾淨 XML（應該通過驗證） >> TestXMLValidationFix.java
echo             System.out.println("=== 測試案例 1：已清理的乾淨 XML ==="); >> TestXMLValidationFix.java
echo             String cleanXml = "^<ManageTerminalResponse^>" + >> TestXMLValidationFix.java
echo                               "^<ManageTerminalResult^>" + >> TestXMLValidationFix.java
echo                               "^<Checksum^>a87519574d731f03955b107379c9c5be^</Checksum^>" + >> TestXMLValidationFix.java
echo                               "^<Message^>Success^</Message^>" + >> TestXMLValidationFix.java
echo                               "^<ResponseCode^>0000^</ResponseCode^>" + >> TestXMLValidationFix.java
echo                               "^<ServerDate^>20250729^</ServerDate^>" + >> TestXMLValidationFix.java
echo                               "^<ServerTime^>160543^</ServerTime^>" + >> TestXMLValidationFix.java
echo                               "^<WorkKey^>eK+DO15GrIpkI/2muc5i3YryM2wrzTMYQQfoSELTw/833SOEtTXwSQ==^</WorkKey^>" + >> TestXMLValidationFix.java
echo                               "^</ManageTerminalResult^>" + >> TestXMLValidationFix.java
echo                               "^</ManageTerminalResponse^>"; >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             testXMLValidation(listener, cleanXml, "已清理的乾淨 XML"); >> TestXMLValidationFix.java
echo             System.out.println(); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             // 測試案例 2：損壞的 XML（應該失敗驗證） >> TestXMLValidationFix.java
echo             System.out.println("=== 測試案例 2：損壞的 XML ==="); >> TestXMLValidationFix.java
echo             String brokenXml = "^<ManageTerminalResponse^>" + >> TestXMLValidationFix.java
echo                               "^<ManageTerminalResult^>" + >> TestXMLValidationFix.java
echo                               "^<Checksum^>a87519574d731f03955b107379c9c5be^<Checksum^>" + // 損壞的結束標籤 >> TestXMLValidationFix.java
echo                               "^<Message^>Success^<Message^>" + // 損壞的結束標籤 >> TestXMLValidationFix.java
echo                               "^<ResponseCode^>0000^<ResponseCode^>" + // 損壞的結束標籤 >> TestXMLValidationFix.java
echo                               "^</ManageTerminalResult^>" + >> TestXMLValidationFix.java
echo                               "^</ManageTerminalResponse^>"; >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             testXMLValidation(listener, brokenXml, "損壞的 XML"); >> TestXMLValidationFix.java
echo             System.out.println(); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             // 測試案例 3：複雜但有效的 XML >> TestXMLValidationFix.java
echo             System.out.println("=== 測試案例 3：複雜但有效的 XML ==="); >> TestXMLValidationFix.java
echo             String complexXml = "^<root^>" + >> TestXMLValidationFix.java
echo                                "^<level1^>" + >> TestXMLValidationFix.java
echo                                "^<level2^>" + >> TestXMLValidationFix.java
echo                                "^<data^>test value^</data^>" + >> TestXMLValidationFix.java
echo                                "^<number^>123^</number^>" + >> TestXMLValidationFix.java
echo                                "^</level2^>" + >> TestXMLValidationFix.java
echo                                "^<another^>another value^</another^>" + >> TestXMLValidationFix.java
echo                                "^</level1^>" + >> TestXMLValidationFix.java
echo                                "^</root^>"; >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             testXMLValidation(listener, complexXml, "複雜但有效的 XML"); >> TestXMLValidationFix.java
echo             System.out.println(); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             // 測試案例 4：測試標籤計數功能 >> TestXMLValidationFix.java
echo             System.out.println("=== 測試案例 4：測試標籤計數功能 ==="); >> TestXMLValidationFix.java
echo             testTagCounting(listener, cleanXml); >> TestXMLValidationFix.java
echo             System.out.println(); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             // 測試案例 5：測試命名空間移除和驗證的完整流程 >> TestXMLValidationFix.java
echo             System.out.println("=== 測試案例 5：完整的命名空間移除和驗證流程 ==="); >> TestXMLValidationFix.java
echo             String xmlWithNamespaces = "^<ManageTerminalResponse xmlns=\"http://ticketxpress.com.tw/\"^>" + >> TestXMLValidationFix.java
echo                                       "^<ManageTerminalResult xmlns:a=\"http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common\"^>" + >> TestXMLValidationFix.java
echo                                       "^<a:Checksum^>a87519574d731f03955b107379c9c5be^</a:Checksum^>" + >> TestXMLValidationFix.java
echo                                       "^<a:Message^>Success^</a:Message^>" + >> TestXMLValidationFix.java
echo                                       "^<a:ResponseCode^>0000^</a:ResponseCode^>" + >> TestXMLValidationFix.java
echo                                       "^</ManageTerminalResult^>" + >> TestXMLValidationFix.java
echo                                       "^</ManageTerminalResponse^>"; >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             testCompleteNamespaceRemovalFlow(listener, xmlWithNamespaces); >> TestXMLValidationFix.java
echo             System.out.println(); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             System.out.println("=== 測試完成 ==="); >> TestXMLValidationFix.java
echo             System.out.println("✅ XML 結構驗證修復測試完成"); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo         } catch (Exception e) { >> TestXMLValidationFix.java
echo             System.out.println("❌ 測試失敗: " + e.getMessage()); >> TestXMLValidationFix.java
echo             e.printStackTrace(); >> TestXMLValidationFix.java
echo         } >> TestXMLValidationFix.java
echo     } >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo     private static void testXMLValidation(QueueListener listener, String xmlContent, String testName) { >> TestXMLValidationFix.java
echo         try { >> TestXMLValidationFix.java
echo             System.out.println("測試類型: " + testName); >> TestXMLValidationFix.java
echo             System.out.println("XML 內容: " + xmlContent); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             Method method = QueueListener.class.getDeclaredMethod("validateXMLStructure", String.class); >> TestXMLValidationFix.java
echo             method.setAccessible(true); >> TestXMLValidationFix.java
echo             Boolean result = (Boolean) method.invoke(listener, xmlContent); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             System.out.println("驗證結果: " + (result ? "✅ 有效" : "❌ 無效")); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             // 預期結果檢查 >> TestXMLValidationFix.java
echo             boolean expectedValid = !xmlContent.contains("^<Checksum^>a87519574d731f03955b107379c9c5be^<Checksum^>"); >> TestXMLValidationFix.java
echo             boolean testPassed = (result == expectedValid); >> TestXMLValidationFix.java
echo             System.out.println("預期結果: " + (expectedValid ? "有效" : "無效")); >> TestXMLValidationFix.java
echo             System.out.println("測試通過: " + (testPassed ? "✅ 是" : "❌ 否")); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo         } catch (Exception e) { >> TestXMLValidationFix.java
echo             System.out.println("❌ " + testName + " 測試失敗: " + e.getMessage()); >> TestXMLValidationFix.java
echo             e.printStackTrace(); >> TestXMLValidationFix.java
echo         } >> TestXMLValidationFix.java
echo     } >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo     private static void testTagCounting(QueueListener listener, String xmlContent) { >> TestXMLValidationFix.java
echo         try { >> TestXMLValidationFix.java
echo             System.out.println("測試標籤計數功能..."); >> TestXMLValidationFix.java
echo             System.out.println("XML 內容: " + xmlContent); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             Method countOpenMethod = QueueListener.class.getDeclaredMethod("countOpenTags", String.class); >> TestXMLValidationFix.java
echo             countOpenMethod.setAccessible(true); >> TestXMLValidationFix.java
echo             Integer openCount = (Integer) countOpenMethod.invoke(listener, xmlContent); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             Method countCloseMethod = QueueListener.class.getDeclaredMethod("countCloseTags", String.class); >> TestXMLValidationFix.java
echo             countCloseMethod.setAccessible(true); >> TestXMLValidationFix.java
echo             Integer closeCount = (Integer) countCloseMethod.invoke(listener, xmlContent); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             System.out.println("開始標籤數量: " + openCount); >> TestXMLValidationFix.java
echo             System.out.println("結束標籤數量: " + closeCount); >> TestXMLValidationFix.java
echo             System.out.println("標籤配對: " + (openCount.equals(closeCount) ? "✅ 正確" : "❌ 錯誤")); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             // 手動計算預期值 >> TestXMLValidationFix.java
echo             int expectedOpenTags = 8; // ManageTerminalResponse, ManageTerminalResult, Checksum, Message, ResponseCode, ServerDate, ServerTime, WorkKey >> TestXMLValidationFix.java
echo             int expectedCloseTags = 8; // 對應的結束標籤 >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             System.out.println("預期開始標籤: " + expectedOpenTags); >> TestXMLValidationFix.java
echo             System.out.println("預期結束標籤: " + expectedCloseTags); >> TestXMLValidationFix.java
echo             System.out.println("計數準確性: " + (openCount == expectedOpenTags ^&^& closeCount == expectedCloseTags ? "✅ 正確" : "❌ 錯誤")); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo         } catch (Exception e) { >> TestXMLValidationFix.java
echo             System.out.println("❌ 標籤計數測試失敗: " + e.getMessage()); >> TestXMLValidationFix.java
echo             e.printStackTrace(); >> TestXMLValidationFix.java
echo         } >> TestXMLValidationFix.java
echo     } >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo     private static void testCompleteNamespaceRemovalFlow(QueueListener listener, String xmlWithNamespaces) { >> TestXMLValidationFix.java
echo         try { >> TestXMLValidationFix.java
echo             System.out.println("測試完整的命名空間移除和驗證流程..."); >> TestXMLValidationFix.java
echo             System.out.println("原始 XML: " + xmlWithNamespaces); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             // 步驟 1：移除命名空間 >> TestXMLValidationFix.java
echo             Method removeMethod = QueueListener.class.getDeclaredMethod("removeNamespaces", String.class); >> TestXMLValidationFix.java
echo             removeMethod.setAccessible(true); >> TestXMLValidationFix.java
echo             String cleanedXml = (String) removeMethod.invoke(listener, xmlWithNamespaces); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             System.out.println("清理後 XML: " + cleanedXml); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             // 步驟 2：驗證清理後的 XML >> TestXMLValidationFix.java
echo             Method validateMethod = QueueListener.class.getDeclaredMethod("validateXMLStructure", String.class); >> TestXMLValidationFix.java
echo             validateMethod.setAccessible(true); >> TestXMLValidationFix.java
echo             Boolean isValid = (Boolean) validateMethod.invoke(listener, cleanedXml); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             System.out.println("清理後驗證結果: " + (isValid ? "✅ 有效" : "❌ 無效")); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             // 檢查命名空間是否完全移除 >> TestXMLValidationFix.java
echo             boolean hasNamespaces = cleanedXml.contains("xmlns") ^|^| cleanedXml.contains("a:"); >> TestXMLValidationFix.java
echo             System.out.println("命名空間完全移除: " + (!hasNamespaces ? "✅ 是" : "❌ 否")); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             // 檢查結構完整性 >> TestXMLValidationFix.java
echo             boolean hasValidStructure = cleanedXml.contains("^</Checksum^>") ^&^& cleanedXml.contains("^</Message^>"); >> TestXMLValidationFix.java
echo             System.out.println("結構完整性: " + (hasValidStructure ? "✅ 完整" : "❌ 損壞")); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo             boolean overallSuccess = isValid ^&^& !hasNamespaces ^&^& hasValidStructure; >> TestXMLValidationFix.java
echo             System.out.println("整體流程成功: " + (overallSuccess ? "✅ 是" : "❌ 否")); >> TestXMLValidationFix.java
echo. >> TestXMLValidationFix.java
echo         } catch (Exception e) { >> TestXMLValidationFix.java
echo             System.out.println("❌ 完整流程測試失敗: " + e.getMessage()); >> TestXMLValidationFix.java
echo             e.printStackTrace(); >> TestXMLValidationFix.java
echo         } >> TestXMLValidationFix.java
echo     } >> TestXMLValidationFix.java
echo } >> TestXMLValidationFix.java

echo 編譯測試程式...
javac -cp "%CLASSPATH%" TestXMLValidationFix.java
if %ERRORLEVEL% equ 0 (
    echo.
    echo 4. 執行 XML 結構驗證修復測試...
    echo.
    java -cp "%CLASSPATH%;." TestXMLValidationFix
) else (
    echo ⚠️ 測試程式編譯失敗，跳過測試
)

echo.
echo 5. 修復前後對比分析...
echo.
echo 修復前的問題：
echo   ❌ validateXMLStructure 標籤計數邏輯錯誤
echo   ❌ 開始標籤數量: 8，結束標籤數量: 5（錯誤計數）
echo   ❌ 導致有效的 XML 被錯誤地判定為無效
echo   ❌ QueueListener 和 OLTP.java 重複的檔案寫入邏輯
echo   ❌ 架構不一致，維護困難
echo.
echo 修復後的改進：
echo   ✅ 使用 XML 解析測試作為主要驗證標準
echo   ✅ 修復標籤計數邏輯，使用正確的正則表達式
echo   ✅ 移除 QueueListener 中重複的檔案寫入邏輯
echo   ✅ 統一由 OLTP.java 處理檔案寫入
echo   ✅ 保持架構一致性和可維護性
echo.

echo 6. 預期效果...
echo.
echo 修復前的錯誤結果：
echo   ❌ 開始標籤數量: 8
echo   ❌ 結束標籤數量: 5
echo   ❌ XML 結構完整性: ❌ 無效
echo   ❌ 返回原始內容（含命名空間）
echo.
echo 修復後的正確結果：
echo   ✅ XML 解析測試: ✅ 成功
echo   ✅ 開始標籤數量: 8
echo   ✅ 結束標籤數量: 8
echo   ✅ XML 結構完整性: ✅ 有效
echo   ✅ 返回清理後的內容（無命名空間）
echo.
echo 測試結果應該顯示：
echo   ✅ 已清理的乾淨 XML: ✅ 有效
echo   ✅ 損壞的 XML: ❌ 無效
echo   ✅ 複雜但有效的 XML: ✅ 有效
echo   ✅ 標籤配對: ✅ 正確
echo   ✅ 計數準確性: ✅ 正確
echo   ✅ 整體流程成功: ✅ 是
echo.

echo ==========================================
echo.
echo ✅ XML 結構驗證修復完成
echo.
echo 主要修復項目：
echo   1. ✅ 修復 validateXMLStructure 方法的標籤計數邏輯
echo   2. ✅ 優先使用 XML 解析測試作為驗證標準
echo   3. ✅ 實作正確的 countOpenTags 和 countCloseTags 方法
echo   4. ✅ 移除 QueueListener 中重複的檔案寫入邏輯
echo   5. ✅ 統一由 OLTP.java 處理檔案寫入
echo   6. ✅ 保持架構一致性
echo.
echo 架構改進說明：
echo   📁 檔案寫入統一處理：
echo      - HTTP 請求：OLTP.java 處理
echo      - SOAP 請求：OLTP.java 處理（統一）
echo      - QueueListener：只負責訊息傳遞
echo   🏗️ 責任分離：
echo      - QueueListener：訊息佇列處理
echo      - OLTP.java：業務邏輯和檔案寫入
echo   🔧 維護性：
echo      - 單一檔案寫入邏輯
echo      - 減少重複代碼
echo      - 統一的錯誤處理
echo.
echo 下一步：
echo 1. 重啟應用程式
echo 2. 重新測試 SOAP 回應處理
echo 3. 檢查 XML 結構驗證是否正確工作
echo 4. 確認檔案寫入由 OLTP.java 統一處理
echo 5. 驗證命名空間移除和結構驗證的完整流程
echo.
goto :end

:error
echo.
echo ❌ XML 結構驗證修復測試失敗
echo 請檢查編譯錯誤並修正問題
echo.
exit /b 1

:end
echo 如需協助，請檢查 Console 輸出中的詳細測試結果
if exist "TestXMLValidationFix.java" del "TestXMLValidationFix.java"
if exist "TestXMLValidationFix.class" del "TestXMLValidationFix.class"
pause
