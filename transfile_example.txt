# transfile.txt 範例配置檔案
# 新格式：tocode,subProcess,target,timeout,maintainStat,maintainStart,maintainEnd,soapaction
#
# 欄位說明：
# tocode        - 交易代碼
# subProcess    - 子程序名稱 (HTTP_SubProcess, SOAP_SubProcess 等)
# target        - 目標 URL
# timeout       - 逾時時間（秒）
# maintainStat  - 維護狀態 (0=正常, 1=維護中)
# maintainStart - 維護開始時間 (yyyyMMddHHmm)
# maintainEnd   - 維護結束時間 (yyyyMMddHHmm)
# soapaction    - SOAP Action 標頭值（僅用於 SOAP_SubProcess）

# SOAP 服務配置範例
MANAGE_TERMINAL,SOAP_SubProcess,https://stage-posapi2.tixpress.tw/POSProxyService.svc,30,0,,,http://ticketxpress.com.tw/IPOSProxy/ManageTerminal
PROCESS_PAYMENT,SOAP_SubProcess,https://stage-posapi2.tixpress.tw/POSProxyService.svc,30,0,,,http://ticketxpress.com.tw/IPOSProxy/ProcessPayment
QUERY_TRANSACTION,SOAP_SubProcess,https://stage-posapi2.tixpress.tw/POSProxyService.svc,30,0,,,http://ticketxpress.com.tw/IPOSProxy/QueryTransaction
CANCEL_TRANSACTION,SOAP_SubProcess,https://stage-posapi2.tixpress.tw/POSProxyService.svc,30,0,,,http://ticketxpress.com.tw/IPOSProxy/CancelTransaction

# 帶維護時間的 SOAP 服務配置
MANAGE_TERMINAL_MAINT,SOAP_SubProcess,https://stage-posapi2.tixpress.tw/POSProxyService.svc,30,1,202412250200,202412250400,http://ticketxpress.com.tw/IPOSProxy/ManageTerminal

# HTTP 服務配置（向後相容，不需要 SOAPAction）
HTTP_SERVICE_1,HTTP_SubProcess,http://api.example.com/service1,15,0,,
HTTP_SERVICE_2,HTTP_SubProcess,http://api.example.com/service2,20,0,,

# 舊格式配置（向後相容，SOAPAction 將使用預設值）
OLD_FORMAT_SOAP,SOAP_SubProcess,https://legacy.soap.service.com/endpoint,25,0

# 測試用配置
TEST_SOAP_LOCAL,SOAP_SubProcess,http://localhost:8080/soap/test,10,0,,,http://tempuri.org/TestService
TEST_SOAP_EMPTY_ACTION,SOAP_SubProcess,https://test.soap.service.com/endpoint,15,0,,,

# 注意事項：
# 1. 如果 SOAPAction 欄位為空，系統會根據 URL 和請求內容自動推斷
# 2. 舊格式的配置行（少於8個欄位）仍然支援，SOAPAction 會使用預設值
# 3. 維護時間格式：yyyyMMddHHmm (例如：202412250200 表示 2024年12月25日02:00)
# 4. HTTP 服務不需要 SOAPAction 欄位，可以省略或留空
# 5. 以 # 開頭的行為註解，會被忽略
