@echo off
echo ===== TERMINO 值獲取修復測試 =====
echo.
echo 測試目標：修復 getTerminoValue 方法無法正確獲取 TERMINO 值的問題
echo 問題：回覆電文的 TERMINO 應該與原始請求中的 TERMINO 完全相同
echo 修復內容：
echo   1. 添加原始請求 TERMINO 的存儲和傳遞
echo   2. 修復 getTerminoValue 方法優先使用原始請求 TERMINO
echo   3. 修復 getResponseTerminoValue 方法確保一致性
echo   4. 修復 buildOLTPHeader 方法中的 TERMINO 處理邏輯
echo   5. 添加詳細的調試資訊和錯誤處理
echo ===============================================
echo.

REM 設定 CLASSPATH
set CLASSPATH=libs\*;bin

echo 1. 檢查必要目錄...
if not exist "bin" mkdir bin
if not exist "bin\com\pic\o2o\common" mkdir bin\com\pic\o2o\common

echo.
echo 2. 編譯修復後的程式碼...

echo 編譯 QueueListener.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\QueueListener.java
if %ERRORLEVEL% neq 0 (
    echo ❌ QueueListener.java 編譯失敗
    echo 可能是 TERMINO 修復代碼有問題
    goto :error
) else (
    echo ✅ QueueListener.java 編譯成功
)

echo.
echo 3. 創建 TERMINO 修復測試程式...

REM 創建測試程式
echo import com.pic.o2o.common.QueueListener; > TestTerminoFix.java
echo import org.dom4j.Element; >> TestTerminoFix.java
echo import org.dom4j.DocumentHelper; >> TestTerminoFix.java
echo import java.lang.reflect.Method; >> TestTerminoFix.java
echo import java.lang.reflect.Field; >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo public class TestTerminoFix { >> TestTerminoFix.java
echo     public static void main(String[] args) { >> TestTerminoFix.java
echo         try { >> TestTerminoFix.java
echo             System.out.println("=== TERMINO 值獲取修復測試 ==="); >> TestTerminoFix.java
echo             System.out.println(); >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo             // 創建 QueueListener 實例 >> TestTerminoFix.java
echo             QueueListener listener = new QueueListener(); >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo             // 模擬原始請求 XML >> TestTerminoFix.java
echo             String originalRequestXml = "^<OLTP^>" + >> TestTerminoFix.java
echo                                         "^<HEADER^>" + >> TestTerminoFix.java
echo                                         "^<VER^>01.01^</VER^>" + >> TestTerminoFix.java
echo                                         "^<FROM^>BU00100001^</FROM^>" + >> TestTerminoFix.java
echo                                         "^<TERMINO^>20250722972549010000011637^</TERMINO^>" + >> TestTerminoFix.java
echo                                         "^<TO^>BU01600010^</TO^>" + >> TestTerminoFix.java
echo                                         "^<BUSINESS^>0160100^</BUSINESS^>" + >> TestTerminoFix.java
echo                                         "^<DATE^>20250722^</DATE^>" + >> TestTerminoFix.java
echo                                         "^<TIME^>163720^</TIME^>" + >> TestTerminoFix.java
echo                                         "^</HEADER^>" + >> TestTerminoFix.java
echo                                         "^<AP^>^<ManageTerminalRequest^>...^</ManageTerminalRequest^>^</AP^>" + >> TestTerminoFix.java
echo                                         "^</OLTP^>"; >> TestTerminoFix.java
echo             Element originalElement = DocumentHelper.parseText(originalRequestXml).getRootElement(); >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo             // 模擬設定原始請求資訊 >> TestTerminoFix.java
echo             System.out.println("=== 模擬設定原始請求資訊 ==="); >> TestTerminoFix.java
echo             setOriginalRequestInfo(listener, originalElement); >> TestTerminoFix.java
echo             System.out.println(); >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo             // 測試案例 1：SOAP 回應（沒有 HEADER） >> TestTerminoFix.java
echo             System.out.println("=== 測試案例 1：SOAP 回應（沒有 HEADER）==="); >> TestTerminoFix.java
echo             String soapResponseXml = "^<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\"^>" + >> TestTerminoFix.java
echo                                      "^<s:Body^>" + >> TestTerminoFix.java
echo                                      "^<ManageTerminalResponse^>" + >> TestTerminoFix.java
echo                                      "^<ManageTerminalResult^>" + >> TestTerminoFix.java
echo                                      "^<ResponseCode^>0000^</ResponseCode^>" + >> TestTerminoFix.java
echo                                      "^<Message^>Success^</Message^>" + >> TestTerminoFix.java
echo                                      "^</ManageTerminalResult^>" + >> TestTerminoFix.java
echo                                      "^</ManageTerminalResponse^>" + >> TestTerminoFix.java
echo                                      "^</s:Body^>" + >> TestTerminoFix.java
echo                                      "^</s:Envelope^>"; >> TestTerminoFix.java
echo             Element soapElement = DocumentHelper.parseText(soapResponseXml).getRootElement(); >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo             testGetTerminoValue(listener, soapElement, "SOAP 回應"); >> TestTerminoFix.java
echo             testGetResponseTerminoValue(listener, soapElement, "SOAP 回應"); >> TestTerminoFix.java
echo             testBuildOLTPHeader(listener, soapElement, true, "SOAP 回應"); >> TestTerminoFix.java
echo             System.out.println(); >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo             // 測試案例 2：HTTP 回應（有 HEADER） >> TestTerminoFix.java
echo             System.out.println("=== 測試案例 2：HTTP 回應（有 HEADER）==="); >> TestTerminoFix.java
echo             String httpResponseXml = "^<OLTP^>" + >> TestTerminoFix.java
echo                                      "^<HEADER^>" + >> TestTerminoFix.java
echo                                      "^<VER^>01.01^</VER^>" + >> TestTerminoFix.java
echo                                      "^<FROM^>BU01600010^</FROM^>" + >> TestTerminoFix.java
echo                                      "^<TERMINO^>20250722972549010000011637^</TERMINO^>" + >> TestTerminoFix.java
echo                                      "^<TO^>BU00100001^</TO^>" + >> TestTerminoFix.java
echo                                      "^<BUSINESS^>0160100^</BUSINESS^>" + >> TestTerminoFix.java
echo                                      "^<STATCODE^>0000^</STATCODE^>" + >> TestTerminoFix.java
echo                                      "^<STATDESC^>^</STATDESC^>" + >> TestTerminoFix.java
echo                                      "^</HEADER^>" + >> TestTerminoFix.java
echo                                      "^<AP^>^<ProcessResult^>...^</ProcessResult^>^</AP^>" + >> TestTerminoFix.java
echo                                      "^</OLTP^>"; >> TestTerminoFix.java
echo             Element httpElement = DocumentHelper.parseText(httpResponseXml).getRootElement(); >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo             testGetTerminoValue(listener, httpElement, "HTTP 回應"); >> TestTerminoFix.java
echo             testGetResponseTerminoValue(listener, httpElement, "HTTP 回應"); >> TestTerminoFix.java
echo             testBuildOLTPHeader(listener, httpElement, false, "HTTP 回應"); >> TestTerminoFix.java
echo             System.out.println(); >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo             // 測試案例 3：測試沒有原始請求資訊的情況 >> TestTerminoFix.java
echo             System.out.println("=== 測試案例 3：沒有原始請求資訊 ==="); >> TestTerminoFix.java
echo             QueueListener listenerNoOriginal = new QueueListener(); >> TestTerminoFix.java
echo             testGetTerminoValue(listenerNoOriginal, soapElement, "無原始請求的 SOAP"); >> TestTerminoFix.java
echo             testGetResponseTerminoValue(listenerNoOriginal, soapElement, "無原始請求的 SOAP"); >> TestTerminoFix.java
echo             System.out.println(); >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo             System.out.println("=== 測試完成 ==="); >> TestTerminoFix.java
echo             System.out.println("✅ TERMINO 值獲取修復測試完成"); >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo         } catch (Exception e) { >> TestTerminoFix.java
echo             System.out.println("❌ 測試失敗: " + e.getMessage()); >> TestTerminoFix.java
echo             e.printStackTrace(); >> TestTerminoFix.java
echo         } >> TestTerminoFix.java
echo     } >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo     private static void setOriginalRequestInfo(QueueListener listener, Element originalElement) { >> TestTerminoFix.java
echo         try { >> TestTerminoFix.java
echo             // 設定 originalTermino >> TestTerminoFix.java
echo             Field originalTerminoField = QueueListener.class.getDeclaredField("originalTermino"); >> TestTerminoFix.java
echo             originalTerminoField.setAccessible(true); >> TestTerminoFix.java
echo             String termino = originalElement.element("HEADER").elementText("TERMINO"); >> TestTerminoFix.java
echo             originalTerminoField.set(listener, termino); >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo             // 設定 originalRequestHeader >> TestTerminoFix.java
echo             Field originalHeaderField = QueueListener.class.getDeclaredField("originalRequestHeader"); >> TestTerminoFix.java
echo             originalHeaderField.setAccessible(true); >> TestTerminoFix.java
echo             Element headerCopy = originalElement.element("HEADER").createCopy(); >> TestTerminoFix.java
echo             originalHeaderField.set(listener, headerCopy); >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo             System.out.println("✅ 原始請求資訊設定完成"); >> TestTerminoFix.java
echo             System.out.println("原始 TERMINO: " + termino); >> TestTerminoFix.java
echo             System.out.println("原始 FROM: " + originalElement.element("HEADER").elementText("FROM")); >> TestTerminoFix.java
echo             System.out.println("原始 TO: " + originalElement.element("HEADER").elementText("TO")); >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo         } catch (Exception e) { >> TestTerminoFix.java
echo             System.out.println("❌ 設定原始請求資訊失敗: " + e.getMessage()); >> TestTerminoFix.java
echo         } >> TestTerminoFix.java
echo     } >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo     private static void testGetTerminoValue(QueueListener listener, Element element, String type) { >> TestTerminoFix.java
echo         try { >> TestTerminoFix.java
echo             Method method = QueueListener.class.getDeclaredMethod("getTerminoValue", Element.class); >> TestTerminoFix.java
echo             method.setAccessible(true); >> TestTerminoFix.java
echo             String result = (String) method.invoke(listener, element); >> TestTerminoFix.java
echo             System.out.println(type + " getTerminoValue 結果: " + result); >> TestTerminoFix.java
echo             System.out.println("預期結果: 20250722972549010000011637"); >> TestTerminoFix.java
echo             boolean isCorrect = "20250722972549010000011637".equals(result); >> TestTerminoFix.java
echo             System.out.println("結果正確: " + isCorrect); >> TestTerminoFix.java
echo         } catch (Exception e) { >> TestTerminoFix.java
echo             System.out.println("❌ " + type + " getTerminoValue 測試失敗: " + e.getMessage()); >> TestTerminoFix.java
echo         } >> TestTerminoFix.java
echo     } >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo     private static void testGetResponseTerminoValue(QueueListener listener, Element element, String type) { >> TestTerminoFix.java
echo         try { >> TestTerminoFix.java
echo             Method method = QueueListener.class.getDeclaredMethod("getResponseTerminoValue", Element.class); >> TestTerminoFix.java
echo             method.setAccessible(true); >> TestTerminoFix.java
echo             String result = (String) method.invoke(listener, element); >> TestTerminoFix.java
echo             System.out.println(type + " getResponseTerminoValue 結果: " + result); >> TestTerminoFix.java
echo             System.out.println("預期結果: 20250722972549010000011637"); >> TestTerminoFix.java
echo             boolean isCorrect = "20250722972549010000011637".equals(result); >> TestTerminoFix.java
echo             System.out.println("結果正確: " + isCorrect); >> TestTerminoFix.java
echo         } catch (Exception e) { >> TestTerminoFix.java
echo             System.out.println("❌ " + type + " getResponseTerminoValue 測試失敗: " + e.getMessage()); >> TestTerminoFix.java
echo         } >> TestTerminoFix.java
echo     } >> TestTerminoFix.java
echo. >> TestTerminoFix.java
echo     private static void testBuildOLTPHeader(QueueListener listener, Element element, boolean isSOAP, String type) { >> TestTerminoFix.java
echo         try { >> TestTerminoFix.java
echo             Method method = QueueListener.class.getDeclaredMethod("buildOLTPHeader", Element.class, boolean.class); >> TestTerminoFix.java
echo             method.setAccessible(true); >> TestTerminoFix.java
echo             String result = (String) method.invoke(listener, element, isSOAP); >> TestTerminoFix.java
echo             System.out.println(type + " buildOLTPHeader 結果長度: " + (result != null ? result.length() : 0)); >> TestTerminoFix.java
echo             if (result != null) { >> TestTerminoFix.java
echo                 boolean containsCorrectTermino = result.contains("20250722972549010000011637"); >> TestTerminoFix.java
echo                 System.out.println("包含正確的 TERMINO: " + containsCorrectTermino); >> TestTerminoFix.java
echo                 boolean containsFromTo = result.contains("ns0:FROM") ^&^& result.contains("ns0:TO"); >> TestTerminoFix.java
echo                 System.out.println("包含 FROM/TO 欄位: " + containsFromTo); >> TestTerminoFix.java
echo             } >> TestTerminoFix.java
echo         } catch (Exception e) { >> TestTerminoFix.java
echo             System.out.println("❌ " + type + " buildOLTPHeader 測試失敗: " + e.getMessage()); >> TestTerminoFix.java
echo         } >> TestTerminoFix.java
echo     } >> TestTerminoFix.java
echo } >> TestTerminoFix.java

echo 編譯測試程式...
javac -cp "%CLASSPATH%" TestTerminoFix.java
if %ERRORLEVEL% equ 0 (
    echo.
    echo 4. 執行 TERMINO 修復測試...
    echo.
    java -cp "%CLASSPATH%;." TestTerminoFix
) else (
    echo ⚠️ 測試程式編譯失敗，跳過測試
)

echo.
echo 5. 修復前後對比分析...
echo.
echo 修復前的問題：
echo   ❌ getTerminoValue 返回 null 或預設值
echo   ❌ 無法從原始請求中獲取實際 TERMINO 值
echo   ❌ 回覆電文的 TERMINO 與原始請求不一致
echo   ❌ 缺乏原始請求上下文的存儲和傳遞
echo   ❌ 錯誤地生成新的 TERMINO 值
echo.
echo 修復後的改進：
echo   ✅ 在 onMessage 中存儲原始請求的 TERMINO 和 HEADER
echo   ✅ getTerminoValue 優先使用存儲的原始 TERMINO
echo   ✅ getResponseTerminoValue 確保與原始請求一致
echo   ✅ buildOLTPHeader 使用原始請求資訊進行 FROM/TO 互換
echo   ✅ 詳細的調試資訊顯示 TERMINO 獲取過程
echo   ✅ 完整的錯誤處理和 fallback 機制
echo.

echo 6. 預期效果...
echo.
echo 修復後應該看到：
echo   ✅ 原始請求資訊設定完成
echo   ✅ 原始 TERMINO: 20250722972549010000011637
echo   ✅ SOAP 回應 getTerminoValue 結果: 20250722972549010000011637
echo   ✅ HTTP 回應 getTerminoValue 結果: 20250722972549010000011637
echo   ✅ getResponseTerminoValue 結果: 20250722972549010000011637
echo   ✅ buildOLTPHeader 包含正確的 TERMINO: true
echo   ✅ 結果正確: true
echo.
echo 不應該再看到：
echo   ❌ getTerminoValue 結果: null
echo   ❌ getTerminoValue 結果: DEFAULT_REPLY_QUEUE
echo   ❌ 生成的時間戳 TERMINO 值
echo   ❌ 結果正確: false
echo.

echo ===============================================
echo.
echo ✅ TERMINO 值獲取修復完成
echo.
echo 主要修復項目：
echo   1. ✅ 添加 originalTermino 和 originalRequestHeader 實例變數
echo   2. ✅ 在 onMessage 中存儲原始請求資訊
echo   3. ✅ 修復 getTerminoValue 優先使用原始 TERMINO
echo   4. ✅ 修復 getResponseTerminoValue 確保一致性
echo   5. ✅ 修復 buildOLTPHeader 使用原始請求資訊
echo   6. ✅ 添加詳細的調試資訊和錯誤處理
echo.
echo 下一步：
echo 1. 重啟應用程式
echo 2. 重新測試 SOAP 和 HTTP 請求
echo 3. 檢查回覆訊息中的 TERMINO 是否與原始請求一致
echo 4. 驗證 FROM/TO 欄位是否正確互換
echo 5. 確認不再生成新的 TERMINO 值
echo.
goto :end

:error
echo.
echo ❌ TERMINO 修復測試失敗
echo 請檢查編譯錯誤並修正問題
echo.
exit /b 1

:end
echo 如需協助，請檢查 Console 輸出中的詳細調試資訊
if exist "TestTerminoFix.java" del "TestTerminoFix.java"
if exist "TestTerminoFix.class" del "TestTerminoFix.class"
pause
