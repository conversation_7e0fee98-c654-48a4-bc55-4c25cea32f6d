@echo off
echo ===== SOAPAction 配置測試 =====
echo.
echo 測試目標：驗證從 transfile.txt 動態讀取 SOAPAction 的功能
echo =======================================
echo.

REM 設定 CLASSPATH
set CLASSPATH=libs\*;bin

echo 1. 檢查必要目錄...
if not exist "bin" mkdir bin
if not exist "bin\com\pic\oltp" mkdir bin\com\pic\oltp
if not exist "bin\com\pic\o2o\common" mkdir bin\com\pic\o2o\common

echo.
echo 2. 編譯修改後的程式碼...

echo 編譯 GlobalVariable.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\oltp\GlobalVariable.java
if %ERRORLEVEL% neq 0 (
    echo ❌ GlobalVariable.java 編譯失敗
    goto :error
) else (
    echo ✅ GlobalVariable.java 編譯成功
)

echo 編譯 OLTP.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\oltp\OLTP.java
if %ERRORLEVEL% neq 0 (
    echo ❌ OLTP.java 編譯失敗
    echo 可能是 SOAPAction 相關的修改有問題
    goto :error
) else (
    echo ✅ OLTP.java 編譯成功
)

echo 編譯 SOAPService.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\SOAPService.java
if %ERRORLEVEL% neq 0 (
    echo ❌ SOAPService.java 編譯失敗
    goto :error
) else (
    echo ✅ SOAPService.java 編譯成功
)

echo.
echo 3. 檢查 transfile.txt 配置...

if exist "transfile.txt" (
    echo ✅ 找到 transfile.txt
    echo.
    echo 當前配置內容：
    type transfile.txt
    echo.
) else (
    echo ⚠️ 未找到 transfile.txt，使用範例配置
    copy transfile_example.txt transfile.txt
    echo ✅ 已創建範例 transfile.txt
)

echo.
echo 4. 分析 transfile.txt 格式...
echo.

REM 使用 PowerShell 分析配置檔案
powershell -Command "Get-Content transfile.txt | Where-Object { $_ -notmatch '^#' -and $_ -ne '' } | ForEach-Object { $fields = $_.Split(','); Write-Host ('配置行: ' + $_); Write-Host ('  欄位數量: ' + $fields.Length); if ($fields.Length -ge 8) { Write-Host ('  SOAPAction: ' + $fields[7]) } else { Write-Host ('  SOAPAction: 未設定（將使用預設值）') }; Write-Host '' }"

echo.
echo 5. 驗證 SOAPAction 推斷邏輯...
echo.

REM 創建測試程式
echo import com.pic.oltp.OLTP; > TestSOAPAction.java
echo import java.lang.reflect.Method; >> TestSOAPAction.java
echo. >> TestSOAPAction.java
echo public class TestSOAPAction { >> TestSOAPAction.java
echo     public static void main(String[] args) { >> TestSOAPAction.java
echo         try { >> TestSOAPAction.java
echo             OLTP oltp = new OLTP(); >> TestSOAPAction.java
echo             Method method = OLTP.class.getDeclaredMethod("inferDefaultSOAPAction", String.class, String.class); >> TestSOAPAction.java
echo             method.setAccessible(true); >> TestSOAPAction.java
echo. >> TestSOAPAction.java
echo             // 測試不同的 URL 和內容組合 >> TestSOAPAction.java
echo             String[][] testCases = { >> TestSOAPAction.java
echo                 {"https://stage-posapi2.tixpress.tw/POSProxyService.svc", "^<ManageTerminal^>^<manageTerminalRequest^>^</manageTerminalRequest^>^</ManageTerminal^>"}, >> TestSOAPAction.java
echo                 {"https://stage-posapi2.tixpress.tw/POSProxyService.svc", "^<ProcessPayment^>^<processPaymentRequest^>^</processPaymentRequest^>^</ProcessPayment^>"}, >> TestSOAPAction.java
echo                 {"https://other.service.com/soap", "^<TestOperation^>^<request^>^</request^>^</TestOperation^>"}, >> TestSOAPAction.java
echo                 {"https://unknown.service.com/endpoint", null} >> TestSOAPAction.java
echo             }; >> TestSOAPAction.java
echo. >> TestSOAPAction.java
echo             System.out.println("=== SOAPAction 推斷測試 ==="); >> TestSOAPAction.java
echo             for (String[] testCase : testCases) { >> TestSOAPAction.java
echo                 String url = testCase[0]; >> TestSOAPAction.java
echo                 String content = testCase[1]; >> TestSOAPAction.java
echo                 String result = (String) method.invoke(oltp, url, content); >> TestSOAPAction.java
echo                 System.out.println("URL: " + url); >> TestSOAPAction.java
echo                 System.out.println("內容: " + (content != null ? content.substring(0, Math.min(50, content.length())) + "..." : "null")); >> TestSOAPAction.java
echo                 System.out.println("推斷的 SOAPAction: " + result); >> TestSOAPAction.java
echo                 System.out.println(""); >> TestSOAPAction.java
echo             } >> TestSOAPAction.java
echo         } catch (Exception e) { >> TestSOAPAction.java
echo             e.printStackTrace(); >> TestSOAPAction.java
echo         } >> TestSOAPAction.java
echo     } >> TestSOAPAction.java
echo } >> TestSOAPAction.java

echo 編譯測試程式...
javac -cp "%CLASSPATH%" TestSOAPAction.java
if %ERRORLEVEL% equ 0 (
    echo 執行 SOAPAction 推斷測試...
    java -cp "%CLASSPATH%;." TestSOAPAction
) else (
    echo ⚠️ 測試程式編譯失敗，跳過推斷測試
)

echo.
echo 6. 配置建議...
echo.
echo 根據您的錯誤（SOAPAction 標頭為空），建議的配置：
echo.
echo 在 transfile.txt 中添加或修改：
echo MANAGE_TERMINAL,SOAP_SubProcess,https://stage-posapi2.tixpress.tw/POSProxyService.svc,23,0,,,http://ticketxpress.com.tw/IPOSProxy/ManageTerminal
echo.
echo 其他常用的 SOAPAction 值：
echo - http://ticketxpress.com.tw/IPOSProxy/ProcessPayment
echo - http://ticketxpress.com.tw/IPOSProxy/QueryTransaction  
echo - http://ticketxpress.com.tw/IPOSProxy/CancelTransaction
echo.

echo =======================================
echo.
echo ✅ SOAPAction 配置功能已實作完成
echo.
echo 主要改進：
echo 1. ✅ 擴展 transfile.txt 格式支援 SOAPAction 欄位
echo 2. ✅ 修改 OLTP.java 解析邏輯讀取第8個欄位
echo 3. ✅ 修改 SOAP_SubProcess 方法接收 SOAPAction 參數
echo 4. ✅ 實作 SOAPAction 推斷邏輯作為備用方案
echo 5. ✅ 保持向後相容性支援舊格式配置
echo.
echo 下一步：
echo 1. 更新您的 transfile.txt 配置檔案
echo 2. 重啟應用程式
echo 3. 測試 SOAP 連線是否正常
echo.
goto :end

:error
echo.
echo ❌ 測試失敗
echo 請檢查編譯錯誤並修正問題
echo.
exit /b 1

:end
echo 如需協助，請參考 transfile_example.txt 範例配置
if exist "TestSOAPAction.java" del "TestSOAPAction.java"
if exist "TestSOAPAction.class" del "TestSOAPAction.class"
pause
