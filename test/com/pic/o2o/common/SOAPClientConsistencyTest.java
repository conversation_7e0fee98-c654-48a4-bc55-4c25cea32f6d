package com.pic.o2o.common;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.pic.oltp.GlobalVariable;

/**
 * SOAPClient 和 SimpleSOAPClient 一致性測試
 * 驗證兩個 SOAP 客戶端產生相同格式的 SOAP 請求
 * 
 * <AUTHOR> OLTP System
 * @version 1.3
 */
public class SOAPClientConsistencyTest {
    
    private SOAPClient soapClient;
    private SimpleSOAPClient simpleSOAPClient;
    private String testServiceUrl = "http://test.soap.service.com/endpoint";
    private int testTimeout = 30;
    
    @Before
    public void setUp() {
        // 初始化測試物件
        soapClient = new SOAPClient(testServiceUrl, testTimeout);
        simpleSOAPClient = new SimpleSOAPClient(testServiceUrl, testTimeout);
        
        // 設定測試環境
        GlobalVariable.HOST = "TEST_HOST";
        GlobalVariable.logTrans = "TEST_LOG";
    }
    
    @Test
    public void testTransformToSOAPFormat_Consistency() throws Exception {
        // 準備測試資料
        String originalXML = "<ManageTerminal>" +
                "<manageTerminalRequest>" +
                "<Channel>Test</Channel>" +
                "<Checksum>B43DB40CD926E971464816D781CFF69B</Checksum>" +
                "<ManageTerminalDateTime>20151015105959</ManageTerminalDateTime>" +
                "<ManageType>101</ManageType>" +
                "<MerchantCode>000000000000038</MerchantCode>" +
                "<ProgramCode>00001</ProgramCode>" +
                "<ShopCode>0000001028</ShopCode>" +
                "<TerminalCode></TerminalCode>" +
                "<TerminalSSN>20151015105959000001</TerminalSSN>" +
                "</manageTerminalRequest>" +
                "</ManageTerminal>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 使用反射測試 SOAPClient 的轉換方法
            java.lang.reflect.Method soapClientMethod = SOAPClient.class.getDeclaredMethod("transformToSOAPFormat", String.class);
            soapClientMethod.setAccessible(true);
            String soapClientResult = (String) soapClientMethod.invoke(soapClient, originalXML);
            
            // 使用反射測試 SimpleSOAPClient 的轉換方法
            java.lang.reflect.Method simpleSOAPClientMethod = SimpleSOAPClient.class.getDeclaredMethod("transformToSOAPFormat", String.class);
            simpleSOAPClientMethod.setAccessible(true);
            String simpleSOAPClientResult = (String) simpleSOAPClientMethod.invoke(simpleSOAPClient, originalXML);
            
            // 驗證兩個結果應該相同
            assertNotNull("SOAPClient 轉換結果不應為 null", soapClientResult);
            assertNotNull("SimpleSOAPClient 轉換結果不應為 null", simpleSOAPClientResult);
            assertEquals("兩個 SOAP 客戶端應該產生相同的轉換結果", simpleSOAPClientResult, soapClientResult);
            
            // 驗證轉換結果的結構
            assertTrue("應包含 m:ManageTerminal", soapClientResult.contains("<m:ManageTerminal"));
            assertTrue("應包含正確的命名空間", soapClientResult.contains("xmlns:m=\"http://ticketxpress.com.tw/\""));
            assertTrue("應包含 m0:Channel", soapClientResult.contains("<m0:Channel>"));
            assertTrue("應包含 m0:Checksum", soapClientResult.contains("<m0:Checksum>"));
            
            System.out.println("SOAPClient 轉換結果:");
            System.out.println(soapClientResult);
            System.out.println("\nSimpleSOAPClient 轉換結果:");
            System.out.println(simpleSOAPClientResult);
        }
    }
    
    @Test
    public void testExtractRootElementName_Consistency() throws Exception {
        String testXML = "<ManageTerminal attr=\"value\"><test/></ManageTerminal>";
        
        // 測試 SOAPClient 的方法
        java.lang.reflect.Method soapClientMethod = SOAPClient.class.getDeclaredMethod("extractRootElementName", String.class);
        soapClientMethod.setAccessible(true);
        String soapClientResult = (String) soapClientMethod.invoke(soapClient, testXML);
        
        // 測試 SimpleSOAPClient 的方法
        java.lang.reflect.Method simpleSOAPClientMethod = SimpleSOAPClient.class.getDeclaredMethod("extractRootElementName", String.class);
        simpleSOAPClientMethod.setAccessible(true);
        String simpleSOAPClientResult = (String) simpleSOAPClientMethod.invoke(simpleSOAPClient, testXML);
        
        // 驗證結果一致性
        assertEquals("兩個客戶端應該提取相同的根元素名稱", simpleSOAPClientResult, soapClientResult);
        assertEquals("應該正確提取根元素名稱", "ManageTerminal", soapClientResult);
    }
    
    @Test
    public void testTransformInnerElements_Consistency() throws Exception {
        String innerContent = "<Channel>Test</Channel><Checksum>ABC123</Checksum><NestedElement><SubElement>Value</SubElement></NestedElement>";
        
        // 測試 SOAPClient 的方法
        java.lang.reflect.Method soapClientMethod = SOAPClient.class.getDeclaredMethod("transformInnerElements", String.class);
        soapClientMethod.setAccessible(true);
        String soapClientResult = (String) soapClientMethod.invoke(soapClient, innerContent);
        
        // 測試 SimpleSOAPClient 的方法
        java.lang.reflect.Method simpleSOAPClientMethod = SimpleSOAPClient.class.getDeclaredMethod("transformInnerElements", String.class);
        simpleSOAPClientMethod.setAccessible(true);
        String simpleSOAPClientResult = (String) simpleSOAPClientMethod.invoke(simpleSOAPClient, innerContent);
        
        // 驗證結果一致性
        assertEquals("兩個客戶端應該產生相同的內部元素轉換", simpleSOAPClientResult, soapClientResult);
        
        // 驗證轉換正確性
        assertTrue("應該添加 m0 前綴到所有元素", soapClientResult.contains("<m0:Channel>"));
        assertTrue("應該添加 m0 前綴到結束標籤", soapClientResult.contains("</m0:Channel>"));
        assertTrue("應該處理嵌套元素", soapClientResult.contains("<m0:NestedElement>"));
        assertTrue("應該處理子元素", soapClientResult.contains("<m0:SubElement>"));
        
        System.out.println("轉換前: " + innerContent);
        System.out.println("轉換後: " + soapClientResult);
    }
    
    @Test
    public void testCreateSOAPEnvelope_vs_CreateSOAPRequest() throws Exception {
        String originalXML = "<ManageTerminal>" +
                "<manageTerminalRequest>" +
                "<Channel>Test</Channel>" +
                "<Checksum>B43DB40CD926E971464816D781CFF69B</Checksum>" +
                "</manageTerminalRequest>" +
                "</ManageTerminal>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 測試 SimpleSOAPClient 的 createSOAPEnvelope 方法
            simpleSOAPClient.setRequestContent(originalXML);
            java.lang.reflect.Method simpleMethod = SimpleSOAPClient.class.getDeclaredMethod("createSOAPEnvelope", String.class);
            simpleMethod.setAccessible(true);
            String simpleResult = (String) simpleMethod.invoke(simpleSOAPClient, originalXML);
            
            // 驗證 SimpleSOAPClient 的結果包含標準 SOAP 結構
            assertTrue("應包含 SOAP-ENV:Envelope", simpleResult.contains("<SOAP-ENV:Envelope"));
            assertTrue("應包含正確的命名空間", simpleResult.contains("xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\""));
            assertTrue("應包含 m0 命名空間", simpleResult.contains("xmlns:m0=\"http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common\""));
            assertTrue("應包含 SOAP-ENV:Body", simpleResult.contains("<SOAP-ENV:Body>"));
            assertTrue("應包含轉換後的內容", simpleResult.contains("<m:ManageTerminal"));
            assertTrue("應包含 m0 前綴的元素", simpleResult.contains("<m0:Channel>"));
            
            System.out.println("SimpleSOAPClient 完整 SOAP Envelope:");
            System.out.println(formatXML(simpleResult));
            
            // 注意：SOAPClient 的 createSOAPRequest 方法需要 SOAPMessage 物件，
            // 這裡我們主要驗證轉換邏輯的一致性
            System.out.println("\n✅ SOAP 格式轉換邏輯一致性驗證通過");
        }
    }
    
    @Test
    public void testEmptyContent_Consistency() throws Exception {
        // 測試空內容的處理
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 測試空字串
            java.lang.reflect.Method soapClientMethod = SOAPClient.class.getDeclaredMethod("transformToSOAPFormat", String.class);
            soapClientMethod.setAccessible(true);
            String soapClientResult = (String) soapClientMethod.invoke(soapClient, "");
            
            java.lang.reflect.Method simpleSOAPClientMethod = SimpleSOAPClient.class.getDeclaredMethod("transformToSOAPFormat", String.class);
            simpleSOAPClientMethod.setAccessible(true);
            String simpleSOAPClientResult = (String) simpleSOAPClientMethod.invoke(simpleSOAPClient, "");
            
            // 驗證空內容處理的一致性
            assertEquals("空內容處理應該一致", simpleSOAPClientResult, soapClientResult);
            assertTrue("應該使用預設的 request 包裝", soapClientResult.contains("<m:request"));
        }
    }
    
    @Test
    public void testPlainTextContent_Consistency() throws Exception {
        String plainText = "Plain text content for testing";
        
        // 測試純文字內容的處理
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 測試 SimpleSOAPClient 的處理
            java.lang.reflect.Method simpleMethod = SimpleSOAPClient.class.getDeclaredMethod("createSOAPEnvelope", String.class);
            simpleMethod.setAccessible(true);
            String simpleResult = (String) simpleMethod.invoke(simpleSOAPClient, plainText);
            
            // 驗證純文字內容的處理
            assertTrue("應該包含 CDATA 包裝", simpleResult.contains("<![CDATA[" + plainText + "]]>"));
            assertTrue("應該使用 m:request 元素", simpleResult.contains("<m:request"));
            
            System.out.println("純文字內容 SOAP 包裝:");
            System.out.println(formatXML(simpleResult));
        }
    }
    
    /**
     * 格式化 XML 輸出（簡單版本）
     */
    private String formatXML(String xml) {
        return xml.replace("><", ">\n<");
    }
}
