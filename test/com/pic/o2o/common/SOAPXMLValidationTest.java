package com.pic.o2o.common;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.pic.oltp.GlobalVariable;

/**
 * SOAP XML 驗證和命名空間衝突修復測試
 * 
 * <AUTHOR> OLTP System
 * @version 1.3
 */
public class SOAPXMLValidationTest {
    
    private SimpleSOAPClient simpleSOAPClient;
    private SOAPClient soapClient;
    
    @Before
    public void setUp() {
        simpleSOAPClient = new SimpleSOAPClient("http://test.soap.service.com/endpoint", 30);
        soapClient = new SOAPClient("http://test.soap.service.com/endpoint", 30);
        
        GlobalVariable.HOST = "TEST_HOST";
        GlobalVariable.logTrans = "TEST_LOG";
    }
    
    @Test
    public void testNamespaceConflictDetection() {
        // 測試命名空間衝突檢測
        String conflictXML = "<m:ns0:ManageTerminal xmlns:m=\"http://ticketxpress.com.tw/\">" +
                            "<m0:ns0:Channel>Test</m0:Channel>" +
                            "</m:ns0:ManageTerminal>";
        
        assertTrue("應該檢測到命名空間衝突", SOAPXMLValidator.hasNamespaceConflict(conflictXML));
        
        String cleanXML = "<m:ManageTerminal xmlns:m=\"http://ticketxpress.com.tw/\">" +
                         "<m0:Channel>Test</m0:Channel>" +
                         "</m:ManageTerminal>";
        
        assertFalse("乾淨的 XML 不應該有衝突", SOAPXMLValidator.hasNamespaceConflict(cleanXML));
    }
    
    @Test
    public void testNamespaceConflictCleaning() {
        // 測試命名空間衝突清理
        String conflictXML = "<m:ns0:ManageTerminal xmlns:m=\"http://ticketxpress.com.tw/\">" +
                            "<m0:ns0:Channel>Test</m0:ns0:Channel>" +
                            "<m0:ns1:Checksum>ABC123</m0:ns1:Checksum>" +
                            "</m:ns0:ManageTerminal>";
        
        String cleanedXML = SOAPXMLValidator.cleanNamespaceConflicts(conflictXML);
        
        // 驗證清理結果
        assertFalse("清理後不應該有 ns0 衝突", cleanedXML.contains("m:ns0:"));
        assertFalse("清理後不應該有 ns1 衝突", cleanedXML.contains("m0:ns0:"));
        assertFalse("清理後不應該有 ns1 衝突", cleanedXML.contains("m0:ns1:"));
        
        assertTrue("應該保留正確的 m: 前綴", cleanedXML.contains("<m:ManageTerminal"));
        assertTrue("應該保留正確的 m0: 前綴", cleanedXML.contains("<m0:Channel>"));
        assertTrue("應該保留正確的 m0: 前綴", cleanedXML.contains("<m0:Checksum>"));
        
        System.out.println("Original: " + conflictXML);
        System.out.println("Cleaned:  " + cleanedXML);
    }
    
    @Test
    public void testXMLValidation_ValidXML() {
        String validXML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                         "<SOAP-ENV:Envelope xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\">" +
                         "<SOAP-ENV:Body>" +
                         "<m:ManageTerminal xmlns:m=\"http://ticketxpress.com.tw/\">" +
                         "<m0:Channel>Test</m0:Channel>" +
                         "</m:ManageTerminal>" +
                         "</SOAP-ENV:Body>" +
                         "</SOAP-ENV:Envelope>";
        
        SOAPXMLValidator.ValidationResult result = SOAPXMLValidator.validateXML(validXML);
        
        assertTrue("有效的 XML 應該通過驗證", result.isValid());
        assertEquals("驗證訊息應該正確", "XML 格式驗證通過", result.getMessage());
        assertTrue("錯誤列表應該為空", result.getErrors().isEmpty());
    }
    
    @Test
    public void testXMLValidation_InvalidXML() {
        String invalidXML = "<m:ns0:ManageTerminal xmlns:m=\"http://ticketxpress.com.tw/\">" +
                           "<m0:ns0:Channel>Test</m0:ns0:Channel>" +
                           "</m:ns0:ManageTerminal>";
        
        SOAPXMLValidator.ValidationResult result = SOAPXMLValidator.validateXML(invalidXML);
        
        assertFalse("無效的 XML 應該驗證失敗", result.isValid());
        assertFalse("錯誤列表不應該為空", result.getErrors().isEmpty());
        
        // 檢查是否檢測到命名空間衝突
        boolean foundNamespaceError = result.getErrors().stream()
            .anyMatch(error -> error.contains("命名空間前綴衝突"));
        
        assertTrue("應該檢測到命名空間前綴衝突", foundNamespaceError);
    }
    
    @Test
    public void testTransformInnerElements_WithNamespacePrefix() throws Exception {
        // 測試帶有命名空間前綴的內部元素轉換
        String innerContentWithNS = "<ns0:ManageTerminal>" +
                                   "<ns0:manageTerminalRequest>" +
                                   "<ns0:Channel>Test</ns0:Channel>" +
                                   "<ns0:Checksum>B43DB40CD926E971464816D781CFF69B</ns0:Checksum>" +
                                   "</ns0:manageTerminalRequest>" +
                                   "</ns0:ManageTerminal>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 測試 SimpleSOAPClient 的轉換
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("transformInnerElements", String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(simpleSOAPClient, innerContentWithNS);
            
            // 驗證結果
            assertNotNull("轉換結果不應為 null", result);
            assertFalse("不應該包含 ns0: 前綴", result.contains("ns0:"));
            assertTrue("應該包含 m0: 前綴", result.contains("<m0:ManageTerminal>"));
            assertTrue("應該包含 m0: 前綴", result.contains("<m0:Channel>"));
            assertTrue("應該包含 m0: 前綴", result.contains("<m0:Checksum>"));
            
            // 驗證轉換後的 XML 是否有效
            SOAPXMLValidator.ValidationResult validation = SOAPXMLValidator.validateXML(result);
            assertTrue("轉換後的 XML 應該有效", validation.isValid());
            
            System.out.println("Original: " + innerContentWithNS);
            System.out.println("Transformed: " + result);
        }
    }
    
    @Test
    public void testCompleteSOAPTransformation_WithNamespaceConflict() throws Exception {
        // 測試完整的 SOAP 轉換過程，包含命名空間衝突處理
        String originalXML = "<ns0:AP xmlns:ns0=\"http://test.namespace\">" +
                            "<ManageTerminal>" +
                            "<manageTerminalRequest>" +
                            "<Channel>Test</Channel>" +
                            "<Checksum>B43DB40CD926E971464816D781CFF69B</Checksum>" +
                            "</manageTerminalRequest>" +
                            "</ManageTerminal>" +
                            "</ns0:AP>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 測試 SimpleSOAPClient 的完整轉換
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("createSOAPEnvelope", String.class);
            method.setAccessible(true);
            String soapEnvelope = (String) method.invoke(simpleSOAPClient, originalXML);
            
            // 驗證結果
            assertNotNull("SOAP Envelope 不應為 null", soapEnvelope);
            assertTrue("應該包含 SOAP-ENV:Envelope", soapEnvelope.contains("<SOAP-ENV:Envelope"));
            assertTrue("應該包含正確的命名空間", soapEnvelope.contains("xmlns:m0=\"http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common\""));
            
            // 驗證沒有命名空間衝突
            assertFalse("不應該有命名空間衝突", SOAPXMLValidator.hasNamespaceConflict(soapEnvelope));
            
            // 驗證 XML 格式
            SOAPXMLValidator.ValidationResult validation = SOAPXMLValidator.validateXML(soapEnvelope);
            assertTrue("SOAP Envelope 應該有效", validation.isValid());
            
            System.out.println("=== Complete SOAP Transformation Test ===");
            System.out.println("Original XML:");
            System.out.println(originalXML);
            System.out.println("\nGenerated SOAP Envelope:");
            System.out.println(soapEnvelope);
            System.out.println("========================================");
        }
    }
    
    @Test
    public void testMultipleNamespacePrefixes() throws Exception {
        // 測試多個命名空間前綴的處理
        String multiNSContent = "<ns0:Root>" +
                               "<ns1:Element1>Value1</ns1:Element1>" +
                               "<ns2:Element2>Value2</ns2:Element2>" +
                               "<ns0:Element3>Value3</ns0:Element3>" +
                               "</ns0:Root>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 測試轉換
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("transformInnerElements", String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(simpleSOAPClient, multiNSContent);
            
            // 驗證所有舊的命名空間前綴都被移除
            assertFalse("不應該包含 ns0:", result.contains("ns0:"));
            assertFalse("不應該包含 ns1:", result.contains("ns1:"));
            assertFalse("不應該包含 ns2:", result.contains("ns2:"));
            
            // 驗證新的前綴被正確添加
            assertTrue("應該包含 m0:Root", result.contains("<m0:Root>"));
            assertTrue("應該包含 m0:Element1", result.contains("<m0:Element1>"));
            assertTrue("應該包含 m0:Element2", result.contains("<m0:Element2>"));
            assertTrue("應該包含 m0:Element3", result.contains("<m0:Element3>"));
            
            // 驗證內容保持不變
            assertTrue("內容應該保持不變", result.contains("Value1"));
            assertTrue("內容應該保持不變", result.contains("Value2"));
            assertTrue("內容應該保持不變", result.contains("Value3"));
            
            System.out.println("Multi-namespace input: " + multiNSContent);
            System.out.println("Transformed output: " + result);
        }
    }
}
