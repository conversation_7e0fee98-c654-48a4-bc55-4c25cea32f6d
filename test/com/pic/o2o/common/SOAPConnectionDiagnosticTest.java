package com.pic.o2o.common;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.pic.oltp.GlobalVariable;

/**
 * SOAP 連線診斷測試
 * 驗證診斷工具的功能和錯誤處理
 * 
 * <AUTHOR> OLTP System
 * @version 1.3
 */
public class SOAPConnectionDiagnosticTest {
    
    @Before
    public void setUp() {
        GlobalVariable.HOST = "TEST_HOST";
        GlobalVariable.logTrans = "TEST_LOG";
    }
    
    @Test
    public void testUrlFormatValidation() {
        // 測試 URL 格式驗證
        
        // 測試有效的 URL
        SOAPConnectionDiagnostic.DiagnosticResult result1 = 
            SOAPConnectionDiagnostic.diagnose("http://valid.service.com/endpoint", 30, null);
        
        // URL 格式檢查應該通過（即使後續連線可能失敗）
        assertTrue("有效 URL 的格式檢查應該通過", 
                  result1.getDetails().stream().anyMatch(d -> d.contains("URL 格式正確")));
        
        // 測試無效的 URL
        SOAPConnectionDiagnostic.DiagnosticResult result2 = 
            SOAPConnectionDiagnostic.diagnose("invalid-url", 30, null);
        
        assertFalse("無效 URL 應該失敗", result2.isSuccess());
        assertTrue("應該包含 URL 格式錯誤訊息", 
                  result2.getSummary().contains("URL 格式錯誤"));
        
        // 測試空 URL
        SOAPConnectionDiagnostic.DiagnosticResult result3 = 
            SOAPConnectionDiagnostic.diagnose("", 30, null);
        
        assertFalse("空 URL 應該失敗", result3.isSuccess());
        assertEquals("應該顯示 URL 為空", "URL 為空", result3.getSummary());
    }
    
    @Test
    public void testDnsResolutionCheck() {
        // 測試 DNS 解析檢查
        
        // 測試已知的有效主機名稱
        SOAPConnectionDiagnostic.DiagnosticResult result1 = 
            SOAPConnectionDiagnostic.diagnose("http://www.google.com", 30, null);
        
        // DNS 解析應該成功
        assertTrue("Google DNS 解析應該成功", 
                  result1.getDetails().stream().anyMatch(d -> d.contains("DNS 解析成功")));
        
        // 測試無效的主機名稱
        SOAPConnectionDiagnostic.DiagnosticResult result2 = 
            SOAPConnectionDiagnostic.diagnose("http://nonexistent.invalid.domain.test", 30, null);
        
        // DNS 解析應該失敗
        assertTrue("無效主機名稱的 DNS 解析應該失敗", 
                  result2.getSummary().contains("DNS 解析失敗"));
    }
    
    @Test
    public void testQuickTest() {
        // 測試快速連線測試功能
        
        // 測試有效的服務（Google）
        boolean result1 = SOAPConnectionDiagnostic.quickTest("http://www.google.com", 10);
        // 注意：這可能會成功或失敗，取決於網路環境
        
        // 測試無效的 URL
        boolean result2 = SOAPConnectionDiagnostic.quickTest("invalid-url", 10);
        assertFalse("無效 URL 的快速測試應該失敗", result2);
        
        // 測試無效的主機
        boolean result3 = SOAPConnectionDiagnostic.quickTest("http://nonexistent.invalid.domain.test", 5);
        assertFalse("無效主機的快速測試應該失敗", result3);
    }
    
    @Test
    public void testDiagnosticResultToString() {
        // 測試診斷結果的字串輸出格式
        
        SOAPConnectionDiagnostic.DiagnosticResult result = 
            new SOAPConnectionDiagnostic.DiagnosticResult();
        
        result.setSuccess(false);
        result.setSummary("測試失敗");
        result.addDetail("詳細資訊 1");
        result.addDetail("詳細資訊 2");
        result.addRecommendation("建議 1");
        result.addRecommendation("建議 2");
        
        String output = result.toString();
        
        assertTrue("應該包含狀態", output.contains("❌ 失敗"));
        assertTrue("應該包含摘要", output.contains("測試失敗"));
        assertTrue("應該包含詳細資訊", output.contains("詳細資訊 1"));
        assertTrue("應該包含建議", output.contains("建議 1"));
        
        System.out.println("診斷結果輸出格式:");
        System.out.println(output);
    }
    
    @Test
    public void testTimeoutHandling() {
        // 測試逾時處理
        
        // 使用很短的逾時時間測試
        long startTime = System.currentTimeMillis();
        SOAPConnectionDiagnostic.DiagnosticResult result = 
            SOAPConnectionDiagnostic.diagnose("http://httpbin.org/delay/10", 1, null);
        long endTime = System.currentTimeMillis();
        
        // 應該在合理時間內完成（不會等待 10 秒）
        assertTrue("診斷應該在合理時間內完成", (endTime - startTime) < 5000);
        
        // 可能會因為逾時而失敗
        if (!result.isSuccess()) {
            assertTrue("逾時錯誤應該被正確識別", 
                      result.getSummary().contains("逾時") || 
                      result.getSummary().contains("timeout"));
        }
    }
    
    @Test
    public void testProtocolValidation() {
        // 測試協議驗證
        
        // 測試不支援的協議
        SOAPConnectionDiagnostic.DiagnosticResult result1 = 
            SOAPConnectionDiagnostic.diagnose("ftp://ftp.example.com", 30, null);
        
        assertFalse("FTP 協議應該不被支援", result1.isSuccess());
        assertTrue("應該顯示不支援的協議錯誤", 
                  result1.getSummary().contains("不支援的協議"));
        
        // 測試 HTTPS 協議
        SOAPConnectionDiagnostic.DiagnosticResult result2 = 
            SOAPConnectionDiagnostic.diagnose("https://www.google.com", 30, null);
        
        // HTTPS 協議應該被接受（格式檢查通過）
        assertTrue("HTTPS 協議應該被支援", 
                  result2.getDetails().stream().anyMatch(d -> d.contains("URL 格式正確")));
    }
    
    @Test
    public void testRecommendationGeneration() {
        // 測試建議生成功能
        
        // 測試各種錯誤情況的建議
        SOAPConnectionDiagnostic.DiagnosticResult result1 = 
            SOAPConnectionDiagnostic.diagnose("", 30, null);
        
        assertFalse("空 URL 測試應該失敗", result1.isSuccess());
        assertFalse("應該有建議", result1.getRecommendations().isEmpty());
        assertTrue("應該建議檢查配置", 
                  result1.getRecommendations().stream()
                         .anyMatch(r -> r.contains("transfile.txt")));
        
        // 測試無效主機的建議
        SOAPConnectionDiagnostic.DiagnosticResult result2 = 
            SOAPConnectionDiagnostic.diagnose("http://nonexistent.invalid.domain.test", 30, null);
        
        if (!result2.isSuccess() && result2.getSummary().contains("DNS")) {
            assertTrue("DNS 錯誤應該有相關建議", 
                      result2.getRecommendations().stream()
                             .anyMatch(r -> r.contains("主機名稱") || r.contains("DNS")));
        }
    }
    
    @Test
    public void testDiagnosticWithMockUtility() {
        // 使用 Mock 測試診斷功能
        
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 執行診斷
            SOAPConnectionDiagnostic.DiagnosticResult result = 
                SOAPConnectionDiagnostic.diagnose("http://test.example.com", 30, null);
            
            // 驗證診斷過程
            assertNotNull("診斷結果不應為 null", result);
            assertNotNull("摘要不應為 null", result.getSummary());
            assertNotNull("詳細資訊不應為 null", result.getDetails());
            assertNotNull("建議不應為 null", result.getRecommendations());
            
            System.out.println("Mock 測試完成，診斷結果:");
            System.out.println(result.toString());
        }
    }
    
    @Test
    public void testErrorMessageClarity() {
        // 測試錯誤訊息的清晰度
        
        // 測試各種錯誤情況
        String[] testUrls = {
            "",                                    // 空 URL
            "invalid-url",                        // 無效格式
            "ftp://ftp.example.com",             // 不支援協議
            "http://nonexistent.invalid.test"    // 無效主機
        };
        
        for (String testUrl : testUrls) {
            SOAPConnectionDiagnostic.DiagnosticResult result = 
                SOAPConnectionDiagnostic.diagnose(testUrl, 30, null);
            
            // 每個錯誤都應該有清楚的摘要
            assertNotNull("錯誤摘要不應為 null: " + testUrl, result.getSummary());
            assertFalse("錯誤摘要不應為空: " + testUrl, result.getSummary().trim().isEmpty());
            
            // 應該有具體的建議
            if (!result.isSuccess()) {
                assertFalse("應該有建議: " + testUrl, result.getRecommendations().isEmpty());
            }
            
            System.out.println("URL: " + testUrl);
            System.out.println("結果: " + result.getSummary());
            System.out.println("建議數量: " + result.getRecommendations().size());
            System.out.println("---");
        }
    }
}
