package com.pic.o2o.common;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;

/**
 * SimpleSOAPClient 測試類別
 * 測試 SOAP 格式轉換功能
 * 
 * <AUTHOR> OLTP System
 * @version 1.3
 */
public class SimpleSOAPClientTest {
    
    private SimpleSOAPClient soapClient;
    
    @Before
    public void setUp() {
        soapClient = new SimpleSOAPClient("http://test.example.com/soap", 30);
    }
    
    @Test
    public void testCreateSOAPEnvelope_WithManageTerminalXML() {
        // 準備測試資料 - Before 格式
        String originalXML = "<ManageTerminal>" +
                "<manageTerminalRequest>" +
                "<Channel>Test</Channel>" +
                "<Checksum>B43DB40CD926E971464816D781CFF69B</Checksum>" +
                "<ManageTerminalDateTime>20151015105959</ManageTerminalDateTime>" +
                "<ManageType>101</ManageType>" +
                "<MerchantCode>000000000000038</MerchantCode>" +
                "<ProgramCode>00001</ProgramCode>" +
                "<ShopCode>0000001028</ShopCode>" +
                "<TerminalCode></TerminalCode>" +
                "<TerminalSSN>20151015105959000001</TerminalSSN>" +
                "</manageTerminalRequest>" +
                "</ManageTerminal>";
        
        // 設定請求內容
        soapClient.setRequestContent(originalXML);
        
        // 使用反射來測試私有方法 createSOAPEnvelope
        try {
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("createSOAPEnvelope", String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(soapClient, originalXML);
            
            // 驗證結果
            assertNotNull("SOAP Envelope 不應為 null", result);
            
            // 驗證 SOAP Envelope 結構
            assertTrue("應包含 SOAP-ENV:Envelope", result.contains("<SOAP-ENV:Envelope"));
            assertTrue("應包含正確的命名空間", result.contains("xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\""));
            assertTrue("應包含 m0 命名空間", result.contains("xmlns:m0=\"http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common\""));
            assertTrue("應包含 SOAP-ENV:Body", result.contains("<SOAP-ENV:Body>"));
            
            // 驗證轉換後的內容
            assertTrue("應包含 m:ManageTerminal", result.contains("<m:ManageTerminal"));
            assertTrue("應包含 m:manageTerminalRequest", result.contains("<m:manageTerminalRequest>"));
            assertTrue("應包含 m0:Channel", result.contains("<m0:Channel>"));
            assertTrue("應包含 m0:Checksum", result.contains("<m0:Checksum>"));
            
            // 驗證內容值保持不變
            assertTrue("Channel 值應保持不變", result.contains("<m0:Channel>Test</m0:Channel>"));
            assertTrue("Checksum 值應保持不變", result.contains("<m0:Checksum>B43DB40CD926E971464816D781CFF69B</m0:Checksum>"));
            
            // 輸出結果供檢查
            System.out.println("Generated SOAP Envelope:");
            System.out.println(result);
            
        } catch (Exception e) {
            fail("測試失敗: " + e.getMessage());
        }
    }
    
    @Test
    public void testCreateSOAPEnvelope_WithEmptyContent() {
        try {
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("createSOAPEnvelope", String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(soapClient, "");
            
            // 驗證空內容的處理
            assertNotNull("SOAP Envelope 不應為 null", result);
            assertTrue("應包含空請求元素", result.contains("<m:request xmlns:m=\"http://ticketxpress.com.tw/\"/>"));
            
        } catch (Exception e) {
            fail("測試失敗: " + e.getMessage());
        }
    }
    
    @Test
    public void testCreateSOAPEnvelope_WithPlainText() {
        try {
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("createSOAPEnvelope", String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(soapClient, "Plain text content");
            
            // 驗證純文字內容的處理
            assertNotNull("SOAP Envelope 不應為 null", result);
            assertTrue("應包含 CDATA 包裝", result.contains("<![CDATA[Plain text content]]>"));
            
        } catch (Exception e) {
            fail("測試失敗: " + e.getMessage());
        }
    }
    
    @Test
    public void testTransformToSOAPFormat() {
        String originalXML = "<ManageTerminal>" +
                "<manageTerminalRequest>" +
                "<Channel>Test</Channel>" +
                "<Checksum>B43DB40CD926E971464816D781CFF69B</Checksum>" +
                "</manageTerminalRequest>" +
                "</ManageTerminal>";
        
        try {
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("transformToSOAPFormat", String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(soapClient, originalXML);
            
            // 驗證轉換結果
            assertNotNull("轉換結果不應為 null", result);
            assertTrue("應包含 m:ManageTerminal", result.contains("<m:ManageTerminal"));
            assertTrue("應包含 m:manageTerminalRequest", result.contains("<m:manageTerminalRequest>"));
            assertTrue("應包含 m0:Channel", result.contains("<m0:Channel>"));
            assertTrue("應包含 m0:Checksum", result.contains("<m0:Checksum>"));
            
            System.out.println("Transformed content:");
            System.out.println(result);
            
        } catch (Exception e) {
            fail("測試失敗: " + e.getMessage());
        }
    }
    
    @Test
    public void testExtractRootElementName() {
        try {
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("extractRootElementName", String.class);
            method.setAccessible(true);
            
            String result1 = (String) method.invoke(soapClient, "<ManageTerminal><test/></ManageTerminal>");
            assertEquals("應提取正確的根元素名稱", "ManageTerminal", result1);
            
            String result2 = (String) method.invoke(soapClient, "<root attr=\"value\"><test/></root>");
            assertEquals("應忽略屬性", "root", result2);
            
        } catch (Exception e) {
            fail("測試失敗: " + e.getMessage());
        }
    }
    
    @Test
    public void testTransformInnerElements() {
        try {
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("transformInnerElements", String.class);
            method.setAccessible(true);
            
            String input = "<Channel>Test</Channel><Checksum>ABC123</Checksum>";
            String result = (String) method.invoke(soapClient, input);
            
            assertEquals("應添加 m0 前綴", "<m0:Channel>Test</m0:Channel><m0:Checksum>ABC123</m0:Checksum>", result);
            
        } catch (Exception e) {
            fail("測試失敗: " + e.getMessage());
        }
    }
    
    /**
     * 整合測試：驗證完整的 SOAP 請求格式
     */
    @Test
    public void testCompleteSOAPFormat() {
        String originalXML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<ManageTerminal>" +
                "<manageTerminalRequest>" +
                "<Channel>Test</Channel>" +
                "<Checksum>B43DB40CD926E971464816D781CFF69B</Checksum>" +
                "<ManageTerminalDateTime>20151015105959</ManageTerminalDateTime>" +
                "<ManageType>101</ManageType>" +
                "<MerchantCode>000000000000038</MerchantCode>" +
                "<ProgramCode>00001</ProgramCode>" +
                "<ShopCode>0000001028</ShopCode>" +
                "<TerminalCode></TerminalCode>" +
                "<TerminalSSN>20151015105959000001</TerminalSSN>" +
                "</manageTerminalRequest>" +
                "</ManageTerminal>";
        
        try {
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("createSOAPEnvelope", String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(soapClient, originalXML);
            
            // 驗證期望的 SOAP 格式
            String expectedPattern = ".*<SOAP-ENV:Envelope.*xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\".*" +
                    "xmlns:m0=\"http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common\".*" +
                    "<SOAP-ENV:Body>.*" +
                    "<m:ManageTerminal xmlns:m=\"http://ticketxpress.com.tw/\">.*" +
                    "<m:manageTerminalRequest>.*" +
                    "<m0:Channel>Test</m0:Channel>.*" +
                    "</m:manageTerminalRequest>.*" +
                    "</m:ManageTerminal>.*" +
                    "</SOAP-ENV:Body>.*" +
                    "</SOAP-ENV:Envelope>.*";
            
            assertTrue("SOAP 格式應符合期望的模式", result.matches(expectedPattern.replace(".*", "[\\s\\S]*")));
            
            // 輸出完整結果
            System.out.println("\n=== Complete SOAP Format Test ===");
            System.out.println("Original XML:");
            System.out.println(originalXML);
            System.out.println("\nGenerated SOAP Envelope:");
            System.out.println(formatXML(result));
            
        } catch (Exception e) {
            fail("測試失敗: " + e.getMessage());
        }
    }
    
    /**
     * 格式化 XML 輸出（簡單版本）
     */
    private String formatXML(String xml) {
        return xml.replace("><", ">\n<");
    }
}
