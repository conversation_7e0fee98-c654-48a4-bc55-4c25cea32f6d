package com.pic.o2o.common;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.pic.oltp.GlobalVariable;

/**
 * SOAP 命名空間前綴測試
 * 驗證 manageTerminalRequest 使用 m: 前綴，其他元素使用 m0: 前綴
 * 
 * <AUTHOR> OLTP System
 * @version 1.3
 */
public class SOAPNamespacePrefixTest {
    
    private SimpleSOAPClient simpleSOAPClient;
    private SOAPClient soapClient;
    
    @Before
    public void setUp() {
        simpleSOAPClient = new SimpleSOAPClient("http://test.soap.service.com/endpoint", 30);
        soapClient = new SOAPClient("http://test.soap.service.com/endpoint", 30);
        
        GlobalVariable.HOST = "TEST_HOST";
        GlobalVariable.logTrans = "TEST_LOG";
    }
    
    @Test
    public void testManageTerminalRequestNamespacePrefix() throws Exception {
        // 測試 manageTerminalRequest 應該使用 m: 前綴
        String innerContent = "<ManageTerminal>" +
                             "<manageTerminalRequest>" +
                             "<Channel>Test</Channel>" +
                             "<Checksum>B43DB40CD926E971464816D781CFF69B</Checksum>" +
                             "<ManageTerminalDateTime>20151015105959</ManageTerminalDateTime>" +
                             "<ManageType>101</ManageType>" +
                             "<MerchantCode>000000000000038</MerchantCode>" +
                             "<ProgramCode>00001</ProgramCode>" +
                             "<ShopCode>0000001028</ShopCode>" +
                             "<TerminalCode></TerminalCode>" +
                             "<TerminalSSN>20151015105959000001</TerminalSSN>" +
                             "</manageTerminalRequest>" +
                             "</ManageTerminal>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 測試 SimpleSOAPClient 的轉換
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("transformInnerElements", String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(simpleSOAPClient, innerContent);
            
            // 驗證結果
            assertNotNull("轉換結果不應為 null", result);
            
            // 驗證 manageTerminalRequest 使用 m: 前綴
            assertTrue("manageTerminalRequest 應該使用 m: 前綴", 
                      result.contains("<m:manageTerminalRequest>"));
            assertTrue("manageTerminalRequest 結束標籤應該使用 m: 前綴", 
                      result.contains("</m:manageTerminalRequest>"));
            
            // 驗證其他元素使用 m0: 前綴
            assertTrue("ManageTerminal 應該使用 m0: 前綴", 
                      result.contains("<m0:ManageTerminal>"));
            assertTrue("Channel 應該使用 m0: 前綴", 
                      result.contains("<m0:Channel>"));
            assertTrue("Checksum 應該使用 m0: 前綴", 
                      result.contains("<m0:Checksum>"));
            assertTrue("ManageType 應該使用 m0: 前綴", 
                      result.contains("<m0:ManageType>"));
            
            // 驗證不應該有錯誤的前綴組合
            assertFalse("不應該有 m0:manageTerminalRequest", 
                       result.contains("<m0:manageTerminalRequest>"));
            assertFalse("不應該有重複前綴", 
                       result.contains("<m0:m:manageTerminalRequest>"));
            
            System.out.println("=== Namespace Prefix Test Result ===");
            System.out.println("Original:");
            System.out.println(innerContent);
            System.out.println("\nTransformed:");
            System.out.println(result);
            System.out.println("====================================");
        }
    }
    
    @Test
    public void testSOAPClientNamespacePrefix() throws Exception {
        // 測試 SOAPClient 的命名空間前綴轉換
        String innerContent = "<ManageTerminal>" +
                             "<manageTerminalRequest>" +
                             "<Channel>Test</Channel>" +
                             "<Checksum>ABC123</Checksum>" +
                             "</manageTerminalRequest>" +
                             "</ManageTerminal>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 測試 SOAPClient 的轉換
            java.lang.reflect.Method method = SOAPClient.class.getDeclaredMethod("transformInnerElements", String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(soapClient, innerContent);
            
            // 驗證 SOAPClient 和 SimpleSOAPClient 產生相同結果
            java.lang.reflect.Method simpleMethod = SimpleSOAPClient.class.getDeclaredMethod("transformInnerElements", String.class);
            simpleMethod.setAccessible(true);
            String simpleResult = (String) simpleMethod.invoke(simpleSOAPClient, innerContent);
            
            assertEquals("SOAPClient 和 SimpleSOAPClient 應該產生相同的轉換結果", simpleResult, result);
            
            // 驗證命名空間前綴
            assertTrue("manageTerminalRequest 應該使用 m: 前綴", 
                      result.contains("<m:manageTerminalRequest>"));
            assertTrue("其他元素應該使用 m0: 前綴", 
                      result.contains("<m0:Channel>"));
            
            System.out.println("SOAPClient 和 SimpleSOAPClient 轉換結果一致性驗證通過");
        }
    }
    
    @Test
    public void testComplexNestedStructure() throws Exception {
        // 測試複雜的嵌套結構
        String complexContent = "<ManageTerminal>" +
                               "<manageTerminalRequest>" +
                               "<RequestHeader>" +
                               "<Channel>Test</Channel>" +
                               "<Version>1.0</Version>" +
                               "</RequestHeader>" +
                               "<RequestBody>" +
                               "<TerminalInfo>" +
                               "<TerminalCode>T001</TerminalCode>" +
                               "<MerchantCode>M001</MerchantCode>" +
                               "</TerminalInfo>" +
                               "</RequestBody>" +
                               "</manageTerminalRequest>" +
                               "</ManageTerminal>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("transformInnerElements", String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(simpleSOAPClient, complexContent);
            
            // 驗證複雜結構的命名空間前綴
            assertTrue("manageTerminalRequest 應該使用 m: 前綴", 
                      result.contains("<m:manageTerminalRequest>"));
            assertTrue("ManageTerminal 應該使用 m0: 前綴", 
                      result.contains("<m0:ManageTerminal>"));
            assertTrue("RequestHeader 應該使用 m0: 前綴", 
                      result.contains("<m0:RequestHeader>"));
            assertTrue("Channel 應該使用 m0: 前綴", 
                      result.contains("<m0:Channel>"));
            assertTrue("TerminalInfo 應該使用 m0: 前綴", 
                      result.contains("<m0:TerminalInfo>"));
            assertTrue("TerminalCode 應該使用 m0: 前綴", 
                      result.contains("<m0:TerminalCode>"));
            
            System.out.println("=== Complex Structure Test ===");
            System.out.println("Result:");
            System.out.println(result);
            System.out.println("==============================");
        }
    }
    
    @Test
    public void testWithExistingNamespaces() throws Exception {
        // 測試帶有現有命名空間前綴的內容
        String contentWithNS = "<ns0:ManageTerminal>" +
                              "<ns0:manageTerminalRequest>" +
                              "<ns0:Channel>Test</ns0:Channel>" +
                              "<ns1:Checksum>ABC123</ns1:Checksum>" +
                              "<ns2:ManageType>101</ns2:ManageType>" +
                              "</ns0:manageTerminalRequest>" +
                              "</ns0:ManageTerminal>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("transformInnerElements", String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(simpleSOAPClient, contentWithNS);
            
            // 驗證舊的命名空間前綴被移除
            assertFalse("不應該包含 ns0: 前綴", result.contains("ns0:"));
            assertFalse("不應該包含 ns1: 前綴", result.contains("ns1:"));
            assertFalse("不應該包含 ns2: 前綴", result.contains("ns2:"));
            
            // 驗證新的命名空間前綴被正確添加
            assertTrue("manageTerminalRequest 應該使用 m: 前綴", 
                      result.contains("<m:manageTerminalRequest>"));
            assertTrue("其他元素應該使用 m0: 前綴", 
                      result.contains("<m0:ManageTerminal>"));
            assertTrue("Channel 應該使用 m0: 前綴", 
                      result.contains("<m0:Channel>"));
            assertTrue("Checksum 應該使用 m0: 前綴", 
                      result.contains("<m0:Checksum>"));
            
            System.out.println("=== Existing Namespaces Test ===");
            System.out.println("Original with ns prefixes:");
            System.out.println(contentWithNS);
            System.out.println("\nTransformed:");
            System.out.println(result);
            System.out.println("================================");
        }
    }
}
