package com.pic.o2o.common;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.pic.oltp.GlobalVariable;

/**
 * OLTP-SOAP 轉換測試
 * 驗證只對 AP 內容進行 SOAP 包裝，保持 OLTP 結構不變
 * 
 * <AUTHOR> OLTP System
 * @version 1.3
 */
public class OLTPSOAPTransformationTest {
    
    private SimpleSOAPClient simpleSOAPClient;
    private SOAPClient soapClient;
    
    @Before
    public void setUp() {
        simpleSOAPClient = new SimpleSOAPClient("http://test.soap.service.com/endpoint", 30);
        soapClient = new SOAPClient("http://test.soap.service.com/endpoint", 30);
        
        GlobalVariable.HOST = "TEST_HOST";
        GlobalVariable.logTrans = "TEST_LOG";
    }
    
    @Test
    public void testOLTPSOAPTransformation_CompleteFlow() throws Exception {
        // 準備完整的 OLTP XML
        String oltpXml = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<ns0:OLTP xmlns:ns0=\"http://7-11.com.tw/online\">\n" +
                "\t<ns0:HEADER>\n" +
                "\t\t<ns0:VER>01.01</ns0:VER>\n" +
                "\t\t<ns0:FROM>BU00100001</ns0:FROM>\n" +
                "\t\t<ns0:TERMINO>20150304972549010000011400</ns0:TERMINO>\n" +
                "\t\t<ns0:TO>BU01600010</ns0:TO>\n" +
                "\t\t<ns0:BUSINESS>0160100</ns0:BUSINESS>\n" +
                "\t\t<ns0:DATE>20150304</ns0:DATE>\n" +
                "\t\t<ns0:TIME>140000</ns0:TIME>\n" +
                "\t\t<ns0:STATCODE>0000</ns0:STATCODE>\n" +
                "\t\t<ns0:STATDESC/>\n" +
                "\t</ns0:HEADER>\n" +
                "\t<ns0:AP>\n" +
                "\t\t<ManageTerminal>\n" +
                "\t\t\t<manageTerminalRequest>\n" +
                "\t\t\t\t<Channel>Test</Channel>\n" +
                "\t\t\t\t<Checksum>B43DB40CD926E971464816D781CFF69B</Checksum>\n" +
                "\t\t\t\t<ManageTerminalDateTime>20151015105959</ManageTerminalDateTime>\n" +
                "\t\t\t\t<ManageType>101</ManageType>\n" +
                "\t\t\t\t<MerchantCode>000000000000038</MerchantCode>\n" +
                "\t\t\t\t<ProgramCode>00001</ProgramCode>\n" +
                "\t\t\t\t<ShopCode>0000001028</ShopCode>\n" +
                "\t\t\t\t<TerminalCode></TerminalCode>\n" +
                "\t\t\t\t<TerminalSSN>20151015105959000001</TerminalSSN>\n" +
                "\t\t\t</manageTerminalRequest>\n" +
                "\t\t</ManageTerminal>\n" +
                "\t</ns0:AP>\n" +
                "</ns0:OLTP>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 測試 SimpleSOAPClient 的轉換
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("createSOAPEnvelope", String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(simpleSOAPClient, oltpXml);
            
            // 驗證結果
            assertNotNull("轉換結果不應為 null", result);
            
            // 驗證 OLTP 結構保持不變
            assertTrue("應該保留 ns0:OLTP 根元素", result.contains("<ns0:OLTP xmlns:ns0=\"http://7-11.com.tw/online\">"));
            assertTrue("應該保留 ns0:HEADER", result.contains("<ns0:HEADER>"));
            assertTrue("應該保留 ns0:VER", result.contains("<ns0:VER>01.01</ns0:VER>"));
            assertTrue("應該保留 ns0:FROM", result.contains("<ns0:FROM>BU00100001</ns0:FROM>"));
            assertTrue("應該保留 ns0:AP 標籤", result.contains("<ns0:AP>"));
            assertTrue("應該保留 ns0:AP 結束標籤", result.contains("</ns0:AP>"));
            assertTrue("應該保留 ns0:OLTP 結束標籤", result.contains("</ns0:OLTP>"));
            
            // 驗證 AP 內容被 SOAP 包裝
            assertTrue("AP 內容應該包含 SOAP-ENV:Envelope", result.contains("<SOAP-ENV:Envelope"));
            assertTrue("應該包含 SOAP 命名空間", result.contains("xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\""));
            assertTrue("應該包含 m0 命名空間", result.contains("xmlns:m0=\"http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common\""));
            assertTrue("應該包含 SOAP-ENV:Body", result.contains("<SOAP-ENV:Body>"));
            
            // 驗證業務內容被正確轉換
            assertTrue("應該包含轉換後的 ManageTerminal", result.contains("<m:ManageTerminal"));
            assertTrue("應該包含 m0 前綴的元素", result.contains("<m0:Channel>Test</m0:Channel>"));
            assertTrue("應該包含 m0 前綴的元素", result.contains("<m0:Checksum>B43DB40CD926E971464816D781CFF69B</m0:Checksum>"));
            
            System.out.println("=== OLTP-SOAP Transformation Test Result ===");
            System.out.println("Original OLTP XML:");
            System.out.println(oltpXml);
            System.out.println("\nTransformed OLTP with SOAP:");
            System.out.println(result);
            System.out.println("============================================");
        }
    }
    
    @Test
    public void testExtractAPContent() throws Exception {
        String oltpXml = "<ns0:OLTP xmlns:ns0=\"http://7-11.com.tw/online\">" +
                        "<ns0:HEADER><ns0:VER>01.01</ns0:VER></ns0:HEADER>" +
                        "<ns0:AP>" +
                        "<ManageTerminal>" +
                        "<Channel>Test</Channel>" +
                        "</ManageTerminal>" +
                        "</ns0:AP>" +
                        "</ns0:OLTP>";
        
        // 測試 AP 內容提取
        java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("extractAPContent", String.class);
        method.setAccessible(true);
        String apContent = (String) method.invoke(simpleSOAPClient, oltpXml);
        
        assertNotNull("AP 內容不應為 null", apContent);
        assertTrue("應該包含 ManageTerminal", apContent.contains("<ManageTerminal>"));
        assertTrue("應該包含 Channel", apContent.contains("<Channel>Test</Channel>"));
        assertFalse("不應該包含 ns0:AP 標籤", apContent.contains("<ns0:AP>"));
        assertFalse("不應該包含 HEADER", apContent.contains("<ns0:HEADER>"));
        
        System.out.println("Extracted AP Content: " + apContent);
    }
    
    @Test
    public void testReplaceAPContent() throws Exception {
        String originalOltp = "<ns0:OLTP xmlns:ns0=\"http://7-11.com.tw/online\">" +
                             "<ns0:HEADER><ns0:VER>01.01</ns0:VER></ns0:HEADER>" +
                             "<ns0:AP>" +
                             "<ManageTerminal><Channel>Test</Channel></ManageTerminal>" +
                             "</ns0:AP>" +
                             "</ns0:OLTP>";
        
        String soapContent = "<SOAP-ENV:Envelope xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\">" +
                           "<SOAP-ENV:Body>" +
                           "<m:ManageTerminal xmlns:m=\"http://ticketxpress.com.tw/\">" +
                           "<m0:Channel>Test</m0:Channel>" +
                           "</m:ManageTerminal>" +
                           "</SOAP-ENV:Body>" +
                           "</SOAP-ENV:Envelope>";
        
        // 測試內容替換
        java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("replaceAPContent", String.class, String.class);
        method.setAccessible(true);
        String result = (String) method.invoke(simpleSOAPClient, originalOltp, soapContent);
        
        assertNotNull("結果不應為 null", result);
        assertTrue("應該保留 OLTP 結構", result.contains("<ns0:OLTP"));
        assertTrue("應該保留 HEADER", result.contains("<ns0:HEADER>"));
        assertTrue("應該包含 SOAP 內容", result.contains("<SOAP-ENV:Envelope"));
        assertTrue("應該保留 AP 標籤", result.contains("<ns0:AP>"));
        
        System.out.println("Replaced OLTP: " + result);
    }
    
    @Test
    public void testNonOLTPXMLHandling() throws Exception {
        // 測試非 OLTP 格式的 XML 處理
        String regularXml = "<ManageTerminal>" +
                           "<manageTerminalRequest>" +
                           "<Channel>Test</Channel>" +
                           "<Checksum>ABC123</Checksum>" +
                           "</manageTerminalRequest>" +
                           "</ManageTerminal>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 測試非 OLTP XML 的處理
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("createSOAPEnvelope", String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(simpleSOAPClient, regularXml);
            
            // 驗證結果應該是標準的 SOAP Envelope
            assertTrue("應該包含 SOAP-ENV:Envelope", result.contains("<SOAP-ENV:Envelope"));
            assertTrue("應該包含 XML 宣告", result.contains("<?xml version=\"1.0\""));
            assertTrue("不應該包含 OLTP 結構", !result.contains("<ns0:OLTP"));
            assertTrue("應該包含轉換後的內容", result.contains("<m:ManageTerminal"));
            
            System.out.println("Non-OLTP XML Result: " + result);
        }
    }
    
    @Test
    public void testEmptyAPContent() throws Exception {
        // 測試空的 AP 內容
        String oltpWithEmptyAP = "<ns0:OLTP xmlns:ns0=\"http://7-11.com.tw/online\">" +
                                "<ns0:HEADER><ns0:VER>01.01</ns0:VER></ns0:HEADER>" +
                                "<ns0:AP></ns0:AP>" +
                                "</ns0:OLTP>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("createSOAPEnvelope", String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(simpleSOAPClient, oltpWithEmptyAP);
            
            // 驗證空 AP 的處理
            assertNotNull("結果不應為 null", result);
            assertTrue("應該保留 OLTP 結構", result.contains("<ns0:OLTP"));
            assertTrue("應該保留 AP 標籤", result.contains("<ns0:AP>"));
            
            System.out.println("Empty AP Result: " + result);
        }
    }
}
