package com.pic.o2o.common;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.pic.oltp.GlobalVariable;

/**
 * SOAP HTTP 標頭測試
 * 驗證 SOAPAction 和 Content-Type 標頭的正確設定
 * 
 * <AUTHOR> OLTP System
 * @version 1.3
 */
public class SOAPHTTPHeadersTest {
    
    private SimpleSOAPClient simpleSOAPClient;
    private SOAPClient soapClient;
    
    @Before
    public void setUp() {
        simpleSOAPClient = new SimpleSOAPClient("http://test.soap.service.com/endpoint", 30);
        soapClient = new SOAPClient("http://test.soap.service.com/endpoint", 30);
        
        GlobalVariable.HOST = "TEST_HOST";
        GlobalVariable.logTrans = "TEST_LOG";
    }
    
    @Test
    public void testSOAPActionInference_ManageTerminal() throws Exception {
        // 測試 ManageTerminal 的 SOAPAction 推斷
        String manageTerminalContent = "<ManageTerminal>" +
                                     "<manageTerminalRequest>" +
                                     "<Channel>Test</Channel>" +
                                     "</manageTerminalRequest>" +
                                     "</ManageTerminal>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 測試 SimpleSOAPClient 的 SOAPAction 推斷
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("inferSOAPAction", String.class);
            method.setAccessible(true);
            String soapAction = (String) method.invoke(simpleSOAPClient, manageTerminalContent);
            
            assertEquals("ManageTerminal 應該推斷出正確的 SOAPAction", 
                        "http://ticketxpress.com.tw/IPOSProxy/ManageTerminal", soapAction);
            
            // 測試設定內容後的自動推斷
            simpleSOAPClient.setRequestContent(manageTerminalContent);
            
            // 使用反射檢查 soapAction 欄位
            java.lang.reflect.Field soapActionField = SimpleSOAPClient.class.getDeclaredField("soapAction");
            soapActionField.setAccessible(true);
            String autoInferredAction = (String) soapActionField.get(simpleSOAPClient);
            
            assertEquals("設定內容後應該自動推斷 SOAPAction", 
                        "http://ticketxpress.com.tw/IPOSProxy/ManageTerminal", autoInferredAction);
            
            System.out.println("ManageTerminal SOAPAction: " + soapAction);
        }
    }
    
    @Test
    public void testSOAPActionInference_ProcessPayment() throws Exception {
        // 測試 ProcessPayment 的 SOAPAction 推斷
        String paymentContent = "<ProcessPayment>" +
                               "<paymentRequest>" +
                               "<Amount>1000</Amount>" +
                               "</paymentRequest>" +
                               "</ProcessPayment>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("inferSOAPAction", String.class);
            method.setAccessible(true);
            String soapAction = (String) method.invoke(simpleSOAPClient, paymentContent);
            
            assertEquals("ProcessPayment 應該推斷出正確的 SOAPAction", 
                        "http://ticketxpress.com.tw/IPOSProxy/ProcessPayment", soapAction);
            
            System.out.println("ProcessPayment SOAPAction: " + soapAction);
        }
    }
    
    @Test
    public void testSOAPActionInference_DefaultAction() throws Exception {
        // 測試未知內容的預設 SOAPAction
        String unknownContent = "<UnknownOperation>" +
                               "<someRequest>" +
                               "<Data>Test</Data>" +
                               "</someRequest>" +
                               "</UnknownOperation>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("inferSOAPAction", String.class);
            method.setAccessible(true);
            String soapAction = (String) method.invoke(simpleSOAPClient, unknownContent);
            
            assertEquals("未知內容應該使用根元素名稱", 
                        "http://ticketxpress.com.tw/IPOSProxy/UnknownOperation", soapAction);
            
            System.out.println("Unknown content SOAPAction: " + soapAction);
        }
    }
    
    @Test
    public void testManualSOAPActionSetting() {
        // 測試手動設定 SOAPAction
        String customAction = "http://custom.service.com/CustomAction";
        
        simpleSOAPClient.setSOAPAction(customAction);
        
        try {
            java.lang.reflect.Field soapActionField = SimpleSOAPClient.class.getDeclaredField("soapAction");
            soapActionField.setAccessible(true);
            String actualAction = (String) soapActionField.get(simpleSOAPClient);
            
            assertEquals("手動設定的 SOAPAction 應該被正確保存", customAction, actualAction);
            
            System.out.println("Manual SOAPAction: " + actualAction);
        } catch (Exception e) {
            fail("測試失敗: " + e.getMessage());
        }
    }
    
    @Test
    public void testSOAPClientConsistency() throws Exception {
        // 測試 SOAPClient 和 SimpleSOAPClient 的 SOAPAction 推斷一致性
        String testContent = "<ManageTerminal><manageTerminalRequest><Channel>Test</Channel></manageTerminalRequest></ManageTerminal>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 測試 SimpleSOAPClient
            java.lang.reflect.Method simpleMethod = SimpleSOAPClient.class.getDeclaredMethod("inferSOAPAction", String.class);
            simpleMethod.setAccessible(true);
            String simpleAction = (String) simpleMethod.invoke(simpleSOAPClient, testContent);
            
            // 測試 SOAPClient
            java.lang.reflect.Method soapMethod = SOAPClient.class.getDeclaredMethod("inferSOAPAction", String.class);
            soapMethod.setAccessible(true);
            String soapAction = (String) soapMethod.invoke(soapClient, testContent);
            
            assertEquals("兩個客戶端應該推斷出相同的 SOAPAction", simpleAction, soapAction);
            
            System.out.println("SOAPClient 和 SimpleSOAPClient SOAPAction 一致性驗證通過");
            System.out.println("SOAPAction: " + soapAction);
        }
    }
    
    @Test
    public void testOLTPContentSOAPAction() throws Exception {
        // 測試 OLTP 格式內容的 SOAPAction 推斷
        String oltpContent = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                           "<ns0:OLTP xmlns:ns0=\"http://7-11.com.tw/online\">" +
                           "<ns0:HEADER><ns0:VER>01.01</ns0:VER></ns0:HEADER>" +
                           "<ns0:AP>" +
                           "<ManageTerminal>" +
                           "<manageTerminalRequest>" +
                           "<Channel>Test</Channel>" +
                           "</manageTerminalRequest>" +
                           "</ManageTerminal>" +
                           "</ns0:AP>" +
                           "</ns0:OLTP>";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("inferSOAPAction", String.class);
            method.setAccessible(true);
            String soapAction = (String) method.invoke(simpleSOAPClient, oltpContent);
            
            assertEquals("OLTP 內容應該能正確推斷 SOAPAction", 
                        "http://ticketxpress.com.tw/IPOSProxy/ManageTerminal", soapAction);
            
            System.out.println("OLTP content SOAPAction: " + soapAction);
        }
    }
    
    @Test
    public void testEmptyContentSOAPAction() throws Exception {
        // 測試空內容的 SOAPAction 處理
        String emptyContent = "";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            java.lang.reflect.Method method = SimpleSOAPClient.class.getDeclaredMethod("inferSOAPAction", String.class);
            method.setAccessible(true);
            String soapAction = (String) method.invoke(simpleSOAPClient, emptyContent);
            
            assertEquals("空內容應該使用預設 SOAPAction", 
                        "http://ticketxpress.com.tw/IPOSProxy/DefaultAction", soapAction);
            
            System.out.println("Empty content SOAPAction: " + soapAction);
        }
    }
    
    @Test
    public void testSOAPServiceWithCustomAction() throws Exception {
        // 測試 SOAPService 使用自訂 SOAPAction
        String testContent = "<ManageTerminal><manageTerminalRequest><Channel>Test</Channel></manageTerminalRequest></ManageTerminal>";
        String customAction = "http://custom.service.com/CustomManageTerminal";
        
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .thenReturn("2024/01/01 12:00:00.000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 注意：這裡只測試方法簽名，實際的網路請求需要模擬
            try {
                // 驗證新的 sendSOAPRequest 方法存在
                java.lang.reflect.Method method = SOAPService.class.getDeclaredMethod(
                    "sendSOAPRequest", String.class, int.class, String.class, String.class);
                assertNotNull("SOAPService 應該有支援 SOAPAction 的方法", method);
                
                System.out.println("✅ SOAPService 支援自訂 SOAPAction 的方法存在");
            } catch (NoSuchMethodException e) {
                fail("SOAPService 缺少支援 SOAPAction 的方法");
            }
        }
    }
}
