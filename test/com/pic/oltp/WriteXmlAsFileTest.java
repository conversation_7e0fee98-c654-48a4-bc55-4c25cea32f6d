package com.pic.oltp;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import java.io.File;
import java.io.FileReader;
import java.io.BufferedReader;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.junit.Before;
import org.junit.Test;
import org.junit.After;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.pic.o2o.common.Utility;

/**
 * writeXmlAsFileWithoutEncryption 方法測試
 * 驗證 SOAP 專用的不加密寫檔功能
 * 
 * <AUTHOR> OLTP System
 * @version 1.3
 */
public class WriteXmlAsFileTest {
    
    private OLTP oltpInstance;
    private Element testPosInXML;
    private Element testForPosXML;
    private StringBuffer xmlFileName;
    private StringBuffer errorMsg;
    private String testTermino = "TEST001";
    
    @Before
    public void setUp() throws Exception {
        // 初始化測試物件
        oltpInstance = new OLTP();
        xmlFileName = new StringBuffer();
        errorMsg = new StringBuffer();
        
        // 設定測試環境
        GlobalVariable.XML_LOG_PATH = "test_output/";
        GlobalVariable.HOST = "TEST_HOST";
        GlobalVariable.logTrans = "TEST_LOG";
        
        // 確保測試輸出目錄存在
        File testDir = new File("test_output");
        if (!testDir.exists()) {
            testDir.mkdirs();
        }
        
        // 建立測試用的 POS IN XML
        String posInXmlStr = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<root>" +
                "<HEADER>" +
                "<FROM>POS001</FROM>" +
                "<TO>SOAP001</TO>" +
                "<TERMINO>" + testTermino + "</TERMINO>" +
                "<STATCODE>0000</STATCODE>" +
                "<STATDESC>成功</STATDESC>" +
                "</HEADER>" +
                "<ns0:AP xmlns:ns0=\"http://test.namespace\">" +
                "<ManageTerminal>" +
                "<manageTerminalRequest>" +
                "<Channel>Test</Channel>" +
                "<Checksum>B43DB40CD926E971464816D781CFF69B</Checksum>" +
                "<ManageTerminalDateTime>20151015105959</ManageTerminalDateTime>" +
                "<ManageType>101</ManageType>" +
                "<MerchantCode>000000000000038</MerchantCode>" +
                "<ProgramCode>00001</ProgramCode>" +
                "<ShopCode>0000001028</ShopCode>" +
                "<TerminalCode></TerminalCode>" +
                "<TerminalSSN>20151015105959000001</TerminalSSN>" +
                "</manageTerminalRequest>" +
                "</ManageTerminal>" +
                "</ns0:AP>" +
                "</root>";
        
        Document posInDoc = DocumentHelper.parseText(posInXmlStr);
        testPosInXML = posInDoc.getRootElement();
        
        // 建立測試用的 For POS XML (回應)
        String forPosXmlStr = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<root>" +
                "<HEADER>" +
                "<FROM>SOAP001</FROM>" +
                "<TO>POS001</TO>" +
                "<TERMINO>" + testTermino + "</TERMINO>" +
                "<STATCODE>0000</STATCODE>" +
                "<STATDESC>成功</STATDESC>" +
                "</HEADER>" +
                "<ns0:AP xmlns:ns0=\"http://test.namespace\">" +
                "<ManageTerminalResponse>" +
                "<Result>SUCCESS</Result>" +
                "<ResponseCode>0000</ResponseCode>" +
                "<ResponseMessage>處理成功</ResponseMessage>" +
                "<TerminalCode>T001</TerminalCode>" +
                "</ManageTerminalResponse>" +
                "</ns0:AP>" +
                "</root>";
        
        Document forPosDoc = DocumentHelper.parseText(forPosXmlStr);
        testForPosXML = forPosDoc.getRootElement();
    }
    
    @After
    public void tearDown() {
        // 清理測試檔案
        File testDir = new File("test_output");
        if (testDir.exists()) {
            File[] files = testDir.listFiles();
            if (files != null) {
                for (File file : files) {
                    file.delete();
                }
            }
        }
    }
    
    @Test
    public void testWriteXmlAsFileWithoutEncryption_Success() throws Exception {
        // 模擬 Utility 方法
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyyMMddHHmmssSSS"))
                       .thenReturn("20240101120000000");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 使用反射調用私有方法
            java.lang.reflect.Method method = OLTP.class.getDeclaredMethod(
                "writeXmlAsFileWithoutEncryption", 
                Element.class, Element.class, StringBuffer.class, StringBuffer.class);
            method.setAccessible(true);
            
            // 執行測試
            method.invoke(oltpInstance, testPosInXML, testForPosXML, xmlFileName, errorMsg);
            
            // 驗證檔案名稱
            String expectedFileName = "20240101120000000_" + testTermino + "_SOAP.txt";
            assertEquals("檔案名稱應包含 _SOAP 後綴", expectedFileName, xmlFileName.toString());
            
            // 驗證檔案是否被創建
            File outputFile = new File("test_output/" + expectedFileName);
            assertTrue("輸出檔案應該存在", outputFile.exists());
            
            // 讀取並驗證檔案內容
            String fileContent = readFileContent(outputFile);
            
            // 驗證檔案標題
            assertTrue("應包含 POS IN XML 標題", fileContent.contains("POS IN XML (SOAP - No Encryption):"));
            assertTrue("應包含 REPLY POS XML 標題", fileContent.contains("REPLY POS XML (SOAP - No Encryption):"));
            
            // 驗證 AP 層內容未加密
            assertTrue("應包含原始 Channel 內容", fileContent.contains("<Channel>Test</Channel>"));
            assertTrue("應包含原始 Checksum 內容", fileContent.contains("<Checksum>B43DB40CD926E971464816D781CFF69B</Checksum>"));
            assertTrue("應包含回應的 Result 內容", fileContent.contains("<Result>SUCCESS</Result>"));
            
            // 驗證沒有錯誤訊息
            assertEquals("不應有錯誤訊息", 0, errorMsg.length());
            
            System.out.println("Generated file content:");
            System.out.println(fileContent);
        }
    }
    
    @Test
    public void testWriteXmlAsFileWithoutEncryption_OnlyPosInXML() throws Exception {
        // 測試只有 POS IN XML，沒有回應 XML 的情況
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyyMMddHHmmssSSS"))
                       .thenReturn("20240101120000001");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 使用反射調用私有方法
            java.lang.reflect.Method method = OLTP.class.getDeclaredMethod(
                "writeXmlAsFileWithoutEncryption", 
                Element.class, Element.class, StringBuffer.class, StringBuffer.class);
            method.setAccessible(true);
            
            // 執行測試（forPosXML 為 null）
            method.invoke(oltpInstance, testPosInXML, null, xmlFileName, errorMsg);
            
            // 驗證檔案名稱
            String expectedFileName = "20240101120000001_" + testTermino + "_SOAP.txt";
            assertEquals("檔案名稱應正確", expectedFileName, xmlFileName.toString());
            
            // 驗證檔案是否被創建
            File outputFile = new File("test_output/" + expectedFileName);
            assertTrue("輸出檔案應該存在", outputFile.exists());
            
            // 讀取並驗證檔案內容
            String fileContent = readFileContent(outputFile);
            
            // 驗證只有 POS IN XML 部分
            assertTrue("應包含 POS IN XML 標題", fileContent.contains("POS IN XML (SOAP - No Encryption):"));
            assertFalse("不應包含 REPLY POS XML 標題", fileContent.contains("REPLY POS XML (SOAP - No Encryption):"));
            
            // 驗證 AP 層內容
            assertTrue("應包含原始 Channel 內容", fileContent.contains("<Channel>Test</Channel>"));
        }
    }
    
    @Test
    public void testConsoleOutput() throws Exception {
        // 測試 Console 輸出功能
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyyMMddHHmmssSSS"))
                       .thenReturn("20240101120000002");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 重定向 System.out 來捕獲輸出
            java.io.ByteArrayOutputStream outContent = new java.io.ByteArrayOutputStream();
            java.io.PrintStream originalOut = System.out;
            System.setOut(new java.io.PrintStream(outContent));
            
            try {
                // 使用反射調用私有方法
                java.lang.reflect.Method method = OLTP.class.getDeclaredMethod(
                    "writeXmlAsFileWithoutEncryption", 
                    Element.class, Element.class, StringBuffer.class, StringBuffer.class);
                method.setAccessible(true);
                
                // 執行測試
                method.invoke(oltpInstance, testPosInXML, testForPosXML, xmlFileName, errorMsg);
                
                // 驗證 Console 輸出
                String consoleOutput = outContent.toString();
                assertTrue("應輸出 apStrBuffer 內容", consoleOutput.contains("apStrBuffer="));
                assertTrue("應輸出 ReplyapStrBuffer 內容", consoleOutput.contains("ReplyapStrBuffer="));
                assertTrue("應輸出 ReplyAPStr 內容", consoleOutput.contains("ReplyAPStr="));
                
                // 驗證輸出包含原始 XML 內容（未加密）
                assertTrue("apStrBuffer 應包含原始內容", 
                    consoleOutput.contains("<Channel>Test</Channel>"));
                assertTrue("ReplyapStrBuffer 應包含回應內容", 
                    consoleOutput.contains("<Result>SUCCESS</Result>"));
                
                System.out.println("Console output captured:");
                System.out.println(consoleOutput);
                
            } finally {
                // 恢復原始的 System.out
                System.setOut(originalOut);
            }
        }
    }
    
    @Test
    public void testAPTagRemoval() throws Exception {
        // 測試 AP 標籤移除功能
        try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
            mockedUtility.when(() -> Utility.getDateTime("yyyyMMddHHmmssSSS"))
                       .thenReturn("20240101120000003");
            mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                       .thenAnswer(invocation -> null);
            
            // 使用反射調用私有方法
            java.lang.reflect.Method method = OLTP.class.getDeclaredMethod(
                "writeXmlAsFileWithoutEncryption", 
                Element.class, Element.class, StringBuffer.class, StringBuffer.class);
            method.setAccessible(true);
            
            // 執行測試
            method.invoke(oltpInstance, testPosInXML, testForPosXML, xmlFileName, errorMsg);
            
            // 讀取檔案內容
            File outputFile = new File("test_output/" + xmlFileName.toString());
            String fileContent = readFileContent(outputFile);
            
            // 驗證 AP 標籤被正確移除，但內容保留
            assertFalse("不應包含 ns0:AP 開始標籤", fileContent.contains("<ns0:AP"));
            assertFalse("不應包含 ns0:AP 結束標籤", fileContent.contains("</ns0:AP>"));
            assertTrue("應包含 AP 內部的實際內容", fileContent.contains("<ManageTerminal>"));
            assertTrue("應包含回應的實際內容", fileContent.contains("<ManageTerminalResponse>"));
        }
    }
    
    /**
     * 讀取檔案內容的工具方法
     */
    private String readFileContent(File file) throws Exception {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }
}
