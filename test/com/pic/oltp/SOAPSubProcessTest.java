package com.pic.oltp;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import java.io.ByteArrayInputStream;

import javax.jms.QueueConnection;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.pic.o2o.common.Utility;

/**
 * SOAP SubProcess 單元測試
 * 測試 SOAP 協議的功能和錯誤處理
 * 
 * <AUTHOR> OLTP System
 * @version 1.3
 */
@RunWith(MockitoJUnitRunner.class)
public class SOAPSubProcessTest {
    
    @Mock
    private QueueConnection mockConnection;
    
    private OLTP oltpInstance;
    private Element testRoot;
    private Boolean[] result;
    private StringBuffer errorMsg;
    private StringBuffer xmlfileName;
    
    @Before
    public void setUp() throws Exception {
        // 初始化測試物件
        oltpInstance = new OLTP();
        result = new Boolean[]{false};
        errorMsg = new StringBuffer();
        xmlfileName = new StringBuffer();
        
        // 建立測試用的 XML 結構
        String testXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<root>" +
                "<HEADER>" +
                "<FROM>POS001</FROM>" +
                "<TO>SOAP001</TO>" +
                "<TERMINO>TEST001</TERMINO>" +
                "<STATCODE></STATCODE>" +
                "<STATDESC></STATDESC>" +
                "</HEADER>" +
                "<AP>" +
                "<testData>測試資料</testData>" +
                "</AP>" +
                "</root>";
        
        Document doc = DocumentHelper.parseText(testXml);
        testRoot = doc.getRootElement();
        
        // 模擬 GlobalVariable
        GlobalVariable.HOST = "TEST_HOST";
        GlobalVariable.logTrans = "TEST_LOG";
    }
    
    @Test
    public void testSOAPSubProcessAP_Success() throws Exception {
        // 準備測試資料
        String target = "http://test.soap.service/endpoint";
        int timeout = 30;
        String apStr = "<testData>測試資料</testData>";
        String mockResponse = "{\"result\":\"success\",\"data\":\"test response\"}";
        
        // 模擬 SOAPService 回應
        try (MockedStatic<SOAPService> mockedSOAPService = Mockito.mockStatic(SOAPService.class)) {
            mockedSOAPService.when(() -> SOAPService.sendSOAPRequest(target, timeout, apStr))
                           .thenReturn(mockResponse);
            
            // 模擬 Utility
            try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
                mockedUtility.when(() -> Utility.getDateTime(anyString()))
                           .thenReturn("2024/01/01 12:00:00.000");
                mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                           .thenAnswer(invocation -> null);
                
                // 執行測試
                Element resultElement = oltpInstance.receiveXML(testRoot, mockConnection, result, errorMsg, xmlfileName);
                
                // 驗證結果
                assertNotNull("結果不應為 null", resultElement);
                assertTrue("處理應該成功", result[0]);
                assertEquals("AP 內容應該被更新", mockResponse, 
                           resultElement.element("AP").getText());
            }
        }
    }
    
    @Test
    public void testSOAPSubProcess_Success() throws Exception {
        // 準備測試資料
        String target = "http://test.soap.service/endpoint";
        int timeout = 30;
        String xmlStr = testRoot.asXML();
        String mockResponse = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<root>" +
                "<HEADER>" +
                "<FROM>SOAP001</FROM>" +
                "<TO>POS001</TO>" +
                "<TERMINO>TEST001</TERMINO>" +
                "<STATCODE>0000</STATCODE>" +
                "<STATDESC>成功</STATDESC>" +
                "</HEADER>" +
                "<AP>" +
                "<responseData>回應資料</responseData>" +
                "</AP>" +
                "</root>";
        
        // 模擬 SOAPService 回應
        try (MockedStatic<SOAPService> mockedSOAPService = Mockito.mockStatic(SOAPService.class)) {
            mockedSOAPService.when(() -> SOAPService.sendSOAPRequest(target, timeout, xmlStr))
                           .thenReturn(mockResponse);
            
            // 模擬 Utility
            try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
                mockedUtility.when(() -> Utility.getDateTime(anyString()))
                           .thenReturn("2024/01/01 12:00:00.000");
                mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                           .thenAnswer(invocation -> null);
                
                // 執行測試
                Element resultElement = oltpInstance.receiveXML(testRoot, mockConnection, result, errorMsg, xmlfileName);
                
                // 驗證結果
                assertNotNull("結果不應為 null", resultElement);
                assertTrue("處理應該成功", result[0]);
                assertEquals("狀態碼應該是 0000", "0000", 
                           resultElement.element("HEADER").elementText("STATCODE"));
            }
        }
    }
    
    @Test
    public void testSOAPSubProcess_Timeout() throws Exception {
        // 準備測試資料
        String target = "http://test.soap.service/endpoint";
        int timeout = 30;
        String xmlStr = testRoot.asXML();
        
        // 模擬 SOAPService 逾時
        try (MockedStatic<SOAPService> mockedSOAPService = Mockito.mockStatic(SOAPService.class)) {
            mockedSOAPService.when(() -> SOAPService.sendSOAPRequest(target, timeout, xmlStr))
                           .thenThrow(new java.net.SocketTimeoutException("SOAP 請求逾時"));
            
            // 模擬 Utility
            try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
                mockedUtility.when(() -> Utility.getDateTime(anyString()))
                           .thenReturn("2024/01/01 12:00:00.000");
                mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                           .thenAnswer(invocation -> null);
                
                // 執行測試
                Element resultElement = oltpInstance.receiveXML(testRoot, mockConnection, result, errorMsg, xmlfileName);
                
                // 驗證結果
                assertNotNull("結果不應為 null", resultElement);
                assertFalse("處理應該失敗", result[0]);
                assertEquals("狀態碼應該是 1001", "1001", 
                           resultElement.element("HEADER").elementText("STATCODE"));
                assertEquals("狀態描述應該是系統繁忙", "系統繁忙", 
                           resultElement.element("HEADER").elementText("STATDESC"));
            }
        }
    }
    
    @Test
    public void testSOAPSubProcess_ConnectionError() throws Exception {
        // 準備測試資料
        String target = "http://test.soap.service/endpoint";
        int timeout = 30;
        String xmlStr = testRoot.asXML();
        
        // 模擬 SOAPService 連線錯誤
        try (MockedStatic<SOAPService> mockedSOAPService = Mockito.mockStatic(SOAPService.class)) {
            mockedSOAPService.when(() -> SOAPService.sendSOAPRequest(target, timeout, xmlStr))
                           .thenThrow(new java.net.ConnectException("SOAP 服務連線失敗"));
            
            // 模擬 Utility
            try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
                mockedUtility.when(() -> Utility.getDateTime(anyString()))
                           .thenReturn("2024/01/01 12:00:00.000");
                mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                           .thenAnswer(invocation -> null);
                
                // 執行測試
                Element resultElement = oltpInstance.receiveXML(testRoot, mockConnection, result, errorMsg, xmlfileName);
                
                // 驗證結果
                assertNotNull("結果不應為 null", resultElement);
                assertFalse("處理應該失敗", result[0]);
                assertEquals("狀態碼應該是 2001", "2001", 
                           resultElement.element("HEADER").elementText("STATCODE"));
                assertEquals("狀態描述應該是系統連線失敗", "系統連線失敗", 
                           resultElement.element("HEADER").elementText("STATDESC"));
            }
        }
    }
    
    @Test
    public void testSOAPSubProcess_GeneralError() throws Exception {
        // 準備測試資料
        String target = "http://test.soap.service/endpoint";
        int timeout = 30;
        String xmlStr = testRoot.asXML();
        
        // 模擬 SOAPService 一般錯誤
        try (MockedStatic<SOAPService> mockedSOAPService = Mockito.mockStatic(SOAPService.class)) {
            mockedSOAPService.when(() -> SOAPService.sendSOAPRequest(target, timeout, xmlStr))
                           .thenThrow(new RuntimeException("SOAP 處理錯誤"));
            
            // 模擬 Utility
            try (MockedStatic<Utility> mockedUtility = Mockito.mockStatic(Utility.class)) {
                mockedUtility.when(() -> Utility.getDateTime(anyString()))
                           .thenReturn("2024/01/01 12:00:00.000");
                mockedUtility.when(() -> Utility.writeLog(anyString(), any(StringBuffer.class)))
                           .thenAnswer(invocation -> null);
                
                // 執行測試
                Element resultElement = oltpInstance.receiveXML(testRoot, mockConnection, result, errorMsg, xmlfileName);
                
                // 驗證結果
                assertNotNull("結果不應為 null", resultElement);
                assertFalse("處理應該失敗", result[0]);
                assertEquals("狀態碼應該是 3001", "3001", 
                           resultElement.element("HEADER").elementText("STATCODE"));
                assertEquals("狀態描述應該是交易處理失敗", "交易處理失敗", 
                           resultElement.element("HEADER").elementText("STATDESC"));
            }
        }
    }
    
    @Test
    public void testSOAPService_ValidateService() {
        // 測試 SOAP 服務驗證功能
        String serviceUrl = "http://test.soap.service/endpoint";
        int timeout = 10;
        
        // 模擬 SOAPService
        try (MockedStatic<SOAPService> mockedSOAPService = Mockito.mockStatic(SOAPService.class)) {
            mockedSOAPService.when(() -> SOAPService.validateSOAPService(serviceUrl, timeout))
                           .thenReturn(true);
            
            // 執行測試
            boolean isValid = SOAPService.validateSOAPService(serviceUrl, timeout);
            
            // 驗證結果
            assertTrue("服務應該有效", isValid);
        }
    }
}
