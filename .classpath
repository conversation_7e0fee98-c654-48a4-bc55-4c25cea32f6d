<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="lib" path="libs/apache-log4j-extras-1.2.17.jar"/>
	<classpathentry kind="lib" path="libs/commons-beanutils-core-1.8.0.jar"/>
	<classpathentry kind="lib" path="libs/commons-codec-1.8.jar"/>
	<classpathentry kind="lib" path="libs/commons-io-2.4.jar"/>
	<classpathentry kind="lib" path="libs/commons-lang-2.6.jar"/>
	<classpathentry kind="lib" path="libs/commons-logging-1.1.1.jar"/>
	<classpathentry kind="lib" path="libs/commons.collections-3.2.1.jar"/>
	<classpathentry kind="lib" path="libs/ezmorph-1.0.6.jar"/>
	<classpathentry kind="lib" path="libs/java-json.jar"/>
	<classpathentry kind="lib" path="libs/jaxen-1.1.jar"/>
	<classpathentry kind="lib" path="libs/json-lib-2.4-jdk15.jar"/>
	<classpathentry kind="lib" path="libs/xom-1.2.8.jar"/>
	<classpathentry kind="lib" path="libs/commons-configuration-1.10.jar"/>
	<classpathentry kind="lib" path="libs/joda-time-2.6.jar"/>
	<classpathentry kind="lib" path="libs/tibjms.jar"/>
	<classpathentry kind="lib" path="/OLTPAPApi_v1.3/libs/dom4j-1.6.1.jar"/>
	<classpathentry kind="lib" path="/OLTPAPApi_v1.3/libs/httpclient-4.5.3.jar"/>
	<classpathentry kind="lib" path="/OLTPAPApi_v1.3/libs/httpcore-4.4.6.jar"/>
	<classpathentry kind="lib" path="/OLTPAPApi_v1.3/libs/jms.jar"/>
	<classpathentry combineaccessrules="false" kind="src" path="/OLTPAPApi_v1.3"/>
	<classpathentry kind="lib" path="/OLTPAPApi_v1.3/libs/jms-2.0.jar"/>
	<classpathentry kind="lib" path="libs/slf4j-api-1.5.2.jar"/>
	<classpathentry kind="lib" path="libs/slf4j-log4j12-1.5.2.jar"/>
	<classpathentry kind="lib" path="libs/slf4j-simple-1.5.2.jar"/>
	<classpathentry kind="lib" path="libs/TIBCrypt.jar"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER">
		<attributes>
			<attribute name="module" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="libs/log4j-1.2.15.jar"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
