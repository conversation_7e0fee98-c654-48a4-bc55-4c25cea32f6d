@echo off
echo ===== SOAP 命名空間連結修復測試 =====
echo.
echo 測試目標：修復 "m:ManageTerminal 前置碼 m 未連結" 錯誤
echo 修復內容：
echo   1. 在 transformToSOAPFormat 中添加命名空間定義
echo   2. 改進 addTransformedContentToBody 的錯誤處理
echo   3. 添加 addDOMElementToSOAPBody 方法
echo   4. 確保所有 XML 片段都包含必要的命名空間宣告
echo ==========================================
echo.

REM 設定 CLASSPATH
set CLASSPATH=libs\*;bin

echo 1. 檢查必要目錄...
if not exist "bin" mkdir bin
if not exist "bin\com\pic\o2o\common" mkdir bin\com\pic\o2o\common

echo.
echo 2. 編譯修復後的程式碼...

echo 編譯 SOAPClient.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\SOAPClient.java
if %ERRORLEVEL% neq 0 (
    echo ❌ SOAPClient.java 編譯失敗
    echo 可能是命名空間連結修復代碼有問題
    goto :error
) else (
    echo ✅ SOAPClient.java 編譯成功
)

echo.
echo 3. 創建命名空間連結測試程式...

REM 創建測試程式
echo import com.pic.o2o.common.SOAPClient; > TestNamespaceLinking.java
echo import java.lang.reflect.Method; >> TestNamespaceLinking.java
echo. >> TestNamespaceLinking.java
echo public class TestNamespaceLinking { >> TestNamespaceLinking.java
echo     public static void main(String[] args) { >> TestNamespaceLinking.java
echo         try { >> TestNamespaceLinking.java
echo             System.out.println("=== SOAP 命名空間連結修復測試 ==="); >> TestNamespaceLinking.java
echo             System.out.println(); >> TestNamespaceLinking.java
echo. >> TestNamespaceLinking.java
echo             // 測試 XML 內容（模擬實際的業務請求） >> TestNamespaceLinking.java
echo             String testXML = "^<ManageTerminal^>" + >> TestNamespaceLinking.java
echo                              "^<manageTerminalRequest^>" + >> TestNamespaceLinking.java
echo                              "^<Channel^>Test^</Channel^>" + >> TestNamespaceLinking.java
echo                              "^<Checksum^>B43DB40CD926E971464816D781CFF69B^</Checksum^>" + >> TestNamespaceLinking.java
echo                              "^<ManageTerminalDateTime^>20151015105959^</ManageTerminalDateTime^>" + >> TestNamespaceLinking.java
echo                              "^<ManageType^>101^</ManageType^>" + >> TestNamespaceLinking.java
echo                              "^<MerchantCode^>000000000000038^</MerchantCode^>" + >> TestNamespaceLinking.java
echo                              "^<ProgramCode^>00001^</ProgramCode^>" + >> TestNamespaceLinking.java
echo                              "^<ShopCode^>0000001028^</ShopCode^>" + >> TestNamespaceLinking.java
echo                              "^<TerminalCode^>^</TerminalCode^>" + >> TestNamespaceLinking.java
echo                              "^<TerminalSSN^>20151015105959000001^</TerminalSSN^>" + >> TestNamespaceLinking.java
echo                              "^</manageTerminalRequest^>" + >> TestNamespaceLinking.java
echo                              "^</ManageTerminal^>"; >> TestNamespaceLinking.java
echo. >> TestNamespaceLinking.java
echo             System.out.println("原始 XML 內容:"); >> TestNamespaceLinking.java
echo             System.out.println(testXML); >> TestNamespaceLinking.java
echo             System.out.println(); >> TestNamespaceLinking.java
echo. >> TestNamespaceLinking.java
echo             // 測試 transformToSOAPFormat 方法 >> TestNamespaceLinking.java
echo             SOAPClient client = new SOAPClient("https://test.example.com", 30); >> TestNamespaceLinking.java
echo             Method transformMethod = SOAPClient.class.getDeclaredMethod("transformToSOAPFormat", String.class); >> TestNamespaceLinking.java
echo             transformMethod.setAccessible(true); >> TestNamespaceLinking.java
echo. >> TestNamespaceLinking.java
echo             String transformed = (String) transformMethod.invoke(client, testXML); >> TestNamespaceLinking.java
echo             System.out.println("轉換後的 SOAP 格式:"); >> TestNamespaceLinking.java
echo             System.out.println(transformed); >> TestNamespaceLinking.java
echo             System.out.println(); >> TestNamespaceLinking.java
echo. >> TestNamespaceLinking.java
echo             // 檢查命名空間連結修復項目 >> TestNamespaceLinking.java
echo             System.out.println("=== 命名空間連結檢查 ==="); >> TestNamespaceLinking.java
echo. >> TestNamespaceLinking.java
echo             // 1. 檢查 m 命名空間定義 >> TestNamespaceLinking.java
echo             if (transformed.contains("xmlns:m=\"http://ticketxpress.com.tw/\"")) { >> TestNamespaceLinking.java
echo                 System.out.println("✅ m 命名空間已正確定義"); >> TestNamespaceLinking.java
echo             } else { >> TestNamespaceLinking.java
echo                 System.out.println("❌ m 命名空間定義缺失"); >> TestNamespaceLinking.java
echo             } >> TestNamespaceLinking.java
echo. >> TestNamespaceLinking.java
echo             // 2. 檢查 m0 命名空間定義 >> TestNamespaceLinking.java
echo             if (transformed.contains("xmlns:m0=\"http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common\"")) { >> TestNamespaceLinking.java
echo                 System.out.println("✅ m0 命名空間已正確定義"); >> TestNamespaceLinking.java
echo             } else { >> TestNamespaceLinking.java
echo                 System.out.println("❌ m0 命名空間定義缺失"); >> TestNamespaceLinking.java
echo             } >> TestNamespaceLinking.java
echo. >> TestNamespaceLinking.java
echo             // 3. 檢查 ManageTerminal 前綴 >> TestNamespaceLinking.java
echo             if (transformed.contains("^<m:ManageTerminal")) { >> TestNamespaceLinking.java
echo                 System.out.println("✅ ManageTerminal 使用正確的 m: 前綴"); >> TestNamespaceLinking.java
echo             } else { >> TestNamespaceLinking.java
echo                 System.out.println("❌ ManageTerminal 前綴錯誤"); >> TestNamespaceLinking.java
echo             } >> TestNamespaceLinking.java
echo. >> TestNamespaceLinking.java
echo             // 4. 測試 XML 有效性（包含命名空間） >> TestNamespaceLinking.java
echo             try { >> TestNamespaceLinking.java
echo                 javax.xml.parsers.DocumentBuilderFactory factory = javax.xml.parsers.DocumentBuilderFactory.newInstance(); >> TestNamespaceLinking.java
echo                 factory.setNamespaceAware(true); >> TestNamespaceLinking.java
echo                 javax.xml.parsers.DocumentBuilder builder = factory.newDocumentBuilder(); >> TestNamespaceLinking.java
echo                 java.io.ByteArrayInputStream inputStream = new java.io.ByteArrayInputStream(transformed.getBytes()); >> TestNamespaceLinking.java
echo                 builder.parse(inputStream); >> TestNamespaceLinking.java
echo                 System.out.println("✅ 轉換後的 XML 格式有效（命名空間已正確連結）"); >> TestNamespaceLinking.java
echo             } catch (Exception e) { >> TestNamespaceLinking.java
echo                 System.out.println("❌ 轉換後的 XML 格式無效: " + e.getMessage()); >> TestNamespaceLinking.java
echo                 if (e.getMessage().contains("未連結") ^|^| e.getMessage().contains("not linked")) { >> TestNamespaceLinking.java
echo                     System.out.println("   🔍 仍有命名空間連結問題"); >> TestNamespaceLinking.java
echo                 } >> TestNamespaceLinking.java
echo             } >> TestNamespaceLinking.java
echo. >> TestNamespaceLinking.java
echo             System.out.println(); >> TestNamespaceLinking.java
echo             System.out.println("=== 測試完成 ==="); >> TestNamespaceLinking.java
echo. >> TestNamespaceLinking.java
echo         } catch (Exception e) { >> TestNamespaceLinking.java
echo             System.out.println("測試失敗: " + e.getMessage()); >> TestNamespaceLinking.java
echo             e.printStackTrace(); >> TestNamespaceLinking.java
echo         } >> TestNamespaceLinking.java
echo     } >> TestNamespaceLinking.java
echo } >> TestNamespaceLinking.java

echo 編譯測試程式...
javac -cp "%CLASSPATH%" TestNamespaceLinking.java
if %ERRORLEVEL% equ 0 (
    echo.
    echo 4. 執行命名空間連結修復測試...
    echo.
    java -cp "%CLASSPATH%;." TestNamespaceLinking
) else (
    echo ⚠️ 測試程式編譯失敗，跳過測試
)

echo.
echo 5. 修復前後對比分析...
echo.
echo 修復前的問題：
echo   ❌ transformToSOAPFormat 生成: ^<m:ManageTerminal^>...^</m:ManageTerminal^>
echo   ❌ 缺少命名空間定義，導致 XML 解析器無法解析 m: 前綴
echo   ❌ 錯誤: "元素 m:ManageTerminal 的前置碼 m 未連結"
echo   ❌ 觸發 fallback 機制，使用文字節點
echo.
echo 修復後的正確格式：
echo   ✅ transformToSOAPFormat 生成: 
echo      ^<m:ManageTerminal xmlns:m="http://ticketxpress.com.tw/" 
echo                        xmlns:m0="http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common"^>
echo        ...
echo      ^</m:ManageTerminal^>
echo   ✅ 包含完整的命名空間定義
echo   ✅ XML 解析器可以正確解析所有前綴
echo   ✅ 不再觸發 fallback 機制
echo.

echo 6. 預期的修復效果...
echo.
echo Console 輸出應該顯示：
echo   ✅ m 命名空間已正確定義
echo   ✅ m0 命名空間已正確定義  
echo   ✅ ManageTerminal 使用正確的 m: 前綴
echo   ✅ 轉換後的 XML 格式有效（命名空間已正確連結）
echo   ✅ XML 解析成功，根元素: ManageTerminal
echo   ✅ 內容已成功添加到 SOAP Body
echo.
echo 不應該再看到：
echo   ❌ "元素 m:ManageTerminal 的前置碼 m 未連結"
echo   ❌ "Fallback: Adding content as text node"
echo   ❌ "SOAP Fault: The server was unable to process the request"
echo.

echo ==========================================
echo.
echo ✅ SOAP 命名空間連結修復完成
echo.
echo 主要修復項目：
echo   1. ✅ transformToSOAPFormat 添加命名空間定義
echo   2. ✅ 改進 addTransformedContentToBody 錯誤處理
echo   3. ✅ 添加 addDOMElementToSOAPBody 方法
echo   4. ✅ 確保 XML 片段自包含命名空間定義
echo   5. ✅ 增強調試資訊和錯誤診斷
echo.
echo 下一步：
echo 1. 重啟應用程式
echo 2. 重新測試 SOAP 連線
echo 3. 檢查是否不再出現命名空間連結錯誤
echo 4. 驗證服務端是否能正確處理請求
echo.
goto :end

:error
echo.
echo ❌ 命名空間連結修復測試失敗
echo 請檢查編譯錯誤並修正問題
echo.
exit /b 1

:end
echo 如需協助，請檢查 Console 輸出中的詳細命名空間資訊
if exist "TestNamespaceLinking.java" del "TestNamespaceLinking.java"
if exist "TestNamespaceLinking.class" del "TestNamespaceLinking.class"
pause
