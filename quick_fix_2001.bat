@echo off
echo ===== SOAP 2001 錯誤快速修復 =====
echo.
echo 這個腳本將嘗試快速修復 "2001 系統連線失敗" 錯誤
echo 目標服務: https://stage-posapi2.tixpress.tw/POSProxyService.svc
echo.

set /p choice="是否要執行快速修復? (Y/N): "
if /i "%choice%" neq "Y" goto :end

echo.
echo 1. 檢查 Java 環境...
java -version
if %ERRORLEVEL% neq 0 (
    echo ❌ Java 未正確安裝
    goto :error
)

echo.
echo 2. 檢查網路連線...
echo 測試 DNS 解析...
nslookup stage-posapi2.tixpress.tw > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ DNS 解析失敗
    echo 💡 建議：檢查網路連線和 DNS 設定
    goto :network_issue
) else (
    echo ✅ DNS 解析正常
)

echo.
echo 3. 檢查 SSL 憑證...
echo 嘗試下載 SSL 憑證...

REM 使用 PowerShell 檢查 SSL 憑證
powershell -Command "try { $cert = [System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}; $request = [System.Net.WebRequest]::Create('https://stage-posapi2.tixpress.tw/POSProxyService.svc'); $response = $request.GetResponse(); Write-Host '✅ SSL 連線成功'; $response.Close() } catch { Write-Host '❌ SSL 連線失敗:' $_.Exception.Message }"

echo.
echo 4. 應用臨時修復...
echo.
echo 正在修改 OLTP.java 以跳過 SSL 憑證驗證...

REM 備份原始檔案
if exist "src\com\pic\oltp\OLTP.java" (
    copy "src\com\pic\oltp\OLTP.java" "src\com\pic\oltp\OLTP.java.backup" > nul
    echo ✅ 已備份原始檔案
) else (
    echo ❌ 找不到 OLTP.java 檔案
    goto :error
)

REM 檢查是否已經應用過修復
findstr "setupTrustAllCertificates" "src\com\pic\oltp\OLTP.java" > nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ⚠️ 修復已經應用過
    goto :already_fixed
)

echo.
echo 正在應用 SSL 憑證跳過修復...

REM 創建修復用的 Java 程式碼片段
echo // 臨時解決方案：跳過 SSL 憑證驗證（僅用於測試） > temp_fix.txt
echo if (target.startsWith("https://")) { >> temp_fix.txt
echo     try { >> temp_fix.txt
echo         SSLCertificateHelper.setupTrustAllCertificates(); >> temp_fix.txt
echo         System.out.println("⚠️ 已啟用 SSL 憑證跳過模式（僅用於測試）"); >> temp_fix.txt
echo     } catch (Exception sslEx) { >> temp_fix.txt
echo         System.out.println("⚠️ SSL 設定失敗: " + sslEx.getMessage()); >> temp_fix.txt
echo     } >> temp_fix.txt
echo } >> temp_fix.txt

echo ✅ 修復程式碼已準備

echo.
echo 5. 重新編譯...
set CLASSPATH=libs\*;bin

echo 編譯 SSLCertificateHelper...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\SSLCertificateHelper.java
if %ERRORLEVEL% neq 0 (
    echo ❌ SSLCertificateHelper 編譯失敗
    goto :error
)

echo 編譯 OLTP...
javac -cp "%CLASSPATH%" -d bin src\com\pic\oltp\OLTP.java
if %ERRORLEVEL% neq 0 (
    echo ❌ OLTP 編譯失敗
    echo 💡 可能需要手動修改 OLTP.java
    goto :manual_fix
)

echo ✅ 編譯成功

echo.
echo 6. 測試修復結果...
echo.
echo 執行 SOAP 連線測試...

REM 編譯並執行測試
javac -cp "%CLASSPATH%" -d . diagnose_soap_connection.java
if %ERRORLEVEL% equ 0 (
    java -cp "%CLASSPATH%;." diagnose_soap_connection
) else (
    echo ⚠️ 無法執行自動測試，請手動測試
)

echo.
echo ===== 快速修復完成 =====
echo.
echo ✅ 已應用臨時修復（跳過 SSL 憑證驗證）
echo ⚠️ 這是臨時解決方案，建議後續進行永久修復
echo.
echo 下一步建議：
echo 1. 測試 SOAP 功能是否正常
echo 2. 如果正常，說明問題是 SSL 憑證
echo 3. 聯絡系統管理員添加正確的 CA 憑證
echo 4. 完成永久修復後，移除臨時修復程式碼
echo.
goto :end

:network_issue
echo.
echo ===== 網路問題診斷 =====
echo.
echo 檢測到網路連線問題，請檢查：
echo 1. 網路連線是否正常
echo 2. DNS 設定是否正確
echo 3. 防火牆是否阻擋連線
echo 4. 代理伺服器設定是否正確
echo.
echo 建議執行：
echo - ipconfig /flushdns
echo - nslookup *******
echo - ping google.com
echo.
goto :end

:already_fixed
echo.
echo ===== 修復狀態檢查 =====
echo.
echo 修復已經應用過，正在檢查狀態...
echo.
echo 如果問題仍然存在，可能的原因：
echo 1. 修復未正確應用
echo 2. 需要重新編譯和重啟
echo 3. 問題不是 SSL 憑證相關
echo.
echo 建議執行完整診斷：
echo diagnose_2001_error.bat
echo.
goto :end

:manual_fix
echo.
echo ===== 手動修復指南 =====
echo.
echo 自動修復失敗，請手動執行以下步驟：
echo.
echo 1. 編輯 src\com\pic\oltp\OLTP.java
echo 2. 在 SOAP_SubProcess 方法開始處添加：
echo.
echo    // 臨時解決方案：跳過 SSL 憑證驗證
echo    if (target.startsWith("https://")) {
echo        SSLCertificateHelper.setupTrustAllCertificates();
echo        System.out.println("⚠️ 已啟用 SSL 憑證跳過模式");
echo    }
echo.
echo 3. 重新編譯：javac -cp "libs/*;bin" -d bin src\com\pic\oltp\OLTP.java
echo 4. 重啟應用程式測試
echo.
goto :end

:error
echo.
echo ❌ 快速修復失敗
echo.
echo 請執行完整診斷：diagnose_2001_error.bat
echo 或參考修復指南：docs\SOAP_2001_Error_Fix_Guide.md
echo.
exit /b 1

:end
echo.
echo 如需進一步協助，請聯絡技術支援團隊
echo 並提供診斷結果和錯誤日誌
echo.
if exist "temp_fix.txt" del "temp_fix.txt"
pause
