@echo off
echo ===== 命名空間移除修復測試 =====
echo.
echo 測試目標：修復 removeNamespaces 方法過度移除內容的問題
echo 問題：正則表達式錯誤地移除了結束標籤中的正斜線 "/"
echo 修復內容：
echo   1. 修改正則表達式，只移除實際的命名空間前綴
echo   2. 精確匹配 prefix:tagname 格式，保留 XML 結構
echo   3. 添加 XML 結構完整性驗證
echo   4. 確保結果 XML 可以正確解析
echo ==========================================
echo.

REM 設定 CLASSPATH
set CLASSPATH=libs\*;bin

echo 1. 檢查必要目錄...
if not exist "bin" mkdir bin
if not exist "bin\com\pic\o2o\common" mkdir bin\com\pic\o2o\common

echo.
echo 2. 編譯修復後的程式碼...

echo 編譯 QueueListener.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\QueueListener.java
if %ERRORLEVEL% neq 0 (
    echo ❌ QueueListener.java 編譯失敗
    echo 可能是命名空間移除修復代碼有問題
    goto :error
) else (
    echo ✅ QueueListener.java 編譯成功
)

echo.
echo 3. 創建命名空間移除修復測試程式...

REM 創建測試程式
echo import com.pic.o2o.common.QueueListener; > TestNamespaceFix.java
echo import java.lang.reflect.Method; >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo public class TestNamespaceFix { >> TestNamespaceFix.java
echo     public static void main(String[] args) { >> TestNamespaceFix.java
echo         try { >> TestNamespaceFix.java
echo             System.out.println("=== 命名空間移除修復測試 ==="); >> TestNamespaceFix.java
echo             System.out.println(); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             // 創建 QueueListener 實例 >> TestNamespaceFix.java
echo             QueueListener listener = new QueueListener(); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             // 測試案例 1：有命名空間前綴的 XML >> TestNamespaceFix.java
echo             System.out.println("=== 測試案例 1：有命名空間前綴的 XML ==="); >> TestNamespaceFix.java
echo             String xmlWithNamespaces = "^<ns0:ManageTerminalResponse xmlns:ns0=\"http://ticketxpress.com.tw/\"^>" + >> TestNamespaceFix.java
echo                                       "^<ns0:ManageTerminalResult^>" + >> TestNamespaceFix.java
echo                                       "^<ns0:Checksum^>a87519574d731f03955b107379c9c5be^</ns0:Checksum^>" + >> TestNamespaceFix.java
echo                                       "^<ns0:Message^>Success^</ns0:Message^>" + >> TestNamespaceFix.java
echo                                       "^<ns0:ResponseCode^>0000^</ns0:ResponseCode^>" + >> TestNamespaceFix.java
echo                                       "^<ns0:ServerDate^>20250729^</ns0:ServerDate^>" + >> TestNamespaceFix.java
echo                                       "^<ns0:ServerTime^>151026^</ns0:ServerTime^>" + >> TestNamespaceFix.java
echo                                       "^<ns0:WorkKey^>eK+DO15GrIpkI/2muc5i3YryM2wrzTMYQQfoSELTw/833SOEtTXwSQ==^</ns0:WorkKey^>" + >> TestNamespaceFix.java
echo                                       "^</ns0:ManageTerminalResult^>" + >> TestNamespaceFix.java
echo                                       "^</ns0:ManageTerminalResponse^>"; >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             testRemoveNamespaces(listener, xmlWithNamespaces, "有命名空間前綴"); >> TestNamespaceFix.java
echo             System.out.println(); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             // 測試案例 2：沒有命名空間前綴的 XML >> TestNamespaceFix.java
echo             System.out.println("=== 測試案例 2：沒有命名空間前綴的 XML ==="); >> TestNamespaceFix.java
echo             String xmlWithoutNamespaces = "^<ManageTerminalResponse^>" + >> TestNamespaceFix.java
echo                                          "^<ManageTerminalResult^>" + >> TestNamespaceFix.java
echo                                          "^<Checksum^>a87519574d731f03955b107379c9c5be^</Checksum^>" + >> TestNamespaceFix.java
echo                                          "^<Message^>Success^</Message^>" + >> TestNamespaceFix.java
echo                                          "^<ResponseCode^>0000^</ResponseCode^>" + >> TestNamespaceFix.java
echo                                          "^<ServerDate^>20250729^</ServerDate^>" + >> TestNamespaceFix.java
echo                                          "^<ServerTime^>151026^</ServerTime^>" + >> TestNamespaceFix.java
echo                                          "^<WorkKey^>eK+DO15GrIpkI/2muc5i3YryM2wrzTMYQQfoSELTw/833SOEtTXwSQ==^</WorkKey^>" + >> TestNamespaceFix.java
echo                                          "^</ManageTerminalResult^>" + >> TestNamespaceFix.java
echo                                          "^</ManageTerminalResponse^>"; >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             testRemoveNamespaces(listener, xmlWithoutNamespaces, "沒有命名空間前綴"); >> TestNamespaceFix.java
echo             System.out.println(); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             // 測試案例 3：混合命名空間的 XML >> TestNamespaceFix.java
echo             System.out.println("=== 測試案例 3：混合命名空間的 XML ==="); >> TestNamespaceFix.java
echo             String xmlMixed = "^<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\"^>" + >> TestNamespaceFix.java
echo                              "^<s:Body^>" + >> TestNamespaceFix.java
echo                              "^<ns0:ManageTerminalResponse xmlns:ns0=\"http://ticketxpress.com.tw/\"^>" + >> TestNamespaceFix.java
echo                              "^<ns0:ManageTerminalResult^>" + >> TestNamespaceFix.java
echo                              "^<ns0:Checksum^>a87519574d731f03955b107379c9c5be^</ns0:Checksum^>" + >> TestNamespaceFix.java
echo                              "^<ns0:Message^>Success^</ns0:Message^>" + >> TestNamespaceFix.java
echo                              "^</ns0:ManageTerminalResult^>" + >> TestNamespaceFix.java
echo                              "^</ns0:ManageTerminalResponse^>" + >> TestNamespaceFix.java
echo                              "^</s:Body^>" + >> TestNamespaceFix.java
echo                              "^</s:Envelope^>"; >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             testRemoveNamespaces(listener, xmlMixed, "混合命名空間"); >> TestNamespaceFix.java
echo             System.out.println(); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             // 測試案例 4：複雜屬性的 XML >> TestNamespaceFix.java
echo             System.out.println("=== 測試案例 4：複雜屬性的 XML ==="); >> TestNamespaceFix.java
echo             String xmlWithAttributes = "^<ns0:Response xmlns:ns0=\"http://test.com\" ns0:version=\"1.0\"^>" + >> TestNamespaceFix.java
echo                                       "^<ns0:Data ns0:type=\"string\"^>Test Value^</ns0:Data^>" + >> TestNamespaceFix.java
echo                                       "^<ns0:Status^>Success^</ns0:Status^>" + >> TestNamespaceFix.java
echo                                       "^</ns0:Response^>"; >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             testRemoveNamespaces(listener, xmlWithAttributes, "複雜屬性"); >> TestNamespaceFix.java
echo             System.out.println(); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             System.out.println("=== 測試完成 ==="); >> TestNamespaceFix.java
echo             System.out.println("✅ 命名空間移除修復測試完成"); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo         } catch (Exception e) { >> TestNamespaceFix.java
echo             System.out.println("❌ 測試失敗: " + e.getMessage()); >> TestNamespaceFix.java
echo             e.printStackTrace(); >> TestNamespaceFix.java
echo         } >> TestNamespaceFix.java
echo     } >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo     private static void testRemoveNamespaces(QueueListener listener, String xmlContent, String testName) { >> TestNamespaceFix.java
echo         try { >> TestNamespaceFix.java
echo             System.out.println("測試類型: " + testName); >> TestNamespaceFix.java
echo             System.out.println("原始 XML: " + xmlContent); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             Method method = QueueListener.class.getDeclaredMethod("removeNamespaces", String.class); >> TestNamespaceFix.java
echo             method.setAccessible(true); >> TestNamespaceFix.java
echo             String result = (String) method.invoke(listener, xmlContent); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             System.out.println("清理後 XML: " + result); >> TestNamespaceFix.java
echo             System.out.println("原始長度: " + xmlContent.length()); >> TestNamespaceFix.java
echo             System.out.println("清理後長度: " + result.length()); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             // 檢查結果 >> TestNamespaceFix.java
echo             boolean hasNamespacePrefix = result.contains("ns0:") ^|^| result.contains("s:"); >> TestNamespaceFix.java
echo             boolean hasNamespaceDeclaration = result.contains("xmlns:"); >> TestNamespaceFix.java
echo             boolean hasValidCloseTags = result.contains("^</") ^&^& !result.contains("^<Checksum^>") ^&^& !result.contains("^<Message^>"); >> TestNamespaceFix.java
echo             boolean hasProperStructure = result.contains("^</Checksum^>") ^&^& result.contains("^</Message^>"); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             System.out.println("包含命名空間前綴: " + hasNamespacePrefix); >> TestNamespaceFix.java
echo             System.out.println("包含命名空間宣告: " + hasNamespaceDeclaration); >> TestNamespaceFix.java
echo             System.out.println("有效的結束標籤: " + hasValidCloseTags); >> TestNamespaceFix.java
echo             System.out.println("正確的結構: " + hasProperStructure); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             boolean isSuccess = !hasNamespacePrefix ^&^& !hasNamespaceDeclaration ^&^& hasValidCloseTags ^&^& hasProperStructure; >> TestNamespaceFix.java
echo             System.out.println("測試結果: " + (isSuccess ? "✅ 成功" : "❌ 失敗")); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             // 驗證 XML 結構完整性 >> TestNamespaceFix.java
echo             Method validateMethod = QueueListener.class.getDeclaredMethod("validateXMLStructure", String.class); >> TestNamespaceFix.java
echo             validateMethod.setAccessible(true); >> TestNamespaceFix.java
echo             Boolean structureValid = (Boolean) validateMethod.invoke(listener, result); >> TestNamespaceFix.java
echo             System.out.println("結構驗證: " + (structureValid ? "✅ 有效" : "❌ 無效")); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo         } catch (Exception e) { >> TestNamespaceFix.java
echo             System.out.println("❌ " + testName + " 測試失敗: " + e.getMessage()); >> TestNamespaceFix.java
echo             e.printStackTrace(); >> TestNamespaceFix.java
echo         } >> TestNamespaceFix.java
echo     } >> TestNamespaceFix.java
echo } >> TestNamespaceFix.java

echo 編譯測試程式...
javac -cp "%CLASSPATH%" TestNamespaceFix.java
if %ERRORLEVEL% equ 0 (
    echo.
    echo 4. 執行命名空間移除修復測試...
    echo.
    java -cp "%CLASSPATH%;." TestNamespaceFix
) else (
    echo ⚠️ 測試程式編譯失敗，跳過測試
)

echo.
echo 5. 修復前後對比分析...
echo.
echo 修復前的問題：
echo   ❌ 正則表達式 "^</[^^>]*?:" 錯誤匹配
echo   ❌ 結束標籤中的正斜線 "/" 被移除
echo   ❌ XML 結構損壞：^<Checksum^>...^<Checksum^>
echo   ❌ 無法正確解析處理後的 XML
echo.
echo 修復後的改進：
echo   ✅ 精確的正則表達式：只匹配 prefix:tagname 格式
echo   ✅ 保留 XML 結構完整性
echo   ✅ 添加 XML 結構驗證
echo   ✅ 確保結果 XML 可以正確解析
echo   ✅ 詳細的調試資訊和錯誤處理
echo.

echo 6. 預期效果...
echo.
echo 修復前的錯誤輸出：
echo   ^<ManageTerminalResponse^>^<ManageTerminalResult^>^<Checksum^>a87519574d731f03955b107379c9c5be^<Checksum^>^<Message^>Success^<Message^>...
echo.
echo 修復後的正確輸出：
echo   ^<ManageTerminalResponse^>^<ManageTerminalResult^>^<Checksum^>a87519574d731f03955b107379c9c5be^</Checksum^>^<Message^>Success^</Message^>...
echo.
echo 測試結果應該顯示：
echo   ✅ 包含命名空間前綴: false
echo   ✅ 包含命名空間宣告: false
echo   ✅ 有效的結束標籤: true
echo   ✅ 正確的結構: true
echo   ✅ 測試結果: ✅ 成功
echo   ✅ 結構驗證: ✅ 有效
echo.

echo ==========================================
echo.
echo ✅ 命名空間移除修復完成
echo.
echo 主要修復項目：
echo   1. ✅ 修改正則表達式為精確匹配 prefix:tagname
echo   2. ✅ 分別處理開始標籤和結束標籤
echo   3. ✅ 添加 XML 結構完整性驗證
echo   4. ✅ 確保處理後的 XML 可以正確解析
echo   5. ✅ 改進錯誤處理和調試資訊
echo.
echo 下一步：
echo 1. 重啟應用程式
echo 2. 重新測試 SOAP 回應處理
echo 3. 檢查 AP 層內容是否正確（有效的結束標籤）
echo 4. 驗證 XML 解析是否成功
echo 5. 確認命名空間移除不影響 XML 結構
echo.
goto :end

:error
echo.
echo ❌ 命名空間移除修復測試失敗
echo 請檢查編譯錯誤並修正問題
echo.
exit /b 1

:end
echo 如需協助，請檢查 Console 輸出中的詳細測試結果
if exist "TestNamespaceFix.java" del "TestNamespaceFix.java"
if exist "TestNamespaceFix.class" del "TestNamespaceFix.class"
pause
