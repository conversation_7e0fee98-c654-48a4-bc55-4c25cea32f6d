@echo off
echo ===== SOAP XML 命名空間修復測試 =====
echo.
echo 測試目標：修復 "m0:Channel 前置碼未連結" 錯誤
echo 修復內容：
echo   1. 統一命名空間定義
echo   2. 改進 XML 轉換邏輯
echo   3. 增強錯誤處理機制
echo =====================================
echo.

REM 設定 CLASSPATH
set CLASSPATH=libs\*;bin

echo 1. 檢查必要目錄...
if not exist "bin" mkdir bin
if not exist "bin\com\pic\o2o\common" mkdir bin\com\pic\o2o\common

echo.
echo 2. 編譯修復後的程式碼...

echo 編譯 SOAPClient.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\SOAPClient.java
if %ERRORLEVEL% neq 0 (
    echo ❌ SOAPClient.java 編譯失敗
    echo 可能是命名空間修復代碼有問題
    goto :error
) else (
    echo ✅ SOAPClient.java 編譯成功
)

echo 編譯 SOAPXMLValidator.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\SOAPXMLValidator.java
if %ERRORLEVEL% neq 0 (
    echo ❌ SOAPXMLValidator.java 編譯失敗
    goto :error
) else (
    echo ✅ SOAPXMLValidator.java 編譯成功
)

echo 編譯 SOAPService.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\SOAPService.java
if %ERRORLEVEL% neq 0 (
    echo ❌ SOAPService.java 編譯失敗
    goto :error
) else (
    echo ✅ SOAPService.java 編譯成功
)

echo.
echo 3. 創建 XML 命名空間測試程式...

REM 創建測試程式
echo import com.pic.o2o.common.SOAPClient; > TestNamespaceFix.java
echo import java.lang.reflect.Method; >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo public class TestNamespaceFix { >> TestNamespaceFix.java
echo     public static void main(String[] args) { >> TestNamespaceFix.java
echo         try { >> TestNamespaceFix.java
echo             System.out.println("=== SOAP XML 命名空間修復測試 ==="); >> TestNamespaceFix.java
echo             System.out.println(); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             // 測試 XML 內容（模擬實際的業務請求） >> TestNamespaceFix.java
echo             String testXML = "^<ManageTerminal^>" + >> TestNamespaceFix.java
echo                              "^<manageTerminalRequest^>" + >> TestNamespaceFix.java
echo                              "^<Channel^>WEB^</Channel^>" + >> TestNamespaceFix.java
echo                              "^<TerminalId^>TEST001^</TerminalId^>" + >> TestNamespaceFix.java
echo                              "^</manageTerminalRequest^>" + >> TestNamespaceFix.java
echo                              "^</ManageTerminal^>"; >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             System.out.println("原始 XML 內容:"); >> TestNamespaceFix.java
echo             System.out.println(testXML); >> TestNamespaceFix.java
echo             System.out.println(); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             // 測試 transformToSOAPFormat 方法 >> TestNamespaceFix.java
echo             SOAPClient client = new SOAPClient("https://test.example.com", 30); >> TestNamespaceFix.java
echo             Method transformMethod = SOAPClient.class.getDeclaredMethod("transformToSOAPFormat", String.class); >> TestNamespaceFix.java
echo             transformMethod.setAccessible(true); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             String transformed = (String) transformMethod.invoke(client, testXML); >> TestNamespaceFix.java
echo             System.out.println("轉換後的 SOAP 格式:"); >> TestNamespaceFix.java
echo             System.out.println(transformed); >> TestNamespaceFix.java
echo             System.out.println(); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             // 檢查命名空間定義 >> TestNamespaceFix.java
echo             if (transformed.contains("xmlns:m0=\"http://ticketxpress.com.tw/\"")) { >> TestNamespaceFix.java
echo                 System.out.println("✅ m0 命名空間已正確定義"); >> TestNamespaceFix.java
echo             } else { >> TestNamespaceFix.java
echo                 System.out.println("❌ m0 命名空間定義缺失"); >> TestNamespaceFix.java
echo             } >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             if (transformed.contains("m0:Channel")) { >> TestNamespaceFix.java
echo                 System.out.println("✅ m0:Channel 前綴已添加"); >> TestNamespaceFix.java
echo             } else { >> TestNamespaceFix.java
echo                 System.out.println("⚠️ m0:Channel 前綴未找到"); >> TestNamespaceFix.java
echo             } >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             // 測試 XML 有效性 >> TestNamespaceFix.java
echo             try { >> TestNamespaceFix.java
echo                 javax.xml.parsers.DocumentBuilderFactory factory = javax.xml.parsers.DocumentBuilderFactory.newInstance(); >> TestNamespaceFix.java
echo                 factory.setNamespaceAware(true); >> TestNamespaceFix.java
echo                 javax.xml.parsers.DocumentBuilder builder = factory.newDocumentBuilder(); >> TestNamespaceFix.java
echo                 java.io.ByteArrayInputStream inputStream = new java.io.ByteArrayInputStream(transformed.getBytes()); >> TestNamespaceFix.java
echo                 builder.parse(inputStream); >> TestNamespaceFix.java
echo                 System.out.println("✅ 轉換後的 XML 格式有效"); >> TestNamespaceFix.java
echo             } catch (Exception e) { >> TestNamespaceFix.java
echo                 System.out.println("❌ 轉換後的 XML 格式無效: " + e.getMessage()); >> TestNamespaceFix.java
echo             } >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo             System.out.println(); >> TestNamespaceFix.java
echo             System.out.println("=== 測試完成 ==="); >> TestNamespaceFix.java
echo. >> TestNamespaceFix.java
echo         } catch (Exception e) { >> TestNamespaceFix.java
echo             System.out.println("測試失敗: " + e.getMessage()); >> TestNamespaceFix.java
echo             e.printStackTrace(); >> TestNamespaceFix.java
echo         } >> TestNamespaceFix.java
echo     } >> TestNamespaceFix.java
echo } >> TestNamespaceFix.java

echo 編譯測試程式...
javac -cp "%CLASSPATH%" TestNamespaceFix.java
if %ERRORLEVEL% equ 0 (
    echo.
    echo 4. 執行命名空間修復測試...
    echo.
    java -cp "%CLASSPATH%;." TestNamespaceFix
) else (
    echo ⚠️ 測試程式編譯失敗，跳過測試
)

echo.
echo 5. 檢查修復內容摘要...
echo.
echo 主要修復項目：
echo.
echo ✅ 1. 統一命名空間定義
echo    - 在 SOAP Envelope 中定義 xmlns:m0="http://ticketxpress.com.tw/"
echo    - 確保所有使用 m0: 前綴的元素都有對應的命名空間定義
echo.
echo ✅ 2. 改進 XML 轉換邏輯
echo    - 統一使用 m0: 前綴避免多種前綴造成混亂
echo    - 改進正則表達式避免重複添加前綴
echo    - 清理現有的命名空間前綴再重新添加
echo.
echo ✅ 3. 增強錯誤處理機制
echo    - 添加 Raw XML fallback 機制
echo    - 改進 DOM 到 SOAP 的轉換邏輯
echo    - 提供更詳細的錯誤診斷資訊
echo.
echo ✅ 4. 調試資訊增強
echo    - 添加命名空間設定的詳細日誌
echo    - 顯示 XML 轉換的每個步驟
echo    - 記錄 fallback 機制的使用情況
echo.

echo 6. 預期效果...
echo.
echo 修復前的錯誤：
echo   ❌ 元素 "m0:Channel" 的前置碼 "m0" 未連結
echo   ❌ SOAP Fault: The server was unable to process the request
echo.
echo 修復後的預期結果：
echo   ✅ XML 命名空間正確定義和使用
echo   ✅ SOAP 請求格式符合服務端要求
echo   ✅ 服務端能夠正確解析和處理請求
echo.

echo =====================================
echo.
echo ✅ SOAP XML 命名空間修復完成
echo.
echo 下一步：
echo 1. 重啟應用程式
echo 2. 重新測試 SOAP 連線
echo 3. 檢查是否還有 "前置碼未連結" 錯誤
echo 4. 驗證服務端是否能正確處理請求
echo.
goto :end

:error
echo.
echo ❌ 修復測試失敗
echo 請檢查編譯錯誤並修正問題
echo.
exit /b 1

:end
echo 如需協助，請檢查 Console 輸出中的詳細診斷資訊
if exist "TestNamespaceFix.java" del "TestNamespaceFix.java"
if exist "TestNamespaceFix.class" del "TestNamespaceFix.class"
pause
