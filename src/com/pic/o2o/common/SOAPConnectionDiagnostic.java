package com.pic.o2o.common;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;

import com.pic.oltp.GlobalVariable;

/**
 * SOAP 連線診斷工具
 * 提供全面的網路連線和 SOAP 服務診斷功能
 * 
 * <AUTHOR> OLTP System
 * @version 1.3
 */
public class SOAPConnectionDiagnostic {
    
    /**
     * 診斷結果類別
     */
    public static class DiagnosticResult {
        private boolean success = false;
        private String summary = "";
        private List<String> details = new ArrayList<>();
        private List<String> recommendations = new ArrayList<>();
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getSummary() { return summary; }
        public void setSummary(String summary) { this.summary = summary; }
        
        public List<String> getDetails() { return details; }
        public void addDetail(String detail) { this.details.add(detail); }
        
        public List<String> getRecommendations() { return recommendations; }
        public void addRecommendation(String recommendation) { this.recommendations.add(recommendation); }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("=== SOAP 連線診斷結果 ===\n");
            sb.append("狀態: ").append(success ? "✅ 成功" : "❌ 失敗").append("\n");
            sb.append("摘要: ").append(summary).append("\n\n");
            
            if (!details.isEmpty()) {
                sb.append("詳細資訊:\n");
                for (String detail : details) {
                    sb.append("  - ").append(detail).append("\n");
                }
                sb.append("\n");
            }
            
            if (!recommendations.isEmpty()) {
                sb.append("建議:\n");
                for (String rec : recommendations) {
                    sb.append("  • ").append(rec).append("\n");
                }
            }
            
            sb.append("========================");
            return sb.toString();
        }
    }
    
    /**
     * 執行完整的 SOAP 連線診斷
     * 
     * @param serviceUrl SOAP 服務 URL
     * @param timeout 逾時時間（秒）
     * @param testContent 測試用的 SOAP 內容（可為 null）
     * @return 診斷結果
     */
    public static DiagnosticResult diagnose(String serviceUrl, int timeout, String testContent) {
        DiagnosticResult result = new DiagnosticResult();
        
        System.out.println("=== 開始 SOAP 連線診斷 ===");
        System.out.println("目標 URL: " + serviceUrl);
        System.out.println("逾時設定: " + timeout + " 秒");
        System.out.println("==========================");
        
        try {
            // 1. URL 格式檢查
            if (!checkUrlFormat(serviceUrl, result)) {
                return result;
            }
            
            // 2. DNS 解析檢查
            if (!checkDnsResolution(serviceUrl, result)) {
                return result;
            }
            
            // 3. 網路連線檢查
            if (!checkNetworkConnectivity(serviceUrl, timeout, result)) {
                return result;
            }
            
            // 4. HTTP 連線檢查
            if (!checkHttpConnectivity(serviceUrl, timeout, result)) {
                return result;
            }
            
            // 5. SOAP 服務檢查
            if (testContent != null) {
                checkSoapService(serviceUrl, timeout, testContent, result);
            } else {
                result.addDetail("跳過 SOAP 服務測試（未提供測試內容）");
            }
            
            if (result.isSuccess()) {
                result.setSummary("所有診斷項目通過");
            }
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setSummary("診斷過程發生錯誤: " + e.getMessage());
            result.addDetail("錯誤詳情: " + e.toString());
        }
        
        System.out.println(result.toString());
        return result;
    }
    
    /**
     * 檢查 URL 格式
     */
    private static boolean checkUrlFormat(String serviceUrl, DiagnosticResult result) {
        try {
            if (serviceUrl == null || serviceUrl.trim().isEmpty()) {
                result.setSuccess(false);
                result.setSummary("URL 為空");
                result.addRecommendation("請檢查 transfile.txt 中的 SOAP 服務 URL 設定");
                return false;
            }
            
            URL url = new URL(serviceUrl);
            result.addDetail("URL 格式正確");
            result.addDetail("協議: " + url.getProtocol());
            result.addDetail("主機: " + url.getHost());
            result.addDetail("埠號: " + (url.getPort() != -1 ? url.getPort() : url.getDefaultPort()));
            result.addDetail("路徑: " + url.getPath());
            
            if (!"http".equalsIgnoreCase(url.getProtocol()) && !"https".equalsIgnoreCase(url.getProtocol())) {
                result.setSuccess(false);
                result.setSummary("不支援的協議: " + url.getProtocol());
                result.addRecommendation("請使用 HTTP 或 HTTPS 協議");
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setSummary("URL 格式錯誤: " + e.getMessage());
            result.addRecommendation("請檢查 URL 格式是否正確");
            return false;
        }
    }
    
    /**
     * 檢查 DNS 解析
     */
    private static boolean checkDnsResolution(String serviceUrl, DiagnosticResult result) {
        try {
            URL url = new URL(serviceUrl);
            String hostname = url.getHost();
            
            InetAddress address = InetAddress.getByName(hostname);
            result.addDetail("DNS 解析成功");
            result.addDetail("主機名稱: " + hostname);
            result.addDetail("IP 位址: " + address.getHostAddress());
            
            return true;
            
        } catch (UnknownHostException e) {
            result.setSuccess(false);
            result.setSummary("DNS 解析失敗: " + e.getMessage());
            result.addRecommendation("請檢查主機名稱是否正確");
            result.addRecommendation("請檢查 DNS 設定");
            result.addRecommendation("請檢查網路連線");
            return false;
        } catch (Exception e) {
            result.setSuccess(false);
            result.setSummary("DNS 檢查錯誤: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 檢查網路連線
     */
    private static boolean checkNetworkConnectivity(String serviceUrl, int timeout, DiagnosticResult result) {
        try {
            URL url = new URL(serviceUrl);
            String hostname = url.getHost();
            int port = url.getPort() != -1 ? url.getPort() : url.getDefaultPort();
            
            // 使用 Socket 測試連線
            java.net.Socket socket = new java.net.Socket();
            socket.connect(new java.net.InetSocketAddress(hostname, port), timeout * 1000);
            socket.close();
            
            result.addDetail("網路連線測試成功");
            result.addDetail("目標: " + hostname + ":" + port);
            
            return true;
            
        } catch (SocketTimeoutException e) {
            result.setSuccess(false);
            result.setSummary("網路連線逾時");
            result.addRecommendation("請檢查網路連線速度");
            result.addRecommendation("請考慮增加逾時時間");
            return false;
        } catch (ConnectException e) {
            result.setSuccess(false);
            result.setSummary("網路連線被拒絕");
            result.addRecommendation("請檢查目標服務是否啟動");
            result.addRecommendation("請檢查防火牆設定");
            result.addRecommendation("請檢查埠號是否正確");
            return false;
        } catch (Exception e) {
            result.setSuccess(false);
            result.setSummary("網路連線錯誤: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 檢查 HTTP 連線
     */
    private static boolean checkHttpConnectivity(String serviceUrl, int timeout, DiagnosticResult result) {
        try {
            URL url = new URL(serviceUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(timeout * 1000);
            connection.setReadTimeout(timeout * 1000);
            connection.setRequestProperty("User-Agent", "CosmedApi-OLTP-Diagnostic/1.3");
            
            int responseCode = connection.getResponseCode();
            String responseMessage = connection.getResponseMessage();
            
            result.addDetail("HTTP 連線測試完成");
            result.addDetail("回應碼: " + responseCode);
            result.addDetail("回應訊息: " + responseMessage);
            
            connection.disconnect();
            
            if (responseCode >= 200 && responseCode < 300) {
                result.addDetail("HTTP 連線正常");
                return true;
            } else if (responseCode == 405) {
                result.addDetail("HTTP 方法不被允許（正常，服務可能只接受 POST）");
                return true;
            } else {
                result.addDetail("HTTP 回應碼異常，但連線成功");
                result.addRecommendation("請檢查服務端點是否正確");
                return true; // 連線成功，只是方法不對
            }
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setSummary("HTTP 連線錯誤: " + e.getMessage());
            result.addRecommendation("請檢查 HTTP 服務設定");
            return false;
        }
    }
    
    /**
     * 檢查 SOAP 服務
     */
    private static boolean checkSoapService(String serviceUrl, int timeout, String testContent, DiagnosticResult result) {
        try {
            SimpleSOAPClient client = new SimpleSOAPClient(serviceUrl, timeout);
            client.setRequestContent(testContent);
            
            String response = client.sendRequest();
            
            if (response != null && !response.trim().isEmpty()) {
                result.setSuccess(true);
                result.setSummary("SOAP 服務測試成功");
                result.addDetail("SOAP 回應長度: " + response.length());
                result.addDetail("SOAP 服務正常運作");
                return true;
            } else {
                result.setSuccess(false);
                result.setSummary("SOAP 服務回應為空");
                result.addRecommendation("請檢查 SOAP 服務實作");
                return false;
            }
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setSummary("SOAP 服務測試失敗: " + e.getMessage());
            result.addRecommendation("請檢查 SOAP 服務設定");
            result.addRecommendation("請檢查 SOAP 請求格式");
            return false;
        }
    }
    
    /**
     * 快速連線測試
     */
    public static boolean quickTest(String serviceUrl, int timeout) {
        try {
            DiagnosticResult result = diagnose(serviceUrl, timeout, null);
            return result.isSuccess();
        } catch (Exception e) {
            System.out.println("快速測試失敗: " + e.getMessage());
            return false;
        }
    }
}
