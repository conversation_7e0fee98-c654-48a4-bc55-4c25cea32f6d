package com.pic.o2o.common;

import java.io.ByteArrayInputStream;
import java.io.StringWriter;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.w3c.dom.Document;
import org.xml.sax.ErrorHandler;
import org.xml.sax.SAXException;
import org.xml.sax.SAXParseException;

import com.pic.oltp.GlobalVariable;

/**
 * SOAP XML 驗證工具類別
 * 提供 XML 格式驗證和除錯功能
 * 
 * <AUTHOR> OLTP System
 * @version 1.3
 */
public class SOAPXMLValidator {
    
    /**
     * 驗證 XML 格式是否正確
     * 
     * @param xmlContent XML 內容
     * @return 驗證結果和錯誤訊息
     */
    public static ValidationResult validateXML(String xmlContent) {
        ValidationResult result = new ValidationResult();
        
        try {
            System.out.println("=== XML Validation Debug ===");
            System.out.println("Validating XML:");
            System.out.println(xmlContent);
            System.out.println("============================");
            
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            factory.setValidating(false);
            
            DocumentBuilder builder = factory.newDocumentBuilder();
            
            // 設定錯誤處理器來捕獲詳細錯誤
            builder.setErrorHandler(new ErrorHandler() {
                @Override
                public void warning(SAXParseException exception) throws SAXException {
                    result.addWarning("Warning at line " + exception.getLineNumber() + 
                                    ", column " + exception.getColumnNumber() + ": " + 
                                    exception.getMessage());
                }
                
                @Override
                public void error(SAXParseException exception) throws SAXException {
                    result.addError("Error at line " + exception.getLineNumber() + 
                                  ", column " + exception.getColumnNumber() + ": " + 
                                  exception.getMessage());
                }
                
                @Override
                public void fatalError(SAXParseException exception) throws SAXException {
                    result.addError("Fatal Error at line " + exception.getLineNumber() + 
                                  ", column " + exception.getColumnNumber() + ": " + 
                                  exception.getMessage());
                    throw exception;
                }
            });
            
            // 解析 XML
            ByteArrayInputStream bis = new ByteArrayInputStream(xmlContent.getBytes("UTF-8"));
            Document doc = builder.parse(bis);
            
            result.setValid(true);
            result.setMessage("XML 格式驗證通過");
            
            // 格式化 XML 輸出
            String formattedXML = formatXML(doc);
            result.setFormattedXML(formattedXML);
            
            System.out.println("✅ XML Validation PASSED");
            System.out.println("Formatted XML:");
            System.out.println(formattedXML);
            
        } catch (SAXParseException e) {
            result.setValid(false);
            String errorMsg = "XML 解析錯誤 at line " + e.getLineNumber() + 
                            ", column " + e.getColumnNumber() + ": " + e.getMessage();
            result.setMessage(errorMsg);
            result.addError(errorMsg);
            
            System.out.println("❌ XML Validation FAILED");
            System.out.println("Error: " + errorMsg);
            
            // 嘗試找出問題所在的具體位置
            analyzeXMLError(xmlContent, e.getLineNumber(), e.getColumnNumber(), result);
            
        } catch (Exception e) {
            result.setValid(false);
            result.setMessage("XML 處理錯誤: " + e.getMessage());
            result.addError(e.getMessage());
            
            System.out.println("❌ XML Processing FAILED");
            System.out.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("============================");
        return result;
    }
    
    /**
     * 分析 XML 錯誤的具體位置和原因
     */
    private static void analyzeXMLError(String xmlContent, int lineNumber, int columnNumber, ValidationResult result) {
        try {
            String[] lines = xmlContent.split("\n");
            
            System.out.println("=== XML Error Analysis ===");
            System.out.println("Error at line " + lineNumber + ", column " + columnNumber);
            
            if (lineNumber <= lines.length) {
                String errorLine = lines[lineNumber - 1];
                System.out.println("Error line content: " + errorLine);
                
                // 顯示錯誤位置
                StringBuilder pointer = new StringBuilder();
                for (int i = 0; i < columnNumber - 1; i++) {
                    pointer.append(" ");
                }
                pointer.append("^");
                System.out.println("Error position:     " + pointer.toString());
                
                // 檢查常見錯誤模式
                if (errorLine.contains("m:ns0") || errorLine.contains("m0:ns0")) {
                    result.addError("發現命名空間前綴衝突: 'ns0' 前綴與 'm' 或 'm0' 前綴衝突");
                    System.out.println("🔍 Detected namespace prefix conflict!");
                }
                
                if (errorLine.matches(".*<[^>]*[^/>]$")) {
                    result.addError("可能的未閉合標籤");
                    System.out.println("🔍 Possible unclosed tag detected!");
                }
                
                // 顯示上下文
                System.out.println("Context:");
                int start = Math.max(0, lineNumber - 3);
                int end = Math.min(lines.length, lineNumber + 2);
                
                for (int i = start; i < end; i++) {
                    String prefix = (i == lineNumber - 1) ? ">>> " : "    ";
                    System.out.println(prefix + (i + 1) + ": " + lines[i]);
                }
            }
            
            System.out.println("==========================");
            
        } catch (Exception e) {
            System.out.println("Error during XML analysis: " + e.getMessage());
        }
    }
    
    /**
     * 格式化 XML 文件
     */
    private static String formatXML(Document doc) {
        try {
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
            transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
            
            DOMSource source = new DOMSource(doc);
            StringWriter writer = new StringWriter();
            StreamResult result = new StreamResult(writer);
            
            transformer.transform(source, result);
            return writer.toString();
            
        } catch (Exception e) {
            return "無法格式化 XML: " + e.getMessage();
        }
    }
    
    /**
     * 檢查命名空間衝突
     */
    public static boolean hasNamespaceConflict(String xmlContent) {
        // 檢查是否有 m:ns0 或類似的衝突模式
        return xmlContent.contains("m:ns0") || 
               xmlContent.contains("m0:ns0") || 
               xmlContent.matches(".*<[^>]*:[^>]*:.*");
    }
    
    /**
     * 清理命名空間衝突
     */
    public static String cleanNamespaceConflicts(String xmlContent) {
        System.out.println("=== Cleaning Namespace Conflicts ===");
        System.out.println("Before cleaning:");
        System.out.println(xmlContent);
        
        // 移除重複的命名空間前綴
        String cleaned = xmlContent.replaceAll("<(m0?:)(ns\\d+:)([a-zA-Z][a-zA-Z0-9]*)", "<$1$3");
        cleaned = cleaned.replaceAll("</(m0?:)(ns\\d+:)([a-zA-Z][a-zA-Z0-9]*)", "</$1$3");
        
        System.out.println("After cleaning:");
        System.out.println(cleaned);
        System.out.println("===================================");
        
        return cleaned;
    }
    
    /**
     * 驗證結果類別
     */
    public static class ValidationResult {
        private boolean valid = false;
        private String message = "";
        private String formattedXML = "";
        private java.util.List<String> errors = new java.util.ArrayList<>();
        private java.util.List<String> warnings = new java.util.ArrayList<>();
        
        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getFormattedXML() { return formattedXML; }
        public void setFormattedXML(String formattedXML) { this.formattedXML = formattedXML; }
        
        public java.util.List<String> getErrors() { return errors; }
        public void addError(String error) { this.errors.add(error); }
        
        public java.util.List<String> getWarnings() { return warnings; }
        public void addWarning(String warning) { this.warnings.add(warning); }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("ValidationResult{");
            sb.append("valid=").append(valid);
            sb.append(", message='").append(message).append('\'');
            if (!errors.isEmpty()) {
                sb.append(", errors=").append(errors);
            }
            if (!warnings.isEmpty()) {
                sb.append(", warnings=").append(warnings);
            }
            sb.append('}');
            return sb.toString();
        }
    }
}
