package com.pic.o2o.common;

import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.ArrayList;
import java.util.List;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;

public class HTTPService {
	private static HttpServer server;

	private static int port;

	private static String http_context;

	private static String host;

	private static String logConsole;

	private static StringBuffer log = new StringBuffer();

	public HTTPService(int port, String host, String logConsole, String http_context, HttpHandler O2OHandler)
			throws IOException, ClientProtocolException, SdkException {
		HTTPService.port = port;
		HTTPService.host = host;
		HTTPService.logConsole = logConsole;
		HTTPService.http_context = http_context;
		server = HttpServer.create(new InetSocketAddress(HTTPService.port), 0);
		server.createContext(HTTPService.http_context, O2OHandler);
		server.setExecutor(null);
		server.start();
		log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(HTTPService.host).append("\t[")
				.append(getClass().getSimpleName()).append("]\tHTTP Server Start (port: ").append(HTTPService.port)
				.append(")");
		Utility.writeLog(HTTPService.logConsole, log);
		sendQueueLogHTTPMsg();
	}

	public static void close() throws IllegalArgumentException {
		server.stop(1);
		log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(host).append("\t[")
				.append(HTTPService.class.getSimpleName()).append("]\tHTTP Server Stop");
		Utility.writeLog(logConsole, log);
		log = null;
		server = null;
	}

	public static void sendQueueLogHTTPMsg() throws SdkException, ClientProtocolException, IOException {
		CloseableHttpClient httpClient = null;
		try {
			httpClient = HttpClients.createDefault();
			HttpPost queuLogHTTPPost = new HttpPost(
					"http://localhost:".concat(String.valueOf(port)).concat(http_context));
			List<NameValuePair> nvps = new ArrayList<>();
			nvps.add(new BasicNameValuePair("action", "getList"));
			queuLogHTTPPost.setEntity((HttpEntity) new UrlEncodedFormEntity(nvps, "UTF-8"));
			CloseableHttpResponse queueLogResponse = httpClient.execute((HttpUriRequest) queuLogHTTPPost);
			if (200 != queueLogResponse.getStatusLine().getStatusCode())
				throw new SdkException("send Disable Queue HTTP msg error");
		} finally {
			httpClient.close();
		}
	}
}
