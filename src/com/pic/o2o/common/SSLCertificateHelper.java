package com.pic.o2o.common;

import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;

import javax.net.ssl.*;

/**
 * SSL 憑證處理工具
 * 提供 SSL 憑證下載、安裝和信任管理功能
 * 
 * <AUTHOR> OLTP System
 * @version 1.3
 */
public class SSLCertificateHelper {
    
    /**
     * 下載並顯示服務的 SSL 憑證資訊
     * 
     * @param hostname 主機名稱
     * @param port 埠號（通常是 443）
     */
    public static void downloadAndShowCertificate(String hostname, int port) {
        System.out.println("=== SSL 憑證資訊 ===");
        System.out.println("主機: " + hostname + ":" + port);
        
        try {
            // 建立 SSL 連線
            SSLSocketFactory factory = (SSLSocketFactory) SSLSocketFactory.getDefault();
            SSLSocket socket = (SSLSocket) factory.createSocket(hostname, port);
            
            // 開始 SSL 握手
            socket.startHandshake();
            
            // 獲取憑證鏈
            Certificate[] certs = socket.getSession().getPeerCertificates();
            
            System.out.println("找到 " + certs.length + " 個憑證:");
            
            for (int i = 0; i < certs.length; i++) {
                if (certs[i] instanceof X509Certificate) {
                    X509Certificate cert = (X509Certificate) certs[i];
                    
                    System.out.println("\n憑證 " + (i + 1) + ":");
                    System.out.println("  主體: " + cert.getSubjectDN().getName());
                    System.out.println("  發行者: " + cert.getIssuerDN().getName());
                    System.out.println("  序號: " + cert.getSerialNumber());
                    System.out.println("  有效期: " + cert.getNotBefore() + " 到 " + cert.getNotAfter());
                    System.out.println("  簽名演算法: " + cert.getSigAlgName());
                    
                    // 檢查憑證是否過期
                    try {
                        cert.checkValidity();
                        System.out.println("  狀態: ✅ 有效");
                    } catch (Exception e) {
                        System.out.println("  狀態: ❌ 無效 - " + e.getMessage());
                    }
                }
            }
            
            socket.close();
            
        } catch (Exception e) {
            System.out.println("❌ 無法獲取憑證: " + e.getMessage());
            
            if (e.getMessage().contains("PKIX path building failed")) {
                System.out.println("\n🔍 診斷：憑證路徑建立失敗");
                System.out.println("這通常表示 Java 信任庫中沒有相應的 CA 憑證。");
                System.out.println("\n解決方案：");
                System.out.println("1. 更新 Java 版本");
                System.out.println("2. 手動添加 CA 憑證到信任庫");
                System.out.println("3. 使用企業 CA 憑證");
            }
        }
        
        System.out.println("==================");
    }
    
    /**
     * 建立信任所有憑證的 SSL 上下文（僅用於測試）
     */
    public static void setupTrustAllCertificates() {
        try {
            // 建立信任所有憑證的 TrustManager
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                    
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        // 信任所有客戶端憑證
                    }
                    
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        // 信任所有伺服器憑證
                        System.out.println("⚠️ 跳過伺服器憑證驗證: " + authType);
                        if (certs.length > 0) {
                            System.out.println("   憑證主體: " + certs[0].getSubjectDN().getName());
                        }
                    }
                }
            };
            
            // 安裝信任所有憑證的 TrustManager
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            
            // 跳過主機名稱驗證
            HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> {
                System.out.println("⚠️ 跳過主機名稱驗證: " + hostname);
                return true;
            });
            
            System.out.println("✅ 已設定信任所有 SSL 憑證（僅用於測試）");
            System.out.println("⚠️ 警告：這會降低安全性，僅應用於測試環境");
            
        } catch (Exception e) {
            System.out.println("❌ 設定信任所有憑證失敗: " + e.getMessage());
        }
    }
    
    /**
     * 測試 HTTPS 連線
     * 
     * @param url 要測試的 HTTPS URL
     * @param timeout 逾時時間（毫秒）
     * @return 是否連線成功
     */
    public static boolean testHttpsConnection(String url, int timeout) {
        try {
            System.out.println("測試 HTTPS 連線: " + url);
            
            URL testUrl = new URL(url);
            HttpsURLConnection connection = (HttpsURLConnection) testUrl.openConnection();
            
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(timeout);
            connection.setReadTimeout(timeout);
            connection.setRequestProperty("User-Agent", "CosmedApi-OLTP-SSL-Test/1.3");
            
            int responseCode = connection.getResponseCode();
            System.out.println("回應碼: " + responseCode);
            System.out.println("回應訊息: " + connection.getResponseMessage());
            
            // 顯示 SSL 資訊
            if (connection.getPeerPrincipal() != null) {
                System.out.println("SSL 協議: " + connection.getPeerPrincipal());
                System.out.println("加密套件: " + connection.getPeerPrincipal());
            }
            
            connection.disconnect();
            
            boolean success = responseCode >= 200 && responseCode < 400;
            System.out.println("連線結果: " + (success ? "✅ 成功" : "❌ 失敗"));
            
            return success;
            
        } catch (SSLHandshakeException e) {
            System.out.println("❌ SSL 握手失敗: " + e.getMessage());
            
            if (e.getMessage().contains("PKIX path building failed")) {
                System.out.println("🔍 問題：憑證路徑驗證失敗");
                System.out.println("💡 建議：嘗試使用 setupTrustAllCertificates() 跳過憑證驗證");
            } else if (e.getMessage().contains("certificate_unknown")) {
                System.out.println("🔍 問題：憑證未知");
                System.out.println("💡 建議：檢查憑證是否為自簽憑證或企業內部憑證");
            }
            
            return false;
            
        } catch (ConnectException e) {
            System.out.println("❌ 連線被拒絕: " + e.getMessage());
            System.out.println("🔍 問題：網路連線問題");
            System.out.println("💡 建議：檢查防火牆和網路設定");
            return false;
            
        } catch (SocketTimeoutException e) {
            System.out.println("❌ 連線逾時: " + e.getMessage());
            System.out.println("🔍 問題：網路延遲或服務無回應");
            System.out.println("💡 建議：增加逾時時間或檢查網路狀況");
            return false;
            
        } catch (Exception e) {
            System.out.println("❌ 連線錯誤: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 獲取系統預設的信任庫資訊
     */
    public static void showTrustStoreInfo() {
        try {
            System.out.println("=== Java 信任庫資訊 ===");
            
            String trustStorePath = System.getProperty("javax.net.ssl.trustStore");
            String trustStorePassword = System.getProperty("javax.net.ssl.trustStorePassword");
            String trustStoreType = System.getProperty("javax.net.ssl.trustStoreType", "JKS");
            
            System.out.println("信任庫路徑: " + (trustStorePath != null ? trustStorePath : "使用預設"));
            System.out.println("信任庫類型: " + trustStoreType);
            System.out.println("信任庫密碼: " + (trustStorePassword != null ? "已設定" : "未設定"));
            
            // 顯示 Java 版本和供應商
            System.out.println("Java 版本: " + System.getProperty("java.version"));
            System.out.println("Java 供應商: " + System.getProperty("java.vendor"));
            System.out.println("Java 安裝路徑: " + System.getProperty("java.home"));
            
            // 顯示支援的 SSL 協議
            SSLContext context = SSLContext.getDefault();
            SSLSocketFactory factory = context.getSocketFactory();
            String[] protocols = factory.getDefaultCipherSuites();
            System.out.println("支援的加密套件數量: " + protocols.length);
            
            System.out.println("=====================");
            
        } catch (Exception e) {
            System.out.println("❌ 無法獲取信任庫資訊: " + e.getMessage());
        }
    }
    
    /**
     * 主要測試方法
     */
    public static void main(String[] args) {
        String hostname = "stage-posapi2.tixpress.tw";
        int port = 443;
        String url = "https://" + hostname + "/POSProxyService.svc";
        
        System.out.println("=== SSL 憑證診斷工具 ===");
        System.out.println();
        
        // 1. 顯示系統資訊
        showTrustStoreInfo();
        System.out.println();
        
        // 2. 下載並顯示憑證
        downloadAndShowCertificate(hostname, port);
        System.out.println();
        
        // 3. 測試標準連線
        System.out.println("=== 標準 HTTPS 連線測試 ===");
        boolean standardSuccess = testHttpsConnection(url, 10000);
        System.out.println();
        
        // 4. 如果標準連線失敗，嘗試跳過憑證驗證
        if (!standardSuccess) {
            System.out.println("=== 跳過憑證驗證測試 ===");
            setupTrustAllCertificates();
            boolean trustAllSuccess = testHttpsConnection(url, 10000);
            
            if (trustAllSuccess) {
                System.out.println();
                System.out.println("🔍 結論：SSL 憑證驗證問題");
                System.out.println("跳過憑證驗證後連線成功，確認問題出在憑證驗證。");
            } else {
                System.out.println();
                System.out.println("🔍 結論：非憑證問題");
                System.out.println("即使跳過憑證驗證仍失敗，問題可能是網路或服務端。");
            }
        }
        
        System.out.println();
        System.out.println("=== 診斷完成 ===");
    }
}
