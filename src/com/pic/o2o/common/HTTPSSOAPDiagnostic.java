package com.pic.o2o.common;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.UnknownHostException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLHandshakeException;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

/**
 * HTTPS SOAP 連線診斷工具
 * 專門診斷 HTTPS SOAP 服務連線問題，包含 SSL/TLS 診斷
 * 
 * <AUTHOR> OLTP System
 * @version 1.3
 */
public class HTTPSSOAPDiagnostic {
    
    /**
     * 診斷結果類別
     */
    public static class DiagnosticResult {
        private boolean success = false;
        private String summary = "";
        private List<String> details = new ArrayList<>();
        private List<String> recommendations = new ArrayList<>();
        private List<String> sslInfo = new ArrayList<>();
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getSummary() { return summary; }
        public void setSummary(String summary) { this.summary = summary; }
        
        public List<String> getDetails() { return details; }
        public void addDetail(String detail) { this.details.add(detail); }
        
        public List<String> getRecommendations() { return recommendations; }
        public void addRecommendation(String recommendation) { this.recommendations.add(recommendation); }
        
        public List<String> getSslInfo() { return sslInfo; }
        public void addSslInfo(String info) { this.sslInfo.add(info); }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("=== HTTPS SOAP 連線診斷結果 ===\n");
            sb.append("狀態: ").append(success ? "✅ 成功" : "❌ 失敗").append("\n");
            sb.append("摘要: ").append(summary).append("\n\n");
            
            if (!details.isEmpty()) {
                sb.append("詳細資訊:\n");
                for (String detail : details) {
                    sb.append("  - ").append(detail).append("\n");
                }
                sb.append("\n");
            }
            
            if (!sslInfo.isEmpty()) {
                sb.append("SSL/TLS 資訊:\n");
                for (String info : sslInfo) {
                    sb.append("  🔒 ").append(info).append("\n");
                }
                sb.append("\n");
            }
            
            if (!recommendations.isEmpty()) {
                sb.append("建議:\n");
                for (String rec : recommendations) {
                    sb.append("  • ").append(rec).append("\n");
                }
            }
            
            sb.append("===============================");
            return sb.toString();
        }
    }
    
    /**
     * 執行完整的 HTTPS SOAP 連線診斷
     * 
     * @param serviceUrl HTTPS SOAP 服務 URL
     * @param timeout 逾時時間（秒）
     * @return 診斷結果
     */
    public static DiagnosticResult diagnoseHTTPS(String serviceUrl, int timeout) {
        DiagnosticResult result = new DiagnosticResult();
        
        System.out.println("=== 開始 HTTPS SOAP 連線診斷 ===");
        System.out.println("目標 URL: " + serviceUrl);
        System.out.println("逾時設定: " + timeout + " 秒");
        System.out.println("================================");
        
        try {
            // 1. URL 格式檢查
            if (!checkUrlFormat(serviceUrl, result)) {
                return result;
            }
            
            // 2. DNS 解析檢查
            if (!checkDnsResolution(serviceUrl, result)) {
                return result;
            }
            
            // 3. 網路連線檢查（TCP）
            if (!checkTcpConnectivity(serviceUrl, timeout, result)) {
                return result;
            }
            
            // 4. SSL/TLS 連線檢查
            if (!checkSslConnectivity(serviceUrl, timeout, result)) {
                return result;
            }
            
            // 5. HTTPS 連線檢查
            if (!checkHttpsConnectivity(serviceUrl, timeout, result)) {
                return result;
            }
            
            // 6. SOAP 服務檢查
            checkSoapService(serviceUrl, timeout, result);
            
            if (result.isSuccess()) {
                result.setSummary("所有診斷項目通過");
            }
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setSummary("診斷過程發生錯誤: " + e.getMessage());
            result.addDetail("錯誤詳情: " + e.toString());
        }
        
        System.out.println(result.toString());
        return result;
    }
    
    /**
     * 檢查 URL 格式
     */
    private static boolean checkUrlFormat(String serviceUrl, DiagnosticResult result) {
        try {
            if (serviceUrl == null || serviceUrl.trim().isEmpty()) {
                result.setSuccess(false);
                result.setSummary("URL 為空");
                result.addRecommendation("請檢查 transfile.txt 中的 SOAP 服務 URL 設定");
                return false;
            }
            
            URL url = new URL(serviceUrl);
            result.addDetail("URL 格式正確");
            result.addDetail("協議: " + url.getProtocol());
            result.addDetail("主機: " + url.getHost());
            result.addDetail("埠號: " + (url.getPort() != -1 ? url.getPort() : url.getDefaultPort()));
            result.addDetail("路徑: " + url.getPath());
            
            if (!"https".equalsIgnoreCase(url.getProtocol())) {
                result.addDetail("⚠️ 警告：不是 HTTPS 協議");
                if ("http".equalsIgnoreCase(url.getProtocol())) {
                    result.addRecommendation("考慮使用 HTTPS 以提高安全性");
                } else {
                    result.setSuccess(false);
                    result.setSummary("不支援的協議: " + url.getProtocol());
                    result.addRecommendation("請使用 HTTPS 協議");
                    return false;
                }
            }
            
            return true;
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setSummary("URL 格式錯誤: " + e.getMessage());
            result.addRecommendation("請檢查 URL 格式是否正確");
            return false;
        }
    }
    
    /**
     * 檢查 DNS 解析
     */
    private static boolean checkDnsResolution(String serviceUrl, DiagnosticResult result) {
        try {
            URL url = new URL(serviceUrl);
            String hostname = url.getHost();
            
            System.out.println("正在解析 DNS: " + hostname);
            InetAddress address = InetAddress.getByName(hostname);
            result.addDetail("DNS 解析成功");
            result.addDetail("主機名稱: " + hostname);
            result.addDetail("IP 位址: " + address.getHostAddress());
            
            return true;
            
        } catch (UnknownHostException e) {
            result.setSuccess(false);
            result.setSummary("DNS 解析失敗: " + e.getMessage());
            result.addRecommendation("請檢查主機名稱是否正確: stage-posapi2.tixpress.tw");
            result.addRecommendation("請檢查 DNS 設定");
            result.addRecommendation("請檢查網路連線");
            result.addRecommendation("嘗試使用 nslookup stage-posapi2.tixpress.tw 測試 DNS");
            return false;
        } catch (Exception e) {
            result.setSuccess(false);
            result.setSummary("DNS 檢查錯誤: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 檢查 TCP 網路連線
     */
    private static boolean checkTcpConnectivity(String serviceUrl, int timeout, DiagnosticResult result) {
        try {
            URL url = new URL(serviceUrl);
            String hostname = url.getHost();
            int port = url.getPort() != -1 ? url.getPort() : 443; // HTTPS 預設埠號
            
            System.out.println("正在測試 TCP 連線: " + hostname + ":" + port);
            
            // 使用 Socket 測試連線
            java.net.Socket socket = new java.net.Socket();
            socket.connect(new java.net.InetSocketAddress(hostname, port), timeout * 1000);
            socket.close();
            
            result.addDetail("TCP 連線測試成功");
            result.addDetail("目標: " + hostname + ":" + port);
            
            return true;
            
        } catch (SocketTimeoutException e) {
            result.setSuccess(false);
            result.setSummary("TCP 連線逾時");
            result.addRecommendation("請檢查網路連線速度");
            result.addRecommendation("請考慮增加逾時時間");
            result.addRecommendation("請檢查防火牆是否阻擋 443 埠");
            return false;
        } catch (ConnectException e) {
            result.setSuccess(false);
            result.setSummary("TCP 連線被拒絕");
            result.addRecommendation("請檢查目標服務是否啟動");
            result.addRecommendation("請檢查防火牆設定");
            result.addRecommendation("請檢查 443 埠是否開放");
            result.addRecommendation("請聯絡服務提供者確認服務狀態");
            return false;
        } catch (Exception e) {
            result.setSuccess(false);
            result.setSummary("TCP 連線錯誤: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 檢查 SSL/TLS 連線
     */
    private static boolean checkSslConnectivity(String serviceUrl, int timeout, DiagnosticResult result) {
        try {
            URL url = new URL(serviceUrl);
            
            System.out.println("正在測試 SSL/TLS 連線...");
            
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(timeout * 1000);
            connection.setReadTimeout(timeout * 1000);
            connection.setRequestProperty("User-Agent", "CosmedApi-OLTP-HTTPS-Diagnostic/1.3");
            
            try {
                connection.connect();
                
                // 獲取 SSL 資訊
                javax.net.ssl.SSLSession session = (javax.net.ssl.SSLSession) connection.getPeerPrincipal();
                result.addSslInfo("SSL 連線成功");
                result.addSslInfo("協議: " + session.getProtocol());
                result.addSslInfo("加密套件: " + session.getCipherSuite());
                
                // 獲取憑證資訊
                java.security.cert.Certificate[] certs = session.getPeerCertificates();
                if (certs.length > 0 && certs[0] instanceof X509Certificate) {
                    X509Certificate cert = (X509Certificate) certs[0];
                    result.addSslInfo("憑證主體: " + cert.getSubjectDN().getName());
                    result.addSslInfo("憑證發行者: " + cert.getIssuerDN().getName());
                    result.addSslInfo("憑證有效期: " + cert.getNotBefore() + " 到 " + cert.getNotAfter());
                }
                
                connection.disconnect();
                result.addDetail("SSL/TLS 連線測試成功");
                return true;
                
            } catch (SSLHandshakeException e) {
                result.setSuccess(false);
                result.setSummary("SSL 握手失敗: " + e.getMessage());
                result.addRecommendation("請檢查 SSL 憑證是否有效");
                result.addRecommendation("請檢查系統時間是否正確");
                result.addRecommendation("請檢查 Java 信任庫是否包含必要的 CA 憑證");
                result.addRecommendation("考慮更新 Java 版本以支援最新的 TLS 協議");
                result.addRecommendation("如果是測試環境，可以考慮暫時跳過憑證驗證");
                return false;
            }
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setSummary("SSL 連線錯誤: " + e.getMessage());
            result.addRecommendation("請檢查 SSL/TLS 設定");
            return false;
        }
    }
    
    /**
     * 檢查 HTTPS 連線
     */
    private static boolean checkHttpsConnectivity(String serviceUrl, int timeout, DiagnosticResult result) {
        try {
            URL url = new URL(serviceUrl);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            
            connection.setRequestMethod("POST"); // SOAP 使用 POST
            connection.setConnectTimeout(timeout * 1000);
            connection.setReadTimeout(timeout * 1000);
            connection.setRequestProperty("User-Agent", "CosmedApi-OLTP-HTTPS-Test/1.3");
            connection.setRequestProperty("Content-Type", "text/xml; charset=utf-8");
            connection.setRequestProperty("SOAPAction", "");
            
            int responseCode = connection.getResponseCode();
            String responseMessage = connection.getResponseMessage();
            
            result.addDetail("HTTPS 連線測試完成");
            result.addDetail("回應碼: " + responseCode);
            result.addDetail("回應訊息: " + responseMessage);
            
            connection.disconnect();
            
            if (responseCode >= 200 && responseCode < 300) {
                result.addDetail("HTTPS 連線正常");
                return true;
            } else if (responseCode == 405) {
                result.addDetail("HTTP 方法不被允許（正常，服務可能需要特定的 SOAP 請求）");
                return true;
            } else if (responseCode >= 400 && responseCode < 500) {
                result.addDetail("客戶端錯誤，但 HTTPS 連線成功");
                result.addRecommendation("請檢查 SOAP 請求格式和標頭");
                return true;
            } else {
                result.addDetail("HTTPS 回應碼異常，但連線成功");
                result.addRecommendation("請檢查服務端點是否正確");
                return true;
            }
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setSummary("HTTPS 連線錯誤: " + e.getMessage());
            result.addRecommendation("請檢查 HTTPS 服務設定");
            return false;
        }
    }
    
    /**
     * 檢查 SOAP 服務
     */
    private static boolean checkSoapService(String serviceUrl, int timeout, DiagnosticResult result) {
        try {
            System.out.println("正在測試 SOAP 服務...");
            
            // 建立簡單的 SOAP 測試請求
            String testSoapRequest = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                    "<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">" +
                    "<soap:Body>" +
                    "<TestConnection xmlns=\"http://tempuri.org/\">" +
                    "</TestConnection>" +
                    "</soap:Body>" +
                    "</soap:Envelope>";
            
            SimpleSOAPClient client = new SimpleSOAPClient(serviceUrl, timeout);
            client.setRequestContent(testSoapRequest);
            
            String response = client.sendRequest();
            
            if (response != null && !response.trim().isEmpty()) {
                result.setSuccess(true);
                result.setSummary("HTTPS SOAP 服務測試成功");
                result.addDetail("SOAP 回應長度: " + response.length());
                result.addDetail("SOAP 服務正常運作");
                return true;
            } else {
                result.setSuccess(false);
                result.setSummary("SOAP 服務回應為空");
                result.addRecommendation("請檢查 SOAP 服務實作");
                return false;
            }
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setSummary("SOAP 服務測試失敗: " + e.getMessage());
            result.addRecommendation("請檢查 SOAP 服務設定");
            result.addRecommendation("請檢查 SOAP 請求格式");
            return false;
        }
    }
    
    /**
     * 建立信任所有憑證的 SSL 上下文（僅用於測試）
     */
    public static void setupTrustAllCertificates() {
        try {
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) { }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) { }
                }
            };
            
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            
            // 跳過主機名稱驗證
            HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
            
            System.out.println("⚠️ 警告：已設定信任所有 SSL 憑證（僅用於測試）");
            
        } catch (Exception e) {
            System.out.println("設定信任所有憑證失敗: " + e.getMessage());
        }
    }
}
