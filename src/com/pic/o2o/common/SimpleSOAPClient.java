package com.pic.o2o.common;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.StringWriter;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;

import com.pic.oltp.GlobalVariable;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.http.conn.ConnectTimeoutException;

// import com.pic.oltp.GlobalVariable; // 移除直接依賴

/**
 * 簡化版 SOAP 客戶端
 * 使用 HTTP POST 方式發送 SOAP 請求，避免複雜的 SOAP API 依賴問題
 * 
 * <AUTHOR> OLTP System
 * @version 1.3
 */
public class SimpleSOAPClient {
    
    private String serviceUrl;
    private int timeout;
    private String requestContent;
    private String soapAction;
    private StringBuffer log = new StringBuffer();

    // 日誌設定（可選，用於依賴注入）
    private String host = "UNKNOWN_HOST";
    private String logTransName = "TRANSLOG";
    
    /**
     * 建構子
     * 
     * @param serviceUrl SOAP 服務的 URL
     * @param timeout 連線逾時時間（秒）
     */
    public SimpleSOAPClient(String serviceUrl, int timeout) {
        this.serviceUrl = serviceUrl;
        this.timeout = timeout;
        this.requestContent = "";
    }
    
    /**
     * 設定請求內容
     *
     * @param content 要發送的內容
     */
    public void setRequestContent(String content) {
        this.requestContent = content;
        // 自動設定 SOAPAction（根據內容推斷）
        this.soapAction = inferSOAPAction(content);
    }

    /**
     * 手動設定 SOAPAction
     *
     * @param soapAction SOAP Action 值
     */
    public void setSOAPAction(String soapAction) {
        this.soapAction = soapAction;
    }

    /**
     * 設定日誌參數（依賴注入）
     *
     * @param host 主機名稱
     * @param logTransName 日誌名稱
     */
    public void setLogSettings(String host, String logTransName) {
        this.host = host;
        this.logTransName = logTransName;
    }

    /**
     * 根據請求內容推斷 SOAPAction
     *
     * @param content 請求內容
     * @return SOAPAction 字串
     */
    private String inferSOAPAction(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "http://ticketxpress.com.tw/IPOSProxy/DefaultAction";
        }

        try {
            // 從內容中提取業務操作名稱
            if (content.contains("ManageTerminal")) {
                return "http://ticketxpress.com.tw/IPOSProxy/ManageTerminal";
            } else if (content.contains("ProcessPayment")) {
                return "http://ticketxpress.com.tw/IPOSProxy/ProcessPayment";
            } else if (content.contains("QueryTransaction")) {
                return "http://ticketxpress.com.tw/IPOSProxy/QueryTransaction";
            } else if (content.contains("CancelTransaction")) {
                return "http://ticketxpress.com.tw/IPOSProxy/CancelTransaction";
            } else {
                // 嘗試從根元素名稱推斷
                String rootElement = extractRootElementName(content);
                if (rootElement != null) {
                    return "http://ticketxpress.com.tw/IPOSProxy/" + rootElement;
                }
            }
        } catch (Exception e) {
            System.out.println("Error inferring SOAPAction: " + e.getMessage());
        }

        // 預設 SOAPAction
        return "http://ticketxpress.com.tw/IPOSProxy/DefaultAction";
    }
    
    /**
     * 發送 SOAP 請求並取得回應
     * 
     * @return 回應內容字串
     * @throws Exception 當連線或處理發生錯誤時
     */
    public String sendRequest() throws Exception {
        String responseStr = null;
        HttpURLConnection connection = null;
        
        try {
            // 記錄開始時間
            long startTime = System.currentTimeMillis();

            // 詳細的連線診斷
            System.out.println("=== SimpleSOAPClient 連線診斷 ===");
            System.out.println("目標 URL: " + serviceUrl);
            System.out.println("連線逾時: " + timeout + " 秒");
            System.out.println("讀取逾時: " + timeout + " 秒");

            // 建立 HTTP 連線
            URL url = new URL(serviceUrl);
            System.out.println("URL 解析成功: " + url.toString());
            System.out.println("協議: " + url.getProtocol());
            System.out.println("主機: " + url.getHost());
            System.out.println("埠號: " + (url.getPort() != -1 ? url.getPort() : url.getDefaultPort()));
            System.out.println("路徑: " + url.getPath());

            connection = (HttpURLConnection) url.openConnection();
            System.out.println("HTTP 連線物件建立成功");
            
            // 設定 HTTP 連線參數
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "text/xml; charset=utf-8");

            // 設定 SOAPAction 標頭
            if (soapAction != null && !soapAction.trim().isEmpty()) {
                connection.setRequestProperty("SOAPAction", soapAction);
            } else {
                connection.setRequestProperty("SOAPAction", "");
            }

            // 設定其他標準 HTTP 標頭
            connection.setRequestProperty("User-Agent", "CosmedApi-OLTP-SimpleSOAPClient/1.3");
            connection.setRequestProperty("Accept", "text/xml, application/soap+xml");

            connection.setDoOutput(true);
            connection.setDoInput(true);

            // 輸出 HTTP 標頭資訊
            System.out.println("=== HTTP Headers Setup (SimpleSOAP) ===");
            System.out.println("Content-Type: text/xml; charset=utf-8");
            System.out.println("SOAPAction: " + (soapAction != null ? soapAction : "(empty)"));
            System.out.println("User-Agent: CosmedApi-OLTP-SimpleSOAPClient/1.3");
            System.out.println("======================================");
            
            // 設定逾時
            connection.setConnectTimeout(timeout * 1000);
            connection.setReadTimeout(timeout * 1000);
            
            // 建立 SOAP 信封
            String soapEnvelope = createSOAPEnvelope(requestContent);

            // Console 除錯輸出
            System.out.println("=== SimpleSOAPClient Debug Info ===");
            System.out.println("Original Request Content:");
            System.out.println(requestContent);
            System.out.println("Generated SOAP Envelope:");
            System.out.println(soapEnvelope);

            // 驗證生成的 SOAP Envelope
            SOAPXMLValidator.ValidationResult validation = SOAPXMLValidator.validateXML(soapEnvelope);
            if (!validation.isValid()) {
                System.out.println("⚠️ SOAP Envelope validation failed:");
                System.out.println(validation.getMessage());
                for (String error : validation.getErrors()) {
                    System.out.println("  - " + error);
                }

                // 嘗試清理命名空間衝突
                if (SOAPXMLValidator.hasNamespaceConflict(soapEnvelope)) {
                    System.out.println("🔧 Attempting to clean namespace conflicts...");
                    soapEnvelope = SOAPXMLValidator.cleanNamespaceConflicts(soapEnvelope);

                    // 重新驗證
                    validation = SOAPXMLValidator.validateXML(soapEnvelope);
                    if (validation.isValid()) {
                        System.out.println("✅ SOAP Envelope validation passed after cleaning!");
                    } else {
                        System.out.println("❌ SOAP Envelope still invalid after cleaning");
                    }
                }
            } else {
                System.out.println("✅ SOAP Envelope validation passed!");
            }

            System.out.println("===================================");

            // 記錄請求日誌
            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(host)
               .append("\t[").append(this.getClass().getSimpleName())
               .append("]\tSimple SOAP Request to: ").append(serviceUrl)
               .append("\r\n");
            Utility.writeLog(logTransName, log);
            
            // 發送請求
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = soapEnvelope.getBytes("utf-8");
                os.write(input, 0, input.length);
                os.flush();
            }
            
            // 讀取回應
            int responseCode = connection.getResponseCode();
            System.out.println("HTTP 回應碼: " + responseCode);
            System.out.println("HTTP 回應訊息: " + connection.getResponseMessage());

            if (responseCode == HttpURLConnection.HTTP_OK) {
                System.out.println("✅ HTTP 請求成功，開始讀取回應內容");
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), "utf-8"))) {
                    StringBuilder response = new StringBuilder();
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        response.append(responseLine.trim());
                    }
                    responseStr = response.toString();
                }

                // Console 除錯輸出 - 原始回應
                System.out.println("=== SimpleSOAPClient Response Debug ===");
                System.out.println("Raw SOAP Response:");
                System.out.println(responseStr);
                System.out.println("======================================");

                // 處理 SOAP 回應
                responseStr = processSOAPResponse(responseStr);

                // Console 除錯輸出 - 處理後回應
                System.out.println("Processed Response:");
                System.out.println(responseStr);
                System.out.println("======================================");
                
            } else {
                // 處理錯誤回應
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getErrorStream(), "utf-8"))) {
                    StringBuilder errorResponse = new StringBuilder();
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        errorResponse.append(responseLine.trim());
                    }
                    
                    log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .append("\t").append(host)
                       .append("\t[").append(this.getClass().getSimpleName())
                       .append("]\tSOAP Error Response (").append(responseCode).append("): ")
                       .append(errorResponse.toString())
                       .append("\r\n");
                    Utility.writeLog(logTransName, log);
                }
                
                throw new ConnectException("SOAP 服務回應錯誤: HTTP " + responseCode);
            }
            
            // 計算處理時間
            long endTime = System.currentTimeMillis();
            long processingTime = endTime - startTime;
            
            // 記錄成功日誌
            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(GlobalVariable.HOST)
               .append("\t[").append(this.getClass().getSimpleName())
               .append("]\tSimple SOAP Response received successfully. Processing time: ")
               .append(processingTime).append("ms")
               .append("\r\n");
            Utility.writeLog(GlobalVariable.logTrans, log);
            
        } catch (SocketTimeoutException e) {
            System.out.println("❌ SOAP 請求逾時錯誤");
            System.out.println("逾時設定: " + timeout + " 秒");
            System.out.println("錯誤訊息: " + e.getMessage());

            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(GlobalVariable.HOST)
               .append("\t[").append(this.getClass().getSimpleName())
               .append("]\tSimple SOAP Request timeout: ").append(serviceUrl)
               .append(" (timeout: ").append(timeout).append("s)")
               .append("\r\n").append(ExceptionUtils.getStackTrace(e));
            Utility.writeLog(GlobalVariable.logTrans, log);
            throw new SocketTimeoutException("SOAP 請求逾時 (" + timeout + "秒)");

        } catch (ConnectException e) {
            System.out.println("❌ SOAP 連線失敗錯誤");
            System.out.println("目標 URL: " + serviceUrl);
            System.out.println("錯誤訊息: " + e.getMessage());
            System.out.println("可能原因:");
            System.out.println("  1. 服務未啟動或無法連線");
            System.out.println("  2. 網路連線問題");
            System.out.println("  3. 防火牆阻擋");
            System.out.println("  4. URL 錯誤");

            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(GlobalVariable.HOST)
               .append("\t[").append(this.getClass().getSimpleName())
               .append("]\tSimple SOAP Connection failed: ").append(serviceUrl)
               .append("\r\n").append(ExceptionUtils.getStackTrace(e));
            Utility.writeLog(GlobalVariable.logTrans, log);
            throw new ConnectException("SOAP 服務連線失敗: " + serviceUrl);

        } catch (IOException e) {
            System.out.println("❌ SOAP IO 錯誤");
            System.out.println("錯誤類型: " + e.getClass().getSimpleName());
            System.out.println("錯誤訊息: " + e.getMessage());

            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(GlobalVariable.HOST)
               .append("\t[").append(this.getClass().getSimpleName())
               .append("]\tSimple SOAP IO Exception: ").append(serviceUrl)
               .append("\r\n").append(ExceptionUtils.getStackTrace(e));
            Utility.writeLog(GlobalVariable.logTrans, log);
            throw new IOException("SOAP 通訊錯誤: " + e.getMessage());
            
        } catch (Exception e) {
            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(GlobalVariable.HOST)
               .append("\t[").append(this.getClass().getSimpleName())
               .append("]\tSimple SOAP General Exception: ").append(serviceUrl)
               .append("\r\n").append(ExceptionUtils.getStackTrace(e));
            Utility.writeLog(GlobalVariable.logTrans, log);
            throw e;
            
        } finally {
            // 關閉連線
            if (connection != null) {
                connection.disconnect();
            }
        }
        
        return responseStr;
    }
    
    /**
     * 建立 SOAP 信封
     *
     * @param content 請求內容（完整的 OLTP XML）
     * @return 修改後的 OLTP XML（AP 內容被 SOAP Envelope 包裝）
     */
    private String createSOAPEnvelope(String content) {
        if (content == null || content.trim().isEmpty()) {
            return content;
        }

        try {
            System.out.println("=== OLTP-SOAP Transformation Debug ===");
            System.out.println("Input OLTP XML:");
            System.out.println(content);

            // 檢查是否為 OLTP 格式
            if (content.contains("<ns0:OLTP") && content.contains("<ns0:AP>")) {
                return transformOLTPWithSOAP(content);
            } else if (content.trim().startsWith("<")) {
                // 如果不是 OLTP 格式，但是 XML，則進行原有的轉換
                return createStandardSOAPEnvelope(content);
            } else {
                // 純文字內容
                return createStandardSOAPEnvelope(content);
            }

        } catch (Exception e) {
            System.out.println("Error in createSOAPEnvelope: " + e.getMessage());
            e.printStackTrace();
            return content; // 如果轉換失敗，返回原始內容
        }
    }

    /**
     * 轉換 OLTP XML，只對 AP 內容進行 SOAP 包裝
     */
    private String transformOLTPWithSOAP(String oltpXml) {
        try {
            System.out.println("=== Transforming OLTP with SOAP ===");

            // 提取 AP 內容
            String apContent = extractAPContent(oltpXml);
            System.out.println("Extracted AP Content:");
            System.out.println(apContent);

            if (apContent != null && !apContent.trim().isEmpty()) {
                // 對 AP 內容進行 SOAP 轉換
                String soapWrappedContent = createSOAPEnvelopeForAPContent(apContent);
                System.out.println("SOAP Wrapped AP Content:");
                System.out.println(soapWrappedContent);

                // 將 SOAP 包裝的內容放回 OLTP 結構中
                String result = replaceAPContent(oltpXml, soapWrappedContent);
                System.out.println("Final OLTP with SOAP:");
                System.out.println(result);
                System.out.println("==================================");

                return result;
            } else {
                System.out.println("No AP content found, returning original XML");
                return oltpXml;
            }

        } catch (Exception e) {
            System.out.println("Error in transformOLTPWithSOAP: " + e.getMessage());
            e.printStackTrace();
            return oltpXml;
        }
    }

    /**
     * 建立標準 SOAP Envelope（用於非 OLTP 格式）
     */
    private String createStandardSOAPEnvelope(String content) {
        StringBuilder envelope = new StringBuilder();

        envelope.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        envelope.append("<SOAP-ENV:Envelope ");
        envelope.append("xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\" ");
        envelope.append("xmlns:SOAP-ENC=\"http://schemas.xmlsoap.org/soap/encoding/\" ");
        envelope.append("xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" ");
        envelope.append("xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" ");
        envelope.append("xmlns:m0=\"http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common\">");
        envelope.append("<SOAP-ENV:Body>");

        if (content != null && !content.trim().isEmpty()) {
            if (content.trim().startsWith("<")) {
                String transformedContent = transformToSOAPFormat(content);
                envelope.append(transformedContent);
            } else {
                envelope.append("<m:request xmlns:m=\"http://ticketxpress.com.tw/\">");
                envelope.append("<![CDATA[").append(content).append("]]>");
                envelope.append("</m:request>");
            }
        } else {
            envelope.append("<m:request xmlns:m=\"http://ticketxpress.com.tw/\"/>");
        }

        envelope.append("</SOAP-ENV:Body>");
        envelope.append("</SOAP-ENV:Envelope>");

        return envelope.toString();
    }

    /**
     * 提取 OLTP XML 中的 AP 內容
     */
    private String extractAPContent(String oltpXml) {
        try {
            // 找到 <ns0:AP> 開始標籤
            int apStartTag = oltpXml.indexOf("<ns0:AP>");
            if (apStartTag == -1) {
                System.out.println("No <ns0:AP> start tag found");
                return null;
            }

            // 找到 AP 內容的開始位置
            int contentStart = oltpXml.indexOf('>', apStartTag) + 1;

            // 找到 </ns0:AP> 結束標籤
            int apEndTag = oltpXml.indexOf("</ns0:AP>");
            if (apEndTag == -1) {
                System.out.println("No </ns0:AP> end tag found");
                return null;
            }

            // 提取 AP 內容
            String apContent = oltpXml.substring(contentStart, apEndTag).trim();
            System.out.println("Raw AP content extracted: " + apContent);

            return apContent;

        } catch (Exception e) {
            System.out.println("Error extracting AP content: " + e.getMessage());
            return null;
        }
    }

    /**
     * 為 AP 內容建立 SOAP Envelope
     */
    private String createSOAPEnvelopeForAPContent(String apContent) {
        StringBuilder soapEnvelope = new StringBuilder();

        soapEnvelope.append("<SOAP-ENV:Envelope ");
        soapEnvelope.append("xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\" ");
        soapEnvelope.append("xmlns:SOAP-ENC=\"http://schemas.xmlsoap.org/soap/encoding/\" ");
        soapEnvelope.append("xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" ");
        soapEnvelope.append("xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" ");
        soapEnvelope.append("xmlns:m0=\"http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common\">");
        soapEnvelope.append("<SOAP-ENV:Body>");

        if (apContent != null && !apContent.trim().isEmpty()) {
            // 轉換 AP 內容為 SOAP 格式
            String transformedContent = transformToSOAPFormat(apContent);
            soapEnvelope.append(transformedContent);
        } else {
            soapEnvelope.append("<m:request xmlns:m=\"http://ticketxpress.com.tw/\"/>");
        }

        soapEnvelope.append("</SOAP-ENV:Body>");
        soapEnvelope.append("</SOAP-ENV:Envelope>");

        return soapEnvelope.toString();
    }

    /**
     * 將 SOAP 包裝的內容替換回 OLTP XML 的 AP 部分
     */
    private String replaceAPContent(String oltpXml, String soapContent) {
        try {
            // 找到 <ns0:AP> 開始標籤
            int apStartTag = oltpXml.indexOf("<ns0:AP>");
            if (apStartTag == -1) {
                return oltpXml;
            }

            // 找到 AP 內容的開始位置
            int contentStart = oltpXml.indexOf('>', apStartTag) + 1;

            // 找到 </ns0:AP> 結束標籤
            int apEndTag = oltpXml.indexOf("</ns0:AP>");
            if (apEndTag == -1) {
                return oltpXml;
            }

            // 建立新的 XML
            StringBuilder result = new StringBuilder();
            result.append(oltpXml.substring(0, contentStart)); // OLTP 開頭到 AP 內容開始
            result.append("\n\t\t"); // 格式化縮排
            result.append(soapContent); // SOAP 包裝的內容
            result.append("\n\t"); // 格式化縮排
            result.append(oltpXml.substring(apEndTag)); // 從 AP 結束標籤到 OLTP 結尾

            return result.toString();

        } catch (Exception e) {
            System.out.println("Error replacing AP content: " + e.getMessage());
            return oltpXml;
        }
    }

    /**
     * 將原始 XML 內容轉換為標準 SOAP 格式
     *
     * @param originalContent 原始 XML 內容
     * @return 轉換後的 SOAP 格式內容
     */
    private String transformToSOAPFormat(String originalContent) {
        try {
            // 移除 XML 宣告（如果存在）
            String content = originalContent.replaceFirst("<\\?xml[^>]*\\?>", "").trim();

            // 解析根元素名稱
            String rootElementName = extractRootElementName(content);

            if (rootElementName != null) {
                // 建立 SOAP 格式的包裝
                StringBuilder soapContent = new StringBuilder();
                soapContent.append("<m:").append(rootElementName).append(" xmlns:m=\"http://ticketxpress.com.tw/\">");

                // 轉換內部內容
                String innerContent = extractInnerContent(content, rootElementName);
                String transformedInner = transformInnerElements(innerContent);
                soapContent.append(transformedInner);

                soapContent.append("</m:").append(rootElementName).append(">");

                return soapContent.toString();
            } else {
                // 如果無法解析，使用預設包裝
                return "<m:request xmlns:m=\"http://ticketxpress.com.tw/\">" + content + "</m:request>";
            }

        } catch (Exception e) {
            // 如果轉換失敗，記錄錯誤並返回原始內容
            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(GlobalVariable.HOST)
               .append("\t[").append(this.getClass().getSimpleName())
               .append("]\tError transforming XML to SOAP format: ").append(e.getMessage())
               .append("\r\n");
            Utility.writeLog(GlobalVariable.logTrans, log);

            return "<m:request xmlns:m=\"http://ticketxpress.com.tw/\">" + originalContent + "</m:request>";
        }
    }

    /**
     * 提取根元素名稱
     */
    private String extractRootElementName(String content) {
        try {
            int start = content.indexOf('<');
            int end = content.indexOf('>', start);
            if (start != -1 && end != -1) {
                String rootTag = content.substring(start + 1, end);
                // 移除屬性，只保留元素名稱
                int spaceIndex = rootTag.indexOf(' ');
                if (spaceIndex != -1) {
                    rootTag = rootTag.substring(0, spaceIndex);
                }
                return rootTag;
            }
        } catch (Exception e) {
            // 忽略解析錯誤
        }
        return null;
    }

    /**
     * 提取內部內容
     */
    private String extractInnerContent(String content, String rootElementName) {
        try {
            String startTag = "<" + rootElementName;
            String endTag = "</" + rootElementName + ">";

            int startIndex = content.indexOf('>');
            int endIndex = content.lastIndexOf(endTag);

            if (startIndex != -1 && endIndex != -1 && startIndex < endIndex) {
                return content.substring(startIndex + 1, endIndex);
            }
        } catch (Exception e) {
            // 忽略解析錯誤
        }
        return content;
    }

    /**
     * 轉換內部元素，添加適當的命名空間前綴
     * manageTerminalRequest 使用 m: 前綴，其他元素使用 m0: 前綴
     */
    private String transformInnerElements(String innerContent) {
        try {
            System.out.println("=== Transform Inner Elements Debug (SimpleSOAP) ===");
            System.out.println("Before transformation:");
            System.out.println(innerContent);

            // 首先移除已存在的命名空間前綴（如 ns0:, ns1: 等）
            String cleanContent = innerContent.replaceAll("<(ns\\d+:)([a-zA-Z][a-zA-Z0-9]*)", "<$2");
            cleanContent = cleanContent.replaceAll("</(ns\\d+:)([a-zA-Z][a-zA-Z0-9]*)", "</$2");

            System.out.println("After removing existing namespaces:");
            System.out.println(cleanContent);

            // 特殊處理：manageTerminalRequest 使用 m: 前綴
            String transformed = cleanContent.replaceAll("<(manageTerminalRequest)([^>]*)", "<m:$1$2");
            transformed = transformed.replaceAll("</(manageTerminalRequest)", "</m:$1");

            // 其他元素使用 m0: 前綴（但排除已經有前綴的元素）
            transformed = transformed.replaceAll("<([a-zA-Z][a-zA-Z0-9]*(?![:])[^>]*)", "<m0:$1");
            transformed = transformed.replaceAll("</([a-zA-Z][a-zA-Z0-9]*)", "</m0:$1");

            // 修正：確保 manageTerminalRequest 不會被重複添加前綴
            transformed = transformed.replaceAll("<m0:(m:manageTerminalRequest)", "<$1");
            transformed = transformed.replaceAll("</m0:(m:manageTerminalRequest)", "</$1");

            System.out.println("After adding namespace prefixes:");
            System.out.println("- manageTerminalRequest: m: prefix");
            System.out.println("- Other elements: m0: prefix");
            System.out.println(transformed);
            System.out.println("==================================================");

            return transformed;
        } catch (Exception e) {
            System.out.println("Error in transformInnerElements: " + e.getMessage());
            e.printStackTrace();
            // 如果轉換失敗，返回原始內容
            return innerContent;
        }
    }

    /**
     * 處理 SOAP 回應
     * 
     * @param soapResponse SOAP 回應字串
     * @return 處理後的回應內容
     * @throws Exception 當處理回應發生錯誤時
     */
    private String processSOAPResponse(String soapResponse) throws Exception {
        try {
            // 檢查是否有 SOAP Fault
            if (soapResponse.contains("soap:Fault") || soapResponse.contains("faultstring")) {
                // 提取錯誤訊息
                String faultString = extractFaultString(soapResponse);
                log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                   .append("\t").append(GlobalVariable.HOST)
                   .append("\t[").append(this.getClass().getSimpleName())
                   .append("]\tSOAP Fault: ").append(faultString)
                   .append("\r\n");
                Utility.writeLog(GlobalVariable.logTrans, log);
                throw new Exception("SOAP Fault: " + faultString);
            }
            
            // 提取 SOAP Body 內容
            return extractSOAPBody(soapResponse);
            
        } catch (Exception e) {
            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(GlobalVariable.HOST)
               .append("\t[").append(this.getClass().getSimpleName())
               .append("]\tError processing SOAP response")
               .append("\r\n").append(ExceptionUtils.getStackTrace(e));
            Utility.writeLog(GlobalVariable.logTrans, log);
            throw e;
        }
    }
    
    /**
     * 提取 SOAP Fault 錯誤訊息
     */
    private String extractFaultString(String soapResponse) {
        try {
            int start = soapResponse.indexOf("<faultstring>");
            int end = soapResponse.indexOf("</faultstring>");
            if (start != -1 && end != -1) {
                return soapResponse.substring(start + 13, end);
            }
        } catch (Exception e) {
            // 忽略解析錯誤
        }
        return "未知的 SOAP 錯誤";
    }
    
    /**
     * 提取 SOAP Body 內容
     */
    private String extractSOAPBody(String soapResponse) {
        try {
            int bodyStart = soapResponse.indexOf("<soap:Body>");
            int bodyEnd = soapResponse.indexOf("</soap:Body>");
            
            if (bodyStart != -1 && bodyEnd != -1) {
                String bodyContent = soapResponse.substring(bodyStart + 11, bodyEnd);
                // 移除外層的命名空間標籤，返回純內容
                return bodyContent.trim();
            }
        } catch (Exception e) {
            // 如果解析失敗，返回原始回應
        }
        return soapResponse;
    }
    
    /**
     * 取得服務 URL
     */
    public String getServiceUrl() {
        return serviceUrl;
    }
    
    /**
     * 取得逾時設定
     */
    public int getTimeout() {
        return timeout;
    }
}
