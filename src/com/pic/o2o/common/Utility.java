package com.pic.o2o.common;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.StringWriter;
import java.io.Writer;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import org.apache.log4j.Logger;

public class Utility {
	public static synchronized void writeLog(String logType, StringBuffer logInfo) {
		try {
			Logger logger = Logger.getLogger(logType);
			logger.info(logInfo);
			logInfo.setLength(0);
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}

	public static String getDateTime(String format) {
		Calendar cal = Calendar.getInstance();
		SimpleDateFormat sdFormat = new SimpleDateFormat(format);
		return sdFormat.format(cal.getTime());
	}

	public static String InputStreamToString(InputStream steam) throws IOException {
		String result = null;
		if (steam != null) {
			Writer writer = new StringWriter();
			char[] buffer = new char[1024];
			Reader reader = new BufferedReader(new InputStreamReader(steam, "UTF-8"));
			int n;
			while ((n = reader.read(buffer)) != -1)
				writer.write(buffer, 0, n);
			result = writer.toString();
			steam.close();
		}
		return result;
	}
}
