package com.pic.o2o.common;

import java.io.StringWriter;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.HashMap;
import java.util.Map;

import javax.xml.soap.SOAPException;

import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.http.conn.ConnectTimeoutException;

import com.pic.o2o.common.Utility;

/**
 * SOAP 服務類別
 * 提供 SOAP 協議的高層級服務功能
 * 包含連線池管理、錯誤處理、日誌記錄等功能
 * 
 * <AUTHOR> OLTP System
 * @version 1.3
 */
public class SOAPService {
    
    private static Map<String, SOAPClient> clientPool = new HashMap<>();
    private static final Object poolLock = new Object();
    private static StringBuffer log = new StringBuffer();

    // 預設的日誌和主機設定（向後相容）
    private static String defaultHost = "UNKNOWN_HOST";
    private static String defaultLogTrans = "TRANSLOG";

    /**
     * 設定預設的主機名稱和日誌
     *
     * @param host 主機名稱
     * @param logTrans 日誌名稱
     */
    public static void setDefaultSettings(String host, String logTrans) {
        defaultHost = host;
        defaultLogTrans = logTrans;
    }
    
    /**
     * 發送 SOAP 請求（使用預設設定）
     *
     * @param serviceUrl SOAP 服務 URL
     * @param timeout 逾時時間（秒）
     * @param requestContent 請求內容
     * @return 回應內容
     * @throws Exception 當處理發生錯誤時
     */
    public static String sendSOAPRequest(String serviceUrl, int timeout, String requestContent) throws Exception {
        return sendSOAPRequest(serviceUrl, timeout, requestContent, null, defaultHost, defaultLogTrans);
    }

    /**
     * 發送 SOAP 請求（支援自訂 SOAPAction，使用預設設定）
     *
     * @param serviceUrl SOAP 服務 URL
     * @param timeout 逾時時間（秒）
     * @param requestContent 請求內容
     * @param soapAction SOAP Action 值（可為 null，會自動推斷）
     * @return 回應內容
     * @throws Exception 當處理發生錯誤時
     */
    public static String sendSOAPRequest(String serviceUrl, int timeout, String requestContent, String soapAction) throws Exception {
        return sendSOAPRequest(serviceUrl, timeout, requestContent, soapAction, defaultHost, defaultLogTrans);
    }

    /**
     * 發送 SOAP 請求（完全參數化版本）
     *
     * @param serviceUrl SOAP 服務 URL
     * @param timeout 逾時時間（秒）
     * @param requestContent 請求內容
     * @param soapAction SOAP Action 值（可為 null，會自動推斷）
     * @param host 主機名稱（用於日誌）
     * @param logTransName 日誌名稱
     * @return 回應內容
     * @throws Exception 當處理發生錯誤時
     */
    public static String sendSOAPRequest(String serviceUrl, int timeout, String requestContent, String soapAction,
                                        String host, String logTransName) throws Exception {
        String response = null;

        try {
            // 記錄請求開始
            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(host)
               .append("\t[").append(SOAPService.class.getSimpleName())
               .append("]\tStarting SOAP request to: ").append(serviceUrl)
               .append("\r\n");
            Utility.writeLog(logTransName, log);

            // 首先嘗試使用標準 SOAP 客戶端
            try {
                SOAPClient client = getSOAPClient(serviceUrl, timeout);
                client.setRequestContent(requestContent);

                // 設定 SOAPAction（如果有提供）
                if (soapAction != null && !soapAction.trim().isEmpty()) {
                    client.setSOAPAction(soapAction);
                    log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                       .append("\t").append(host)
                       .append("\t[").append(SOAPService.class.getSimpleName())
                       .append("]\tUsing custom SOAPAction: ").append(soapAction)
                       .append("\r\n");
                    Utility.writeLog(logTransName, log);
                }

                response = client.sendRequest();

            } catch (NoClassDefFoundError | ClassNotFoundException e) {
                // 如果標準 SOAP 客戶端因為依賴問題失敗，使用簡化版本
                // 注意：兩個客戶端現在產生相同格式的 SOAP 請求
                log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                   .append("\t").append(host)
                   .append("\t[").append(SOAPService.class.getSimpleName())
                   .append("]\tFalling back to SimpleSOAPClient due to dependency issue (same SOAP format)")
                   .append("\r\n");
                Utility.writeLog(logTransName, log);

                SimpleSOAPClient simpleClient = new SimpleSOAPClient(serviceUrl, timeout);
                simpleClient.setRequestContent(requestContent);

                // 設定 SOAPAction（如果有提供）
                if (soapAction != null && !soapAction.trim().isEmpty()) {
                    simpleClient.setSOAPAction(soapAction);
                }

                response = simpleClient.sendRequest();
            }
            
            // 記錄成功
            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(host)
               .append("\t[").append(SOAPService.class.getSimpleName())
               .append("]\tSOAP request completed successfully")
               .append("\r\n");
            Utility.writeLog(logTransName, log);
            
            return response;
            
        } catch (SocketTimeoutException e) {
            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(host)
               .append("\t[").append(SOAPService.class.getSimpleName())
               .append("]\tSOAP request timeout for URL: ").append(serviceUrl)
               .append("\r\n").append(ExceptionUtils.getStackTrace(e));
            Utility.writeLog(logTransName, log);
            throw new SocketTimeoutException("SOAP 服務請求逾時");

        } catch (ConnectTimeoutException e) {
            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(host)
               .append("\t[").append(SOAPService.class.getSimpleName())
               .append("]\tSOAP connection timeout for URL: ").append(serviceUrl)
               .append("\r\n").append(ExceptionUtils.getStackTrace(e));
            Utility.writeLog(logTransName, log);
            throw new ConnectTimeoutException("SOAP 服務連線逾時");

        } catch (ConnectException e) {
            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(host)
               .append("\t[").append(SOAPService.class.getSimpleName())
               .append("]\tSOAP connection failed for URL: ").append(serviceUrl)
               .append("\r\n").append(ExceptionUtils.getStackTrace(e));
            Utility.writeLog(logTransName, log);
            throw new ConnectException("SOAP 服務連線失敗");

        } catch (SOAPException e) {
            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(host)
               .append("\t[").append(SOAPService.class.getSimpleName())
               .append("]\tSOAP protocol error for URL: ").append(serviceUrl)
               .append("\r\n").append(ExceptionUtils.getStackTrace(e));
            Utility.writeLog(logTransName, log);
            throw new Exception("SOAP 協議錯誤: " + e.getMessage());

        } catch (Exception e) {
            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(host)
               .append("\t[").append(SOAPService.class.getSimpleName())
               .append("]\tGeneral SOAP error for URL: ").append(serviceUrl)
               .append("\r\n").append(ExceptionUtils.getStackTrace(e));
            Utility.writeLog(logTransName, log);
            throw e;
        }
    }
    
    /**
     * 取得或建立 SOAP 客戶端
     * 使用簡單的連線池機制來重用客戶端
     * 
     * @param serviceUrl 服務 URL
     * @param timeout 逾時時間
     * @return SOAP 客戶端
     */
    private static SOAPClient getSOAPClient(String serviceUrl, int timeout) {
        synchronized (poolLock) {
            String key = serviceUrl + "_" + timeout;
            SOAPClient client = clientPool.get(key);
            
            if (client == null) {
                client = new SOAPClient(serviceUrl, timeout);
                clientPool.put(key, client);
                
                log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
                   .append("\t").append(defaultHost)
                   .append("\t[").append(SOAPService.class.getSimpleName())
                   .append("]\tCreated new SOAP client for: ").append(serviceUrl)
                   .append("\r\n");
                Utility.writeLog(defaultLogTrans, log);
            }
            
            return client;
        }
    }
    
    /**
     * 清理連線池
     * 釋放所有快取的客戶端連線
     */
    public static void clearClientPool() {
        synchronized (poolLock) {
            clientPool.clear();
            
            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(defaultHost)
               .append("\t[").append(SOAPService.class.getSimpleName())
               .append("]\tSOAP client pool cleared")
               .append("\r\n");
            Utility.writeLog(defaultLogTrans, log);
        }
    }
    
    /**
     * 取得連線池狀態
     * 
     * @return 連線池中的客戶端數量
     */
    public static int getPoolSize() {
        synchronized (poolLock) {
            return clientPool.size();
        }
    }
    
    /**
     * 驗證 SOAP 服務是否可用
     * 
     * @param serviceUrl 服務 URL
     * @param timeout 逾時時間
     * @return true 如果服務可用，false 否則
     */
    public static boolean validateSOAPService(String serviceUrl, int timeout) {
        try {
            // 發送簡單的測試請求
            String testRequest = "<test>ping</test>";
            sendSOAPRequest(serviceUrl, timeout, testRequest);
            return true;
            
        } catch (Exception e) {
            log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
               .append("\t").append(defaultHost)
               .append("\t[").append(SOAPService.class.getSimpleName())
               .append("]\tSOAP service validation failed for: ").append(serviceUrl)
               .append("\r\n").append(ExceptionUtils.getStackTrace(e));
            Utility.writeLog(defaultLogTrans, log);
            return false;
        }
    }
    
    /**
     * 格式化 SOAP 錯誤訊息
     * 
     * @param e 例外物件
     * @param serviceUrl 服務 URL
     * @return 格式化的錯誤訊息
     */
    public static String formatSOAPError(Exception e, String serviceUrl) {
        StringWriter errorMsg = new StringWriter();
        
        errorMsg.append("SOAP 服務錯誤 - URL: ").append(serviceUrl).append("\n");
        errorMsg.append("錯誤類型: ").append(e.getClass().getSimpleName()).append("\n");
        errorMsg.append("錯誤訊息: ").append(e.getMessage()).append("\n");
        errorMsg.append("發生時間: ").append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\n");
        
        if (e instanceof SocketTimeoutException) {
            errorMsg.append("建議: 檢查網路連線或增加逾時時間\n");
        } else if (e instanceof ConnectException) {
            errorMsg.append("建議: 檢查服務是否正常運行或網路連線\n");
        } else if (e instanceof SOAPException) {
            errorMsg.append("建議: 檢查 SOAP 請求格式或服務端點配置\n");
        }
        
        return errorMsg.toString();
    }
    
    /**
     * 記錄 SOAP 服務統計資訊
     */
    public static void logServiceStatistics() {
        log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
           .append("\t").append(defaultHost)
           .append("\t[").append(SOAPService.class.getSimpleName())
           .append("]\tSOAP Service Statistics - Active clients: ").append(getPoolSize())
           .append("\r\n");
        Utility.writeLog(defaultLogTrans, log);
    }
}
