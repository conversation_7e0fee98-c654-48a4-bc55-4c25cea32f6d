package com.pic.oltp;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.management.ManagementFactory;
import java.util.ArrayList;
import java.util.UUID;

import javax.jms.ExceptionListener;
import javax.jms.JMSException;
import javax.jms.QueueConnection;
import javax.jms.QueueConnectionFactory;

import org.apache.log4j.Logger;
import org.joda.time.DateTime;

import com.pic.o2o.common.IQueueService;
import com.pic.o2o.common.QueueListener;
import com.pic.o2o.common.Utility;


public class EMSProcess implements ExceptionListener, IQueueService, Runnable{
	private StringBuffer log = new StringBuffer();
	private static final Logger syslog = Logger.getLogger("SYSLOG");
	
	//執行狀態與啟動時間
	private int RunningThread = 0;
	private DateTime startTime;
		
	/**
	 * EMS連線相關變數
	 */
	private int emsIndex = -1;
	private String emsHostname;
	private String emsURL;
	private String emsUserName;
	private String emsPassword;
	//private String emsClientId;
	private QueueConnection connection = null;
	
	
	/* QueueListener物件 */
	private QueueListener queueListener;
	private ArrayList<QueueListener> listenQueueList;
	
	/* Write Exception Detail Info Variables */
	private StringWriter wr = new StringWriter();
	private PrintWriter pWriter = new PrintWriter(wr);
	
	//存活在QueueListener訊息變數	
	private int ActiveMsgCount = 0;
	
	//建構子: 初始化EMS連線變數
	public EMSProcess(int emsIndex){
		this.emsIndex = emsIndex;
		emsHostname = GlobalVariable.EMS_HOSTNAME.get(emsIndex);
		emsURL = GlobalVariable.EMS_URL.get(emsIndex);
		emsUserName = GlobalVariable.EMS_USERNAME.get(emsIndex);
		emsPassword = GlobalVariable.EMS_PASSWORD.get(emsIndex);
	}
	
	@Override
	public void run() {
		try{
			init();
		} catch (JMSException | InterruptedException | IllegalThreadStateException e){
			e.printStackTrace(pWriter);
			
			/* 將錯誤訊息寫入file */
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST). 
				append("\t[").append(QueueService.class.getSimpleName()).append("]\t").append(wr.toString());
			Utility.writeLog(GlobalVariable.logTrans, log);
			
			//寫入syslog
			String uuid = UUID.randomUUID().toString();
			syslog.error("check translogs, UUID: " + uuid);
		} catch (Exception e) {
			e.printStackTrace(pWriter);

			/* 將錯誤訊息寫入file */
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
					.append("\t").append(GlobalVariable.HOST).append("\t[")
					.append(QueueService.class.getSimpleName()).append("]\t")
					.append(wr.toString());
			Utility.writeLog(GlobalVariable.logTrans, log);

			// 寫入syslog
			String uuid = UUID.randomUUID().toString();
			syslog.error("check translogs, UUID: " + uuid);
		}
	}
	
	public void init() throws InterruptedException, JMSException{
		initEMSObject();
		
		//設定啟動狀態與時間
		RunningThread = CountRunningThread();
		startTime = DateTime.now();
		
	}
	
	private int CountRunningThread(){
		int count = 0;
		
		if(listenQueueList == null)
			count = 0;
		else
			for (int i = 0 ; i < GlobalVariable.OLTP_THREAD; i++)
				if (listenQueueList.get(i).getStatus() == "enable")
					count++;
		
		return count;
	}
	
	public void initEMSObject() throws InterruptedException, JMSException,IllegalArgumentException{
		/* call createEMSConnection() Method 
		 * create EMS connection, if can't connect EMS then every 10 seconds retry */
		while(!createEMSConnection()){
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST).
				append("\t[").append(this.getClass().getSimpleName()).append("]\twait ").
				append(GlobalVariable.EMS_CONNECT_INTERVAL_SECOND).append(" seconds").append("\r\n");
			Utility.writeLog(GlobalVariable.logTrans, log);
			Thread.sleep(GlobalVariable.EMS_CONNECT_INTERVAL_SECOND * 1000);
		}
		
		createQueueListener();
		
		//set the exception listener
		connection.setExceptionListener(this);
				
		/* start the connection and log it*/
		connection.start();

		log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST).
			append("\t[").append(this.getClass().getSimpleName()).append("]\tConnection Success URL: tcp://").
			append(emsURL).append("\r\n");
		Utility.writeLog(GlobalVariable.logTrans, log);
    }
	
	private void createQueueListener()throws IllegalThreadStateException{
		//建立聽Queue List
		listenQueueList = new ArrayList<QueueListener>();
		
		
				
		/* Create Listen EMS Queue Thread */
		for (int i = 0 ; i < GlobalVariable.OLTP_THREAD; i++){	
			//OLTPAP物件
			OLTP qware = new OLTP();
			
			//建立聽Queue物件
			queueListener = new QueueListener(GlobalVariable.logTrans,GlobalVariable.HOST,connection, 
					qware, GlobalVariable.POS_IN_QUEUE, GlobalVariable.POS_OUT_QUEUE,
					this);
			
			listenQueueList.add(queueListener);

			/* 建立聽Queue的Thread */
			Thread thread = new Thread(queueListener);
			thread.start();
			
		}
	}
    
    /************
     * 建立EMS連線
     ************/
  	public boolean createEMSConnection(){
  		try{
  			QueueConnectionFactory factory = new com.tibco.tibjms.TibjmsQueueConnectionFactory(emsURL);
  			
  			connection = factory.createQueueConnection(emsUserName, emsPassword);
  			
  			//Set EMS Connection ClientID，ClientID Format is : package full name - PID
			connection.setClientID(this.getClass().getName().concat("-").concat(ManagementFactory.getRuntimeMXBean().getName().
					substring(0, ManagementFactory.getRuntimeMXBean().getName().indexOf("@"))));
  			
  			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST). 
  				append("\t[").append(this.getClass().getSimpleName()).append("]\tCreate Connection");
  			Utility.writeLog(GlobalVariable.logTrans, log);			
  			return true;
  		} catch (JMSException e){
  			e.printStackTrace(pWriter);
			
			/* 將錯誤訊息寫入file */
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST).
				append("\t[").append(this.getClass().getSimpleName()).append("]\t").append(wr.toString());
			Utility.writeLog(GlobalVariable.logTrans, log);
			
			//寫入syslog
			String uuid = UUID.randomUUID().toString();
			syslog.error("check translogs, UUID: " + uuid);
  			
  			return false;
  		} catch (Exception e){
  			e.printStackTrace(pWriter);
			
			/* 將錯誤訊息寫入file */
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST).
				append("\t[").append(this.getClass().getSimpleName()).append("]\t").append(wr.toString());
			Utility.writeLog(GlobalVariable.logTrans, log);
			
			//寫入syslog
			String uuid = UUID.randomUUID().toString();
			syslog.error("check translogs, UUID: " + uuid);
  			
  			return false;
  		}
  	}
  	
  	public void destoryEMSObject() throws JMSException{
  		//Close All Consumers
  		for (int i = 0 ; i < GlobalVariable.OLTP_THREAD; i++){
  			listenQueueList.get(i).closeConsumer();
  			listenQueueList.get(i).setStatus("disable");
  		}
  		
  		/* Reset EMS Variables */
    	listenQueueList = null;
    	
    	//Reset listenQ Object
    	queueListener = null;
  		
  		//Close EMS Connection
    	connection.close();
    	
    	//初始化connection
    	connection = null;
    }
  	
  	public void close() throws JMSException{
  		//關閉EMS物件(包含Queue Listener)
  		destoryEMSObject();
		
		//更新狀態, 啟動時間清空
  		RunningThread = CountRunningThread();
		startTime = null;
  		
  		/* Reset log variables */
    	log.setLength(0);
//    	syslog = Logger.getLogger("SYSLOG");
    	
  	}
	
	@Override
	public void onException(JMSException e) {
		try{
			close();
			
			/* 將JMS的錯誤訊息寫入file */
			e.printStackTrace(pWriter);
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST). 
				append("\t[").append(this.getClass().getSimpleName()).append("]\t").append(wr.toString());
			Utility.writeLog(GlobalVariable.logTrans, log);
			
			init();
		} catch (JMSException | InterruptedException ex){
			ex.printStackTrace(pWriter);
			
			/* 將錯誤訊息寫入file */
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST). 
				append("\t[").append(this.getClass().getSimpleName()).append("]\t").append(wr.toString());
			Utility.writeLog(GlobalVariable.logTrans, log);
			
			//寫入syslog
			String uuid = UUID.randomUUID().toString();
			syslog.error("check translogs, UUID: " + uuid);
		} catch (Exception ex){
			ex.printStackTrace(pWriter);
			
			/* 將錯誤訊息寫入file */
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST). 
				append("\t[").append(this.getClass().getSimpleName()).append("]\t").append(wr.toString());
			Utility.writeLog(GlobalVariable.logTrans, log);
			
			//寫入syslog
			String uuid = UUID.randomUUID().toString();
			syslog.error("check translogs, UUID: " + uuid);
		}
	}
	
	public String getEMSHostname(){
  		return emsHostname;
  	}
  	
  	public String getEMSIndex(){
  		return Integer.toString(emsIndex);
  	}
  	
  	/* 取得目前中的Message數  */
  	public String getActiveMsgCount(){
  		return Integer.toString(ActiveMsgCount);
  	}
  	
  	/* 取得目前Thread數  */
  	public String getRunningThread() {
		return Integer.toString(RunningThread);
	}

	public DateTime getStartTime() {
		return startTime;
	}
	
	public String getStartTimeString(String pattern){
		if(null == startTime){
			return "";
		}
		else{
			return startTime.toString(pattern);
		}
	}
	
	public void enableQueueListeners() throws JMSException{
		//Create Consumer
		for (int i = 0 ; i < GlobalVariable.OLTP_THREAD; i++){
			listenQueueList.get(i).createConsumer();
			listenQueueList.get(i).setStatus("enable");
		}
		
		RunningThread = CountRunningThread();
		//重新啟動連線
		connection.start();
	}
	
	public void disableQueueListeners() throws JMSException {
		connection.stop();
		
		//Close Consumer
		for (int i = 0 ; i < GlobalVariable.OLTP_THREAD; i++){
			listenQueueList.get(i).closeConsumer();
			listenQueueList.get(i).setStatus("disable");
		}
		
		RunningThread = CountRunningThread();
	}

    /* 進行加ActiveMsgCount變數加1 */
	public int additionActiveMsg(){
		return ++ActiveMsgCount;
	}
	
	/* 進行加ActiveMsgCount變數減1 */
	public int subtractionActiveMsg(){
		return --ActiveMsgCount;
	}
}