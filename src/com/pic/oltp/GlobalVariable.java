package com.pic.oltp;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.commons.lang.exception.ExceptionUtils;

import com.pic.o2o.common.Utility;
import com.tibco.security.ObfuscationEngine;

public class GlobalVariable{
	// log4j用變數
	private static final StringBuffer log = new StringBuffer();
	//外部設定檔屬性
	static private PropertiesConfiguration PROPS;
	
	public static WatchService watcher = null;
	
	//log4j變數
	public static String logTrans = "TRANSLOG";
	static String logConsole = "CONSOLELOG";
	
	//EMS相關變數
	static int EMS_CONNECT_INTERVAL_SECOND;
	static String POS_IN_QUEUE = null;
	static String POS_OUT_QUEUE = null;
	

	
	static List<String> EMS_HOSTNAME;
	static List<String> EMS_URL;
	static List<String> EMS_USERNAME;
	static List<String> EMS_PASSWORD = new ArrayList<String>();
	
	//密鑰
	static String KEY;
	
	//寫檔用路徑
	static String XML_LOG_PATH = null;
	
	//主機名稱變數
	public static String HOST = null;
	
	
	//THREAD數
	static int OLTP_THREAD;
	
	//Consosle變數
	static int HTTP_PORT;
	static String HTTP_CONTEXT = null;
	
	//2020.08.07 新增錯誤代碼寫檔路徑
	static String ERROR_CODE_FILEPATH = null;
	static String ERROR_CODE_MONITOR = null;
	
	static Map<String,String> TRANSFILEMAP = new HashMap<String, String>();
	static private FileReader TRANSFILEREADER = null;
	
	static{
		/* 讀取外部設定檔 */
		loadProperties();
		    	
		/* 設定變數數值 */
		setVariables();
	}
	
	/* 讀取外部設定檔*/
	public static void loadProperties() {
		try {
			PROPS = new PropertiesConfiguration();
			PROPS.setEncoding("UTF-8");
			if(System.getProperty("os.name")==null){
				PROPS.setFile(new File("/o2odata/OLTP/config/config.properties"));
				TRANSFILEREADER = new FileReader("/o2odata/OLTP/config/transfile.txt");
			}else if(System.getProperty("os.name").equals("Linux")){
				PROPS.setFile(new File("/o2odata/OLTP/config/config.properties"));
				TRANSFILEREADER = new FileReader("/o2odata/OLTP/config/transfile.txt");
			}else{
				PROPS.setFile(new File("C:\\o2odata\\OLTP\\config\\config.properties"));
				TRANSFILEREADER = new FileReader("C:\\o2odata\\OLTP\\config\\transfile.txt");
			}
			PROPS.setDelimiterParsingDisabled(true);
			PROPS.setThrowExceptionOnMissing(true);
			PROPS.load();
			
			BufferedReader BufferedReader = new BufferedReader(TRANSFILEREADER);
			TRANSFILEMAP.clear();
			while (BufferedReader.ready()) {
				String line = BufferedReader.readLine();
				if (line != null && !line.trim().isEmpty() && !line.trim().startsWith("#")) {
					// 解析完整的配置行，支援新的 SOAPAction 欄位
					String[] transfileString = line.split(",", 2);
					if (transfileString.length >= 2) {
						TRANSFILEMAP.put(transfileString[0], transfileString[1]);
					}
				}
			}
			TRANSFILEREADER.close();
			
		} catch (ConfigurationException | SecurityException | IllegalArgumentException e) {
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
					.append("\t").append("Error Message:\n")
					.append(ExceptionUtils.getStackTrace(e));
			Utility.writeLog(logTrans, log);
		} catch (Exception e){
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
					.append("\t").append("Error Message:\n")
					.append(ExceptionUtils.getStackTrace(e));
			Utility.writeLog(logTrans, log);
		}
	}

	/* 設定變數值 */
	public static void setVariables(){
		try{
			//EMS相關變數
			EMS_URL = Arrays.asList(PROPS.getStringArray("EMS.URL"));
			EMS_HOSTNAME = Arrays.asList(PROPS.getStringArray("EMS.HOSTNAME"));
			EMS_USERNAME = Arrays.asList(PROPS.getStringArray("EMS.USERNAME"));
			EMS_PASSWORD = Arrays.asList(PROPS.getStringArray("EMS.PASSWORD"));
			
			POS_IN_QUEUE = PROPS.getString("POS.IN.QUEUE");
			POS_OUT_QUEUE = PROPS.getString("POS.OUT.QUEUE");

			
			EMS_CONNECT_INTERVAL_SECOND = PROPS.getInt("EMS.CONNECT.INTERVAL.SECOND");
			

			
			//寫檔用路徑
			XML_LOG_PATH = PROPS.getString("XML.LOG.PATH");
			
			//主機變數
			HOST = PROPS.getString("PIC.HOST");
			
			//執行序變數
			OLTP_THREAD = PROPS.getInt("OLTP.THREAD");
			
			//DisableQueue變數
			HTTP_PORT = PROPS.getInt("HTTP.PORT");
			HTTP_CONTEXT = PROPS.getString("HTTP.CONTEXT");
			
			//錯誤代碼寫檔路徑
			ERROR_CODE_FILEPATH = PROPS.getString("ERROR.CODE.FILEPATH");
			
			//需監控的錯誤代碼
			ERROR_CODE_MONITOR = PROPS.getString("ERROR.CODE.MONITOR");
			
			//金鑰
			KEY = java.lang.String.valueOf(ObfuscationEngine.decrypt(PROPS.getString("KEY")));
			
			//解密
			byte[] key = KEY.getBytes("UTF-8");
			SecretKeySpec skeySpec = new SecretKeySpec(key, "AES");
			Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
			cipher.init(Cipher.DECRYPT_MODE, skeySpec);
			
			for(int i=0;i<EMS_USERNAME.size();i++) {

				EMS_USERNAME.set(i, new String(cipher.doFinal(Base64.getDecoder().decode(EMS_USERNAME.get(i)))));
				EMS_PASSWORD.set(i, new String(cipher.doFinal(Base64.getDecoder().decode(EMS_PASSWORD.get(i)))));

			}
			
			
		} catch (NoSuchElementException e) {
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
				.append("\t").append("Error Message:\n")
				.append(ExceptionUtils.getStackTrace(e));
			Utility.writeLog(logTrans, log);
		} catch (Exception e) {
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
					.append("\t").append("Error Message:\n")
					.append(ExceptionUtils.getStackTrace(e));
			Utility.writeLog(logTrans, log);
		}
		
	}
	
	public void filePoller() throws IOException{
		
		watcher = FileSystems.getDefault().newWatchService();
		String path="";
		String filename="";
		if(System.getProperty("os.name")==null){
			path="/o2odata/OLTP/config/";
		}else if(System.getProperty("os.name").equals("Linux")){
			path="/o2odata/OLTP/config/";
		}else{
			path="C:\\o2odata\\OLTP\\config\\";
		}
		Path dir = Paths.get(path);
		dir.register(watcher,
		    		StandardWatchEventKinds.ENTRY_MODIFY);
		while(true) {
		try {
			WatchKey key;
			key = watcher.take();
			//key.pollEvents();
            for (WatchEvent<?> event : key.pollEvents()) {
                filename=event.context().toString();
            }
			key.reset();
			loadProperties();
			setVariables();
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
			.append("\t").append("Reload Config "+filename+"\n");
			Utility.writeLog(logTrans, log);
			} catch (ClosedWatchServiceException e) {
				break;
			} catch (InterruptedException e) {
				log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
				.append("\t").append("Error Message:\n")
				.append(ExceptionUtils.getStackTrace(e));
				Utility.writeLog(logTrans, log);
			}
		}
		
	}

}
 