package com.pic.oltp;

import java.util.Scanner;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;

import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.ParseException;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import com.pic.o2o.common.Utility;

public class O2OConsole {
	public static void main(String[] args){
		
		/* Write Exception Detail Info Variables */
		Writer wr = new StringWriter();
		PrintWriter pWriter = new PrintWriter(wr);
		
		//log變數
		StringBuffer log = new StringBuffer();
		
		try {
			CloseableHttpClient httpClient = HttpClients.createDefault();
	
			//POST參數清單
			List<NameValuePair> nvps = new ArrayList <NameValuePair>();
			nvps.add(new BasicNameValuePair("action", "getList"));
			
			//發出HTTP訊息(使用POST method), 取得目前Listener清單內容
			sendHTTPPost(httpClient, nvps);
			Scanner in = new Scanner(System.in);
		
			while(true) {
				int command;
				System.out.println("\r\n(1)List (2)Enable (3)Disable (4)Close (0)Exit\r\n");
				
				//接收command
				String newLine = "";
				newLine = getConsoleLine("command> ");
					
				try{
					command = Integer.parseInt(newLine);
				}
				catch(Exception e){
					System.out.println("Error: Invalid command '" + newLine + "'");
					continue;
				}

				nvps.clear();
				
				//解析command指令
				switch (command) {
					case 1:
						nvps.add(new BasicNameValuePair("action", "getList"));
						sendHTTPPost(httpClient, nvps);
						break;
							
					case 2:
						nvps.add(new BasicNameValuePair("action", "enableQueue"));
						newLine = getConsoleLine("Queue Name> ");
						
						nvps.add(new BasicNameValuePair("queueName", newLine));	
						newLine = getConsoleLine("Are you sure (yes,no)? ");
						if("y".equals(newLine) || "yes".equals(newLine))
							sendHTTPPost(httpClient, nvps);
						else
							System.out.println("Command aborted");
						break;
							
					case 3:
						nvps.add(new BasicNameValuePair("action", "disableQueue"));
						newLine = getConsoleLine("Queue Name> ");
						
						nvps.add(new BasicNameValuePair("queueName", newLine));						
						newLine = getConsoleLine("Are you sure (yes,no)? ");
						if("y".equals(newLine) || "yes".equals(newLine))
							sendHTTPPost(httpClient, nvps);
						else
							System.out.println("Command aborted");
						break;
						
					case 4:
						nvps.add(new BasicNameValuePair("action", "close"));					
						newLine = getConsoleLine("Are you sure (yes,no)? ");
						if("y".equals(newLine) || "yes".equals(newLine))
							sendHTTPPost(httpClient, nvps);
						else
							System.out.println("Command aborted");
						break;
												
					default:
						break;
				}       			
				//結束迴圈, 中止程式運作
				if(command == 0)
					break;
			}
			in.close();
		} catch (IllegalArgumentException | UnsupportedOperationException
				| ClassCastException | NoSuchElementException | ParseException
				| IOException | IllegalStateException e) {
			e.printStackTrace(pWriter);

			/* 將錯誤訊息寫入file */
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
					.append("\t").append(GlobalVariable.HOST).append("\t[")
					.append(O2OConsole.class.getSimpleName()).append("]\t")
					.append(wr.toString());
			Utility.writeLog(GlobalVariable.logConsole, log);
		} catch (Exception e) {
			e.printStackTrace(pWriter);

			/* 將錯誤訊息寫入file */
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
					.append("\t").append(GlobalVariable.HOST).append("\t[")
					.append(O2OConsole.class.getSimpleName()).append("]\t")
					.append(wr.toString());
			Utility.writeLog(GlobalVariable.logConsole, log);
		}
	}
	
	@SuppressWarnings("resource")
	public static String getConsoleLine(String text) throws NoSuchElementException,IllegalStateException{
		Scanner in = new Scanner(System.in);
		String newLine = "";
		
		do {
			System.out.print(text);
			if(in.hasNextLine())
				newLine = in.nextLine();
		}
		while(newLine.trim().length()==0);
		
		return newLine;
	}
	
	public static void sendHTTPPost(CloseableHttpClient httpClient, List<NameValuePair> nvps) throws ClientProtocolException, IOException,ParseException{
		HttpPost httpost = new HttpPost("http://localhost:".concat(String.valueOf(GlobalVariable.HTTP_PORT)).
				concat(GlobalVariable.HTTP_CONTEXT));          
        httpost.setEntity(new UrlEncodedFormEntity(nvps, "UTF-8"));  
        
        HttpResponse response = httpClient.execute(httpost);
        String responseString = EntityUtils.toString(response.getEntity());
        EntityUtils.consume(response.getEntity());
        
        if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
            // 如果回傳是 200 OK 的話才輸出
            System.out.print(responseString);
        } 
        else {
            System.out.println(response.getStatusLine());
        }
	}
}
