package com.pic.oltp;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.UUID;

import javax.jms.JMSException;

import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.log4j.Logger;

import com.pic.o2o.common.HTTPService;
import com.pic.o2o.common.SdkException;
import com.pic.o2o.common.Utility;

public class QueueService{
	//log變數
	private static final StringBuffer log = new StringBuffer();
	private static Logger syslog = Logger.getLogger("SYSLOG");
	
	private static Map<String, EMSProcess> emsMap = new LinkedHashMap<String, EMSProcess>();
	
	public static void main (String[] args){
		StringWriter wr = new StringWriter();
		PrintWriter pWriter = new PrintWriter(wr);	
		
		try {
			new QueueService();
			new HTTPService(GlobalVariable.HTTP_PORT, GlobalVariable.HOST, GlobalVariable.logConsole, 
					GlobalVariable.HTTP_CONTEXT, new O2OHandler());
			new GlobalVariable().filePoller();
		} catch (UnsupportedOperationException | ClassCastException
				| IllegalArgumentException | IOException | SdkException e) {
			e.printStackTrace(pWriter);
			/* 將錯誤訊息寫入file */
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
					.append("\t").append(GlobalVariable.HOST).append("]\t[")
					.append(QueueService.class.getSimpleName()).append("]\t")
					.append(wr.toString());
			Utility.writeLog(GlobalVariable.logTrans, log);

			// 寫入syslog
			String uuid = UUID.randomUUID().toString();
			syslog.error("check translogs, UUID: " + uuid);
		} catch (Exception e) {
			e.printStackTrace(pWriter);
			
			/* 將錯誤訊息寫入file */
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST). 
				append("]\t[").append(QueueService.class.getSimpleName()).append("]\t").append(wr.toString());
			Utility.writeLog(GlobalVariable.logTrans, log);
			
			//寫入syslog
			String uuid = UUID.randomUUID().toString();
			syslog.error("check translogs, UUID: " + uuid);
		} 
	}
	
	public QueueService() throws UnsupportedOperationException,
			IllegalThreadStateException, ClassCastException,
			IllegalArgumentException {
		//createQueueListener();
		for(int i=0; i<GlobalVariable.EMS_URL.size(); i++){
			String hostname = GlobalVariable.EMS_HOSTNAME.get(i);
			EMSProcess emsProcess = new EMSProcess(i);
			Thread thread = new Thread(emsProcess, hostname.concat("-thread"));
			emsMap.put(hostname, emsProcess);
			
			//啟動, 使用1個Thread
			thread.start();
		}
	}

    /* Get Main List */
	public static String getList(String source){
		StringBuffer result = new StringBuffer();
		String leftAlignFormat = "| %-20s | %-10s | %-20s | %-20s |%n";
    	
		//印出list內容
    	if(null != source && "web".equals(source)) {
    		for(EMSProcess process : emsMap.values()){
    			result.append(GlobalVariable.POS_IN_QUEUE + ",");
    			result.append(process.getRunningThread().concat(","));
    			result.append(process.getActiveMsgCount() + ",");
    		}
			result.setLength(result.length()-1);
		} else{
    		for(EMSProcess process : emsMap.values())
				result.append(String.format(leftAlignFormat, GlobalVariable.POS_IN_QUEUE, 
						process.getEMSHostname(), process.getRunningThread(), process.getActiveMsgCount()));
		}
    	return result.toString();
    }

	/* Enable Queue */
    public static String enableQueue() throws JMSException{
    	StringBuffer result = new StringBuffer("\r\n");
    	
    	for(EMSProcess process : emsMap.values()){
    		if(process.getRunningThread().equals(Integer.toString(GlobalVariable.OLTP_THREAD))) {    		
        		/* 寫入log記錄Queue已經enable */
        		log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST).append("\t[").
        			append(QueueService.class.getSimpleName()).append("]\tQueue '").append(GlobalVariable.POS_IN_QUEUE).append("' already enable\r\n");
        		Utility.writeLog(GlobalVariable.logConsole, log);
        		
        		result.append((char)27 + "[33m");
        		result.append("EMS '" + process.getEMSHostname() + "' ");
        		result.append("Queue '" + GlobalVariable.POS_IN_QUEUE + "' already enable\r\n");
        		result.append((char)27 + "[0m");
        		//return "Queue '" + GlobalVariable.EMS_IN_QUEUE + "' already enable\r\n";
        	} else {
        		//Create Consumer
        		process.enableQueueListeners();
        			    		
        		/* 寫入log記錄Queue已enable */
        		log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST).append("\t[").
        			append(QueueService.class.getSimpleName()).append("]\tQueue: ").append(GlobalVariable.POS_IN_QUEUE).append(" has been enable\r\n");
        		Utility.writeLog(GlobalVariable.logConsole, log);
    	
        		result.append((char)27 + "[32m");
        		result.append("EMS '" + process.getEMSHostname() + "' ");
        		result.append("Queue : " + GlobalVariable.POS_IN_QUEUE + " has been enabled\r\n");
        		result.append((char)27 + "[0m");
        		//return "Queue : " + GlobalVariable.EMS_IN_QUEUE + " has been enabled\r\n";
        	}
    	}
    	
    	return result.toString();
    }
	
	/* Disable Queue */
    public static String disableQueue() throws JMSException{
    	StringBuffer result = new StringBuffer();
    	
    	for(EMSProcess process : emsMap.values()){
    		if(process.getRunningThread().equals(Integer.toString(0))) {
        		/* 寫入log記錄Queue已經enable */
        		log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST).append("\t[").
    				append(QueueService.class.getSimpleName()).append("]\tQueue '").append(GlobalVariable.POS_IN_QUEUE).append("' already disable\r\n");
    	    	Utility.writeLog(GlobalVariable.logConsole, log);
    	    		
    	    	result.append((char)27 + "[33m");
    	    	result.append("EMS '" + process.getEMSHostname() + "' ");
        		result.append("Queue '" + GlobalVariable.POS_IN_QUEUE + "' already disable\r\n");
        		result.append((char)27 + "[0m");
        		//return "Queue '" + GlobalVariable.EMS_IN_QUEUE + "' already disable\r\n";
        	} else {
        		//
        		process.disableQueueListeners();
    	    		
        		/* 寫入log記錄Queue已disable*/
        		log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST).append("\t[").
    				append(QueueService.class.getSimpleName()).append("]\tQueue: ").append(GlobalVariable.POS_IN_QUEUE).append(" has been disable\r\n");
        		Utility.writeLog(GlobalVariable.logConsole, log);
    	
        		result.append((char)27 + "[32m");
        		result.append("EMS '" + process.getEMSHostname() + "' ");
        		result.append("Queue : " + GlobalVariable.POS_IN_QUEUE + " has been disabled\r\n");
        		result.append((char)27 + "[0m");
        		//return "Queue: " + GlobalVariable.EMS_IN_QUEUE + " has been disabled\r\n";
        	}
    	}
    	
    	return result.toString();
    }

    /* Close相關物件 */
    public static void close() throws JMSException,IllegalArgumentException{
    	//closeEMSConnection();
    	for(EMSProcess process : emsMap.values())
  			process.close();
    	/* Close HTTPServer Connection */
    	HTTPService.close();
    	try {
			GlobalVariable.watcher.close();
		} catch (IOException e) {
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
			.append("\t").append("Error Message:\n")
			.append(ExceptionUtils.getStackTrace(e));
			Utility.writeLog(GlobalVariable.logTrans, log);
		}
    }
    
    /*****************
	 * getter / setter
	 *****************/
	public static EMSProcess getEMSProcess(String emsHostName) throws EMSProcessNotFoundException{
		EMSProcess process = emsMap.get(emsHostName);
		
		if(null == process)
			throw new EMSProcessNotFoundException(emsHostName);
		
		return process;
	}
}