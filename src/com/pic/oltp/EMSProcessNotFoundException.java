package com.pic.oltp;

public class EMSProcessNotFoundException extends Exception{
	private static final long serialVersionUID = 5826024179600943414L;
	
	private String hostName;
	
	public EMSProcessNotFoundException(String hostName){
		this.hostName = hostName;
	}
	
	@Override
    public String getMessage() {
		if(null != hostName)
			return "EMS process '" + hostName + "' not found";
		else
			return super.getMessage();
    }
}
