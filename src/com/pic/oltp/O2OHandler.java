package com.pic.oltp;

import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.util.HashMap;
import java.util.Map;

import javax.jms.JMSException;

import com.pic.o2o.common.Utility;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;

/*
 * 處理接收的O2O Console HTTP Request
 */
public class O2OHandler implements HttpHandler {
	
	public void handle(HttpExchange t){       
		/* 接收與處理HTTP Request*/
		String HTTPBody = null;
		StringBuffer response = new StringBuffer();
		
		/* Write Exception Detail Info Variables */
		Writer wr = new StringWriter();
		PrintWriter pWriter = new PrintWriter(wr);
		
		//log變數
		StringBuffer log = new StringBuffer();
		
		try {
			HTTPBody = Utility.InputStreamToString(t.getRequestBody());
			Boolean IsServiceRunning = true;
			
			//處理POST HTTP Request
			if(t.getRequestMethod().equals("POST")) {
				log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST).
					append("\t[").append(this.getClass().getSimpleName()).append("]\tHTTP[POST] Request Parameter: ").append(HTTPBody);
				
				//寫入Log
				Utility.writeLog(GlobalVariable.logConsole, log);
				
				//解析參數
				Map<String, String> params = new HashMap<String, String>();				
				String[] temp = HTTPBody.replace("%2C", ",").split("&");
				
				for(String param : temp){
					params.put(param.split("=")[0], param.split("=")[1]);
				}

				if(params.get("action").equals("getList")) {
					getList(params.get("source"), response);
				}
				else if(params.get("action").equals("enableQueue")) {
					enableQueue(params, response);
				}
				else if(params.get("action").equals("disableQueue")) {
					disableQueue(params, response);
				}
				else if(params.get("action").equals("close")) {
					close(params);
					IsServiceRunning = false;
				}
			}
			//處理GET Request
			else if(t.getRequestMethod().equals("GET")) {
				log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS")).append("\t").append(GlobalVariable.HOST).
					append("\t").append(this.getClass().getSimpleName()).append("HTTP[GET] Request Parameter: ").append(HTTPBody);
				Utility.writeLog(GlobalVariable.logConsole, log);
				
				//解析參數
				int index = t.getRequestURI().toString().indexOf("?");
				Map<String, String> params = new HashMap<String, String>();
				String[] temp = t.getRequestURI().toString().substring(index+1).split("&");
				
				for(String param : temp) {
					params.put(param.split("=")[0], param.split("=")[1]);
				}
				
				if(params.get("action").equals("getList")) {
					getList(params.get("source"), response);
				}
				else if(params.get("action").equals("enableQueue")) {
					enableQueue(params, response);
				}
				else if(params.get("action").equals("disableQueue")) {
					disableQueue(params, response);
				}
				else if(params.get("action").equals("close")) {
					close(params);
					IsServiceRunning = false;
				}
			}
			//處理HEAD Request
			else if(t.getRequestMethod().equals("HEAD")){
				t.sendResponseHeaders(200, response.length());
				IsServiceRunning = false;
			}
			
			if(IsServiceRunning)
			{
				t.sendResponseHeaders(200, response.length());
				OutputStream os = t.getResponseBody();
				os.write(response.toString().getBytes());
				os.close();
			}
		} catch (IOException | JMSException e) {
			e.printStackTrace(pWriter);

			// 將錯誤訊息寫入file
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
					.append("\t").append(GlobalVariable.HOST).append("\t[")
					.append(this.getClass().getSimpleName()).append("]\t")
					.append(wr.toString());
			Utility.writeLog(GlobalVariable.logConsole, log);
		} catch (Exception e) {
			e.printStackTrace(pWriter);

			// 將錯誤訊息寫入file
			log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
					.append("\t").append(GlobalVariable.HOST).append("\t[")
					.append(this.getClass().getSimpleName()).append("]\t")
					.append(wr.toString());
			Utility.writeLog(GlobalVariable.logConsole, log);
		}
    }
	
	private void getList(String source, StringBuffer response){
		response.append(QueueService.getList(source));
	}
	
	private void enableQueue(Map<String, String> params, StringBuffer response) throws JMSException{
		response.append(QueueService.enableQueue());
	}
	
	private void disableQueue(Map<String, String> params, StringBuffer response) throws JMSException{
		response.append(QueueService.disableQueue());
	}
	
	private void close(Map<String, String> params) throws JMSException{
		QueueService.close();
	}	
}