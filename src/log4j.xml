<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">
	<!--Translog-->
	<appender name="translog" class="org.apache.log4j.rolling.RollingFileAppender">
		<param name="threshold" value="INFO" />
		<param name="immediateFlush" value="true"/>
		<param name="encoding" value="UTF-8"/>
		<param name="append" value="true" />
		<rollingPolicy class="org.apache.log4j.rolling.TimeBasedRollingPolicy">
        	<param name="FileNamePattern" value="/o2odata/OLTP/logs/OLTPtrans_%d{yyyyMMdd}.log" />
		</rollingPolicy>
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern" value="%m%n" />
		</layout>
	</appender>
	<!--O2OConsole Log-->
	<appender name="o2oconsole" class="org.apache.log4j.rolling.RollingFileAppender">
		<param name="threshold" value="INFO" />
		<param name="append" value="true" />
		<param name="immediateFlush" value="true" />
		<param name="encoding" value="UTF-8"/>
		<param name="append" value="true" />
		<rollingPolicy class="org.apache.log4j.rolling.TimeBasedRollingPolicy">
        	<param name="FileNamePattern" value="/o2odata/OLTP/logs/console_%d{yyyyMMdd}.log" />
		</rollingPolicy>
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern" value="%m%n" />
		</layout>
	</appender>
	<!--SYSLOG-->
	<appender name="syslog" class="org.apache.log4j.net.SyslogAppender">
		<errorHandler class="org.apache.log4j.helpers.OnlyOnceErrorHandler" />
		<!--寫入syslog的主機位置參數-->
		<param name="SyslogHost" value="localhost" />
		<!--寫入syslog的type參數，tyep:USER, MAIL, DAEMON.....，預設為USER，可以不用特別指定，若有需可自行決定型態 -->
		<param name="Facility" value="USER"/>
		<!--是否要顯示type參數-->
		<param name="FacilityPrinting" value="false"/>
		<param name="Threshold" value="ERROR"/>
		<!--寫入syslog的標籤參數，timestamp和host name-->
		<param name="Header" value="true"/>
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern" value="[O2O_OLTP_WARNING] %m" />
		</layout>
	</appender>
	<logger name="TRANSLOG">
		<level value="INFO" />
		<appender-ref ref="translog" />
	</logger>
	<logger name="CONSOLELOG">
		<level value="INFO" />
		<appender-ref ref="o2oconsole" />
	</logger>
	<logger name="SYSLOG">
		<level value="ERROR" />
		<appender-ref ref="syslog" />
	</logger>
	<root>
	<!--Log訊息的Level: DEBUG, INFO, WARN, ERROR, FATAL-->
		<priority value="INFO"/>
	</root>
</log4j:configuration>