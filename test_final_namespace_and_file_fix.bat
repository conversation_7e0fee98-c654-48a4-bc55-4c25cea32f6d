@echo off
echo ===== 最終版命名空間移除和檔案寫入修復測試 =====
echo.
echo 測試目標：修復兩個關鍵問題的最終版本
echo 問題 1：命名空間移除仍有問題（保留 XML 結構完整性）
echo 問題 2：檔案寫入路徑和邏輯修正（與 OLTP.java 一致）
echo 修復內容：
echo   1. 修復命名空間移除，保留 XML 結構完整性
echo   2. 使用與 OLTP.java 一致的檔案寫入邏輯
echo   3. append 回覆內容到現有 SOAP 檔案
echo   4. 使用正確的檔案路徑：o2odata\OLTP\logs\XML\
echo   5. 檔案格式：timestamp_TERMINO_SOAP.txt
echo ===================================================
echo.

REM 設定 CLASSPATH
set CLASSPATH=libs\*;bin

echo 1. 檢查必要目錄...
if not exist "bin" mkdir bin
if not exist "bin\com\pic\o2o\common" mkdir bin\com\pic\o2o\common
if not exist "test_output" mkdir test_output
if not exist "test_output\o2odata\OLTP\logs\XML" mkdir test_output\o2odata\OLTP\logs\XML

echo.
echo 2. 編譯修復後的程式碼...

echo 編譯 QueueListener.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\QueueListener.java
if %ERRORLEVEL% neq 0 (
    echo ❌ QueueListener.java 編譯失敗
    echo 可能是最終版修復代碼有問題
    goto :error
) else (
    echo ✅ QueueListener.java 編譯成功
)

echo.
echo 3. 創建最終版修復測試程式...

REM 創建測試程式
echo import com.pic.o2o.common.QueueListener; > TestFinalFix.java
echo import java.lang.reflect.Method; >> TestFinalFix.java
echo import java.lang.reflect.Field; >> TestFinalFix.java
echo import java.io.File; >> TestFinalFix.java
echo import java.io.FileWriter; >> TestFinalFix.java
echo import java.io.IOException; >> TestFinalFix.java
echo. >> TestFinalFix.java
echo public class TestFinalFix { >> TestFinalFix.java
echo     public static void main(String[] args) { >> TestFinalFix.java
echo         try { >> TestFinalFix.java
echo             System.out.println("=== 最終版命名空間移除和檔案寫入修復測試 ==="); >> TestFinalFix.java
echo             System.out.println(); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             // 創建 QueueListener 實例 >> TestFinalFix.java
echo             QueueListener listener = new QueueListener(); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             // 設定測試配置 >> TestFinalFix.java
echo             setupTestConfiguration(listener); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             // 測試案例 1：命名空間移除（保留結構完整性） >> TestFinalFix.java
echo             System.out.println("=== 測試案例 1：命名空間移除（保留結構完整性）==="); >> TestFinalFix.java
echo             String complexXml = "^<ManageTerminalResponse xmlns=\"http://ticketxpress.com.tw/\"^>" + >> TestFinalFix.java
echo                                 "^<ManageTerminalResult xmlns:a=\"http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common\" xmlns:i=\"http://www.w3.org/2001/XMLSchema-instance\"^>" + >> TestFinalFix.java
echo                                 "^<a:Checksum^>a87519574d731f03955b107379c9c5be^</a:Checksum^>" + >> TestFinalFix.java
echo                                 "^<a:Message^>Success^</a:Message^>" + >> TestFinalFix.java
echo                                 "^<a:ResponseCode^>0000^</a:ResponseCode^>" + >> TestFinalFix.java
echo                                 "^<a:ServerDate^>20250729^</a:ServerDate^>" + >> TestFinalFix.java
echo                                 "^<a:ServerTime^>152336^</a:ServerTime^>" + >> TestFinalFix.java
echo                                 "^<a:WorkKey^>eK+DO15GrIpkI/2muc5i3YryM2wrzTMYQQfoSELTw/833SOEtTXwSQ==^</a:WorkKey^>" + >> TestFinalFix.java
echo                                 "^</ManageTerminalResult^>" + >> TestFinalFix.java
echo                                 "^</ManageTerminalResponse^>"; >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             String cleanedXml = testRemoveNamespaces(listener, complexXml, "複雜命名空間"); >> TestFinalFix.java
echo             System.out.println(); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             // 測試案例 2：創建模擬的現有 SOAP 檔案 >> TestFinalFix.java
echo             System.out.println("=== 測試案例 2：創建模擬的現有 SOAP 檔案 ==="); >> TestFinalFix.java
echo             String testTermino = "TEST_FINAL_789"; >> TestFinalFix.java
echo             String mockSOAPFileName = createMockSOAPFile(testTermino); >> TestFinalFix.java
echo             System.out.println("模擬 SOAP 檔案: " + mockSOAPFileName); >> TestFinalFix.java
echo             System.out.println(); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             // 測試案例 3：檔案 append 功能測試 >> TestFinalFix.java
echo             System.out.println("=== 測試案例 3：檔案 append 功能測試 ==="); >> TestFinalFix.java
echo             String replyXml = "^<?xml version=\"1.0\" encoding=\"utf-8\"?^>" + >> TestFinalFix.java
echo                               "^<ns0:OLTP xmlns:ns0=\"http://7-11.com.tw/online\"^>" + >> TestFinalFix.java
echo                               "^<ns0:HEADER^>" + >> TestFinalFix.java
echo                               "^<ns0:VER^>01.01^</ns0:VER^>" + >> TestFinalFix.java
echo                               "^<ns0:FROM^>BU01600010^</ns0:FROM^>" + >> TestFinalFix.java
echo                               "^<ns0:TERMINO^>" + testTermino + "^</ns0:TERMINO^>" + >> TestFinalFix.java
echo                               "^<ns0:TO^>BU00100001^</ns0:TO^>" + >> TestFinalFix.java
echo                               "^</ns0:HEADER^>" + >> TestFinalFix.java
echo                               "^<ns0:AP^>" + >> TestFinalFix.java
echo                               (cleanedXml != null ? cleanedXml : "^<ManageTerminalResponse^>^<ManageTerminalResult^>^<Checksum^>test^</Checksum^>^</ManageTerminalResult^>^</ManageTerminalResponse^>") + >> TestFinalFix.java
echo                               "^</ns0:AP^>" + >> TestFinalFix.java
echo                               "^</ns0:OLTP^>"; >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             testAppendReplyToSOAPFile(listener, replyXml, testTermino); >> TestFinalFix.java
echo             System.out.println(); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             // 測試案例 4：驗證檔案內容 >> TestFinalFix.java
echo             System.out.println("=== 測試案例 4：驗證檔案內容 ==="); >> TestFinalFix.java
echo             verifySOAPFileContent(mockSOAPFileName); >> TestFinalFix.java
echo             System.out.println(); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             System.out.println("=== 測試完成 ==="); >> TestFinalFix.java
echo             System.out.println("✅ 最終版命名空間移除和檔案寫入修復測試完成"); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo         } catch (Exception e) { >> TestFinalFix.java
echo             System.out.println("❌ 測試失敗: " + e.getMessage()); >> TestFinalFix.java
echo             e.printStackTrace(); >> TestFinalFix.java
echo         } >> TestFinalFix.java
echo     } >> TestFinalFix.java
echo. >> TestFinalFix.java
echo     private static void setupTestConfiguration(QueueListener listener) { >> TestFinalFix.java
echo         try { >> TestFinalFix.java
echo             // 設定 LOG_FILEPATH >> TestFinalFix.java
echo             Field logFilePathField = QueueListener.class.getDeclaredField("LOG_FILEPATH"); >> TestFinalFix.java
echo             logFilePathField.setAccessible(true); >> TestFinalFix.java
echo             logFilePathField.set(null, "test_output" + File.separator + "o2odata" + File.separator + "OLTP" + File.separator + "logs" + File.separator + "XML" + File.separator); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             System.out.println("✅ 測試配置設定完成"); >> TestFinalFix.java
echo             System.out.println("LOG_FILEPATH: test_output" + File.separator + "o2odata" + File.separator + "OLTP" + File.separator + "logs" + File.separator + "XML" + File.separator); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo         } catch (Exception e) { >> TestFinalFix.java
echo             System.out.println("❌ 設定測試配置失敗: " + e.getMessage()); >> TestFinalFix.java
echo         } >> TestFinalFix.java
echo     } >> TestFinalFix.java
echo. >> TestFinalFix.java
echo     private static String testRemoveNamespaces(QueueListener listener, String xmlContent, String testName) { >> TestFinalFix.java
echo         try { >> TestFinalFix.java
echo             System.out.println("測試類型: " + testName); >> TestFinalFix.java
echo             System.out.println("原始 XML: " + xmlContent); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             Method method = QueueListener.class.getDeclaredMethod("removeNamespaces", String.class); >> TestFinalFix.java
echo             method.setAccessible(true); >> TestFinalFix.java
echo             String result = (String) method.invoke(listener, xmlContent); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             System.out.println("清理後 XML: " + result); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             // 檢查結果 >> TestFinalFix.java
echo             boolean hasDefaultNamespace = result.contains("xmlns=\""); >> TestFinalFix.java
echo             boolean hasPrefixNamespace = result.contains("xmlns:"); >> TestFinalFix.java
echo             boolean hasNamespacePrefix = result.contains("a:") ^|^| result.contains("ns0:") ^|^| result.contains("s:"); >> TestFinalFix.java
echo             boolean hasValidCloseTags = result.contains("^</Checksum^>") ^&^& result.contains("^</Message^>") ^&^& result.contains("^</ResponseCode^>"); >> TestFinalFix.java
echo             boolean hasValidStructure = !result.contains("^<Checksum^>a87519574d731f03955b107379c9c5be^<Checksum^>"); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             System.out.println("包含預設命名空間: " + hasDefaultNamespace); >> TestFinalFix.java
echo             System.out.println("包含前綴命名空間: " + hasPrefixNamespace); >> TestFinalFix.java
echo             System.out.println("包含命名空間前綴: " + hasNamespacePrefix); >> TestFinalFix.java
echo             System.out.println("有效的結束標籤: " + hasValidCloseTags); >> TestFinalFix.java
echo             System.out.println("結構完整性: " + hasValidStructure); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             boolean isSuccess = !hasDefaultNamespace ^&^& !hasPrefixNamespace ^&^& !hasNamespacePrefix ^&^& hasValidCloseTags ^&^& hasValidStructure; >> TestFinalFix.java
echo             System.out.println("測試結果: " + (isSuccess ? "✅ 成功" : "❌ 失敗")); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             return result; >> TestFinalFix.java
echo. >> TestFinalFix.java
echo         } catch (Exception e) { >> TestFinalFix.java
echo             System.out.println("❌ " + testName + " 測試失敗: " + e.getMessage()); >> TestFinalFix.java
echo             e.printStackTrace(); >> TestFinalFix.java
echo             return null; >> TestFinalFix.java
echo         } >> TestFinalFix.java
echo     } >> TestFinalFix.java
echo. >> TestFinalFix.java
echo     private static String createMockSOAPFile(String termino) { >> TestFinalFix.java
echo         try { >> TestFinalFix.java
echo             String fileName = "test_output" + File.separator + "o2odata" + File.separator + "OLTP" + File.separator + "logs" + File.separator + "XML" + File.separator + >> TestFinalFix.java
echo                              "20250729152336123_" + termino + "_SOAP.txt"; >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             File file = new File(fileName); >> TestFinalFix.java
echo             file.getParentFile().mkdirs(); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             FileWriter writer = new FileWriter(file); >> TestFinalFix.java
echo             writer.write("POS IN XML (SOAP - No Encryption):\n"); >> TestFinalFix.java
echo             writer.write("^<?xml version=\"1.0\" encoding=\"utf-8\"?^>\n"); >> TestFinalFix.java
echo             writer.write("^<ns0:OLTP xmlns:ns0=\"http://7-11.com.tw/online\"^>\n"); >> TestFinalFix.java
echo             writer.write("^<ns0:HEADER^>\n"); >> TestFinalFix.java
echo             writer.write("^<ns0:TERMINO^>" + termino + "^</ns0:TERMINO^>\n"); >> TestFinalFix.java
echo             writer.write("^</ns0:HEADER^>\n"); >> TestFinalFix.java
echo             writer.write("^<ns0:AP^>Original Request Content^</ns0:AP^>\n"); >> TestFinalFix.java
echo             writer.write("^</ns0:OLTP^>\n"); >> TestFinalFix.java
echo             writer.close(); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             System.out.println("✅ 模擬 SOAP 檔案創建成功: " + fileName); >> TestFinalFix.java
echo             return fileName; >> TestFinalFix.java
echo. >> TestFinalFix.java
echo         } catch (IOException e) { >> TestFinalFix.java
echo             System.out.println("❌ 創建模擬 SOAP 檔案失敗: " + e.getMessage()); >> TestFinalFix.java
echo             return null; >> TestFinalFix.java
echo         } >> TestFinalFix.java
echo     } >> TestFinalFix.java
echo. >> TestFinalFix.java
echo     private static void testAppendReplyToSOAPFile(QueueListener listener, String replyXml, String termino) { >> TestFinalFix.java
echo         try { >> TestFinalFix.java
echo             System.out.println("測試 append 回覆到 SOAP 檔案..."); >> TestFinalFix.java
echo             System.out.println("回覆 XML 長度: " + replyXml.length()); >> TestFinalFix.java
echo             System.out.println("TERMINO: " + termino); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             Method method = QueueListener.class.getDeclaredMethod("appendReplyToSOAPFile", String.class, String.class); >> TestFinalFix.java
echo             method.setAccessible(true); >> TestFinalFix.java
echo             method.invoke(listener, replyXml, termino); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             System.out.println("✅ append 回覆到 SOAP 檔案測試完成"); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo         } catch (Exception e) { >> TestFinalFix.java
echo             System.out.println("❌ append 回覆到 SOAP 檔案測試失敗: " + e.getMessage()); >> TestFinalFix.java
echo             e.printStackTrace(); >> TestFinalFix.java
echo         } >> TestFinalFix.java
echo     } >> TestFinalFix.java
echo. >> TestFinalFix.java
echo     private static void verifySOAPFileContent(String fileName) { >> TestFinalFix.java
echo         try { >> TestFinalFix.java
echo             if (fileName == null) { >> TestFinalFix.java
echo                 System.out.println("❌ 檔案名稱為 null，無法驗證"); >> TestFinalFix.java
echo                 return; >> TestFinalFix.java
echo             } >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             File file = new File(fileName); >> TestFinalFix.java
echo             if (!file.exists()) { >> TestFinalFix.java
echo                 System.out.println("❌ 檔案不存在: " + fileName); >> TestFinalFix.java
echo                 return; >> TestFinalFix.java
echo             } >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             System.out.println("✅ 檔案存在: " + fileName); >> TestFinalFix.java
echo             System.out.println("檔案大小: " + file.length() + " bytes"); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             // 簡單檢查檔案內容 >> TestFinalFix.java
echo             java.util.Scanner scanner = new java.util.Scanner(file); >> TestFinalFix.java
echo             boolean hasPosIn = false; >> TestFinalFix.java
echo             boolean hasReplyPos = false; >> TestFinalFix.java
echo             int lineCount = 0; >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             while (scanner.hasNextLine()) { >> TestFinalFix.java
echo                 String line = scanner.nextLine(); >> TestFinalFix.java
echo                 lineCount++; >> TestFinalFix.java
echo                 if (line.contains("POS IN XML (SOAP - No Encryption)")) { >> TestFinalFix.java
echo                     hasPosIn = true; >> TestFinalFix.java
echo                 } >> TestFinalFix.java
echo                 if (line.contains("REPLY POS XML (SOAP - No Encryption)")) { >> TestFinalFix.java
echo                     hasReplyPos = true; >> TestFinalFix.java
echo                 } >> TestFinalFix.java
echo             } >> TestFinalFix.java
echo             scanner.close(); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             System.out.println("檔案行數: " + lineCount); >> TestFinalFix.java
echo             System.out.println("包含 POS IN XML: " + hasPosIn); >> TestFinalFix.java
echo             System.out.println("包含 REPLY POS XML: " + hasReplyPos); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo             boolean isValid = hasPosIn ^&^& hasReplyPos ^&^& lineCount ^> 10; >> TestFinalFix.java
echo             System.out.println("檔案內容驗證: " + (isValid ? "✅ 成功" : "❌ 失敗")); >> TestFinalFix.java
echo. >> TestFinalFix.java
echo         } catch (Exception e) { >> TestFinalFix.java
echo             System.out.println("❌ 驗證檔案內容失敗: " + e.getMessage()); >> TestFinalFix.java
echo         } >> TestFinalFix.java
echo     } >> TestFinalFix.java
echo } >> TestFinalFix.java

echo 編譯測試程式...
javac -cp "%CLASSPATH%" TestFinalFix.java
if %ERRORLEVEL% equ 0 (
    echo.
    echo 4. 執行最終版修復測試...
    echo.
    java -cp "%CLASSPATH%;." TestFinalFix
) else (
    echo ⚠️ 測試程式編譯失敗，跳過測試
)

echo.
echo 5. 檢查測試輸出檔案...
if exist "test_output\o2odata\OLTP\logs\XML" (
    echo.
    echo 測試輸出目錄內容：
    dir /b "test_output\o2odata\OLTP\logs\XML\*SOAP*.txt" 2>nul
    if %ERRORLEVEL% equ 0 (
        echo ✅ 找到 SOAP 檔案
        for %%f in ("test_output\o2odata\OLTP\logs\XML\*SOAP*.txt") do (
            echo 檔案: %%f
            echo 大小: %%~zf bytes
        )
    ) else (
        echo ❌ 未找到 SOAP 檔案
    )
) else (
    echo ❌ 測試輸出目錄不存在
)

echo.
echo 6. 修復前後對比分析...
echo.
echo 修復前的問題：
echo   ❌ 命名空間移除會損壞 XML 結構（移除正斜線）
echo   ❌ 檔案寫入創建新的回覆檔案，而不是 append 到現有檔案
echo   ❌ 檔案路徑和格式與 OLTP.java 不一致
echo   ❌ 缺乏與現有系統的整合
echo.
echo 修復後的改進：
echo   ✅ 命名空間移除保留 XML 結構完整性
echo   ✅ 使用與 OLTP.java 一致的檔案寫入邏輯
echo   ✅ append 回覆內容到現有 SOAP 檔案
echo   ✅ 正確的檔案路徑：o2odata\OLTP\logs\XML\
echo   ✅ 正確的檔案格式：timestamp_TERMINO_SOAP.txt
echo   ✅ 完整的錯誤處理和調試資訊
echo.

echo 7. 預期效果...
echo.
echo 修復前的錯誤輸出：
echo   ^<ManageTerminalResponse xmlns="..."^>^<a:Checksum^>...^<Checksum^>...
echo   創建新檔案：timestamp_TERMINO_REPLY_SOAP.txt
echo.
echo 修復後的正確輸出：
echo   ^<ManageTerminalResponse^>^<ManageTerminalResult^>^<Checksum^>...^</Checksum^>^<Message^>...^</Message^>...
echo   append 到現有檔案：timestamp_TERMINO_SOAP.txt
echo.
echo 測試結果應該顯示：
echo   ✅ 包含預設命名空間: false
echo   ✅ 包含前綴命名空間: false
echo   ✅ 包含命名空間前綴: false
echo   ✅ 有效的結束標籤: true
echo   ✅ 結構完整性: true
echo   ✅ 測試結果: ✅ 成功
echo   ✅ 檔案內容驗證: ✅ 成功
echo   ✅ 包含 POS IN XML: true
echo   ✅ 包含 REPLY POS XML: true
echo.

echo ===================================================
echo.
echo ✅ 最終版命名空間移除和檔案寫入修復完成
echo.
echo 主要修復項目：
echo   1. ✅ 修復命名空間移除，保留 XML 結構完整性
echo   2. ✅ 使用更安全的正則表達式，避免損壞結束標籤
echo   3. ✅ 實作與 OLTP.java 一致的檔案寫入邏輯
echo   4. ✅ append 回覆內容到現有 SOAP 檔案
echo   5. ✅ 使用正確的檔案路徑和格式
echo   6. ✅ 添加檔案查找和驗證功能
echo   7. ✅ 完整的錯誤處理和調試資訊
echo.
echo 下一步：
echo 1. 重啟應用程式
echo 2. 重新測試 SOAP 回應處理
echo 3. 檢查 AP 層內容是否完全乾淨且結構完整
echo 4. 驗證回覆內容是否正確 append 到現有 SOAP 檔案
echo 5. 確認檔案路徑和格式與 OLTP.java 一致
echo.
goto :end

:error
echo.
echo ❌ 最終版修復測試失敗
echo 請檢查編譯錯誤並修正問題
echo.
exit /b 1

:end
echo 如需協助，請檢查 Console 輸出和 test_output\o2odata\OLTP\logs\XML 目錄中的檔案
if exist "TestFinalFix.java" del "TestFinalFix.java"
if exist "TestFinalFix.class" del "TestFinalFix.class"
pause
