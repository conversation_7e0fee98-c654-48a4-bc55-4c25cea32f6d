@echo off
echo ===== SOAP XML 格式修復測試 =====
echo.
echo 測試目標：修正 SOAP 電文格式以匹配服務端期望
echo 修復內容：
echo   1. 修正命名空間 URI（m0 指向正確的 datacontract 命名空間）
echo   2. 修正元素前綴（manageTerminalRequest 使用 m: 前綴）
echo   3. 移除空的 SOAP Header
echo   4. 確保所有元素都有正確的前綴
echo ==========================================
echo.

REM 設定 CLASSPATH
set CLASSPATH=libs\*;bin

echo 1. 檢查必要目錄...
if not exist "bin" mkdir bin
if not exist "bin\com\pic\o2o\common" mkdir bin\com\pic\o2o\common

echo.
echo 2. 編譯修復後的程式碼...

echo 編譯 SOAPClient.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\SOAPClient.java
if %ERRORLEVEL% neq 0 (
    echo ❌ SOAPClient.java 編譯失敗
    echo 可能是格式修復代碼有問題
    goto :error
) else (
    echo ✅ SOAPClient.java 編譯成功
)

echo.
echo 3. 創建 SOAP 格式測試程式...

REM 創建測試程式
echo import com.pic.o2o.common.SOAPClient; > TestSOAPFormat.java
echo import java.lang.reflect.Method; >> TestSOAPFormat.java
echo. >> TestSOAPFormat.java
echo public class TestSOAPFormat { >> TestSOAPFormat.java
echo     public static void main(String[] args) { >> TestSOAPFormat.java
echo         try { >> TestSOAPFormat.java
echo             System.out.println("=== SOAP XML 格式修復測試 ==="); >> TestSOAPFormat.java
echo             System.out.println(); >> TestSOAPFormat.java
echo. >> TestSOAPFormat.java
echo             // 測試 XML 內容（模擬實際的業務請求） >> TestSOAPFormat.java
echo             String testXML = "^<ManageTerminal^>" + >> TestSOAPFormat.java
echo                              "^<manageTerminalRequest^>" + >> TestSOAPFormat.java
echo                              "^<Channel^>Test^</Channel^>" + >> TestSOAPFormat.java
echo                              "^<Checksum^>B43DB40CD926E971464816D781CFF69B^</Checksum^>" + >> TestSOAPFormat.java
echo                              "^<ManageTerminalDateTime^>20151015105959^</ManageTerminalDateTime^>" + >> TestSOAPFormat.java
echo                              "^<ManageType^>101^</ManageType^>" + >> TestSOAPFormat.java
echo                              "^<MerchantCode^>000000000000038^</MerchantCode^>" + >> TestSOAPFormat.java
echo                              "^<ProgramCode^>00001^</ProgramCode^>" + >> TestSOAPFormat.java
echo                              "^<ShopCode^>0000001028^</ShopCode^>" + >> TestSOAPFormat.java
echo                              "^<TerminalCode^>^</TerminalCode^>" + >> TestSOAPFormat.java
echo                              "^<TerminalSSN^>20151015105959000001^</TerminalSSN^>" + >> TestSOAPFormat.java
echo                              "^</manageTerminalRequest^>" + >> TestSOAPFormat.java
echo                              "^</ManageTerminal^>"; >> TestSOAPFormat.java
echo. >> TestSOAPFormat.java
echo             System.out.println("原始 XML 內容:"); >> TestSOAPFormat.java
echo             System.out.println(testXML); >> TestSOAPFormat.java
echo             System.out.println(); >> TestSOAPFormat.java
echo. >> TestSOAPFormat.java
echo             // 測試 transformToSOAPFormat 方法 >> TestSOAPFormat.java
echo             SOAPClient client = new SOAPClient("https://test.example.com", 30); >> TestSOAPFormat.java
echo             Method transformMethod = SOAPClient.class.getDeclaredMethod("transformToSOAPFormat", String.class); >> TestSOAPFormat.java
echo             transformMethod.setAccessible(true); >> TestSOAPFormat.java
echo. >> TestSOAPFormat.java
echo             String transformed = (String) transformMethod.invoke(client, testXML); >> TestSOAPFormat.java
echo             System.out.println("轉換後的 SOAP 格式:"); >> TestSOAPFormat.java
echo             System.out.println(transformed); >> TestSOAPFormat.java
echo             System.out.println(); >> TestSOAPFormat.java
echo. >> TestSOAPFormat.java
echo             // 檢查格式修復項目 >> TestSOAPFormat.java
echo             System.out.println("=== 格式檢查 ==="); >> TestSOAPFormat.java
echo. >> TestSOAPFormat.java
echo             // 1. 檢查 manageTerminalRequest 前綴 >> TestSOAPFormat.java
echo             if (transformed.contains("^<m:manageTerminalRequest^>")) { >> TestSOAPFormat.java
echo                 System.out.println("✅ manageTerminalRequest 使用正確的 m: 前綴"); >> TestSOAPFormat.java
echo             } else { >> TestSOAPFormat.java
echo                 System.out.println("❌ manageTerminalRequest 前綴錯誤"); >> TestSOAPFormat.java
echo             } >> TestSOAPFormat.java
echo. >> TestSOAPFormat.java
echo             // 2. 檢查其他元素的 m0: 前綴 >> TestSOAPFormat.java
echo             if (transformed.contains("^<m0:Channel^>") ^&^& transformed.contains("^<m0:TerminalCode^>")) { >> TestSOAPFormat.java
echo                 System.out.println("✅ 其他元素使用正確的 m0: 前綴"); >> TestSOAPFormat.java
echo             } else { >> TestSOAPFormat.java
echo                 System.out.println("❌ 其他元素前綴錯誤"); >> TestSOAPFormat.java
echo             } >> TestSOAPFormat.java
echo. >> TestSOAPFormat.java
echo             // 3. 檢查 TerminalCode 格式 >> TestSOAPFormat.java
echo             if (transformed.contains("^<m0:TerminalCode^>^</m0:TerminalCode^>")) { >> TestSOAPFormat.java
echo                 System.out.println("✅ TerminalCode 使用正確的完整標籤格式"); >> TestSOAPFormat.java
echo             } else if (transformed.contains("^<TerminalCode/^>")) { >> TestSOAPFormat.java
echo                 System.out.println("❌ TerminalCode 仍使用自閉合標籤格式"); >> TestSOAPFormat.java
echo             } else { >> TestSOAPFormat.java
echo                 System.out.println("⚠️ TerminalCode 格式需要檢查"); >> TestSOAPFormat.java
echo             } >> TestSOAPFormat.java
echo. >> TestSOAPFormat.java
echo             System.out.println(); >> TestSOAPFormat.java
echo             System.out.println("=== 測試完成 ==="); >> TestSOAPFormat.java
echo. >> TestSOAPFormat.java
echo         } catch (Exception e) { >> TestSOAPFormat.java
echo             System.out.println("測試失敗: " + e.getMessage()); >> TestSOAPFormat.java
echo             e.printStackTrace(); >> TestSOAPFormat.java
echo         } >> TestSOAPFormat.java
echo     } >> TestSOAPFormat.java
echo } >> TestSOAPFormat.java

echo 編譯測試程式...
javac -cp "%CLASSPATH%" TestSOAPFormat.java
if %ERRORLEVEL% equ 0 (
    echo.
    echo 4. 執行 SOAP 格式修復測試...
    echo.
    java -cp "%CLASSPATH%;." TestSOAPFormat
) else (
    echo ⚠️ 測試程式編譯失敗，跳過測試
)

echo.
echo 5. 格式對比分析...
echo.
echo 修復前的問題格式：
echo   ❌ m0 命名空間: http://ticketxpress.com.tw/
echo   ❌ manageTerminalRequest: ^<m0:manageTerminalRequest^>
echo   ❌ TerminalCode: ^<TerminalCode/^>
echo   ❌ 包含空的 SOAP Header
echo.
echo 修復後的正確格式：
echo   ✅ m0 命名空間: http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common
echo   ✅ manageTerminalRequest: ^<m:manageTerminalRequest^>
echo   ✅ TerminalCode: ^<m0:TerminalCode^>^</m0:TerminalCode^>
echo   ✅ 不包含空的 SOAP Header
echo.

echo 6. 預期的完整 SOAP 電文格式：
echo.
echo ^<SOAP-ENV:Envelope 
echo   xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" 
echo   xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" 
echo   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
echo   xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
echo   xmlns:m0="http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common" 
echo   xmlns:m="http://ticketxpress.com.tw/"^>
echo   ^<SOAP-ENV:Body^>
echo     ^<m:ManageTerminal^>
echo       ^<m:manageTerminalRequest^>
echo         ^<m0:Channel^>Test^</m0:Channel^>
echo         ^<m0:Checksum^>B43DB40CD926E971464816D781CFF69B^</m0:Checksum^>
echo         ^<m0:ManageTerminalDateTime^>20151015105959^</m0:ManageTerminalDateTime^>
echo         ^<m0:ManageType^>101^</m0:ManageType^>
echo         ^<m0:MerchantCode^>000000000000038^</m0:MerchantCode^>
echo         ^<m0:ProgramCode^>00001^</m0:ProgramCode^>
echo         ^<m0:ShopCode^>0000001028^</m0:ShopCode^>
echo         ^<m0:TerminalCode^>^</m0:TerminalCode^>
echo         ^<m0:TerminalSSN^>20151015105959000001^</m0:TerminalSSN^>
echo       ^</m:manageTerminalRequest^>
echo     ^</m:ManageTerminal^>
echo   ^</SOAP-ENV:Body^>
echo ^</SOAP-ENV:Envelope^>
echo.

echo ==========================================
echo.
echo ✅ SOAP XML 格式修復完成
echo.
echo 主要修復項目：
echo   1. ✅ 修正 m0 命名空間 URI
echo   2. ✅ 修正 manageTerminalRequest 前綴為 m:
echo   3. ✅ 確保所有其他元素使用 m0: 前綴
echo   4. ✅ 移除空的 SOAP Header
echo   5. ✅ 改進元素前綴分配邏輯
echo.
echo 下一步：
echo 1. 重啟應用程式
echo 2. 重新測試 SOAP 連線
echo 3. 檢查服務端是否能正確處理請求
echo 4. 驗證是否收到正確的業務回應
echo.
goto :end

:error
echo.
echo ❌ 格式修復測試失敗
echo 請檢查編譯錯誤並修正問題
echo.
exit /b 1

:end
echo 如需協助，請檢查 Console 輸出中的詳細格式資訊
if exist "TestSOAPFormat.java" del "TestSOAPFormat.java"
if exist "TestSOAPFormat.class" del "TestSOAPFormat.class"
pause
