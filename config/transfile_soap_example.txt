# CosmedApi OLTP 交易檔案配置範例
# 格式: tocode,subProcess,target,timeout,maintainStat
# 
# tocode: 交易代碼
# subProcess: 處理程序類型 (HTTP_SubProcess, HTTP_SubProcess_AP, SOAP_SubProcess, SOAP_SubProcess_AP)
# target: 目標服務 URL
# timeout: 逾時時間（秒）
# maintainStat: 維護狀態 (Y/N)

# HTTP 協議範例
H001,HTTP_SubProcess,http://api.example.com/service1,30,N
H002,HTTP_SubProcess_AP,http://api.example.com/service2,45,N

# SOAP 協議範例
S001,SOAP_SubProcess,http://soap.example.com/service1,30,N
S002,SOAP_SubProcess_AP,http://soap.example.com/service2,45,N
S003,SOAP_SubProcess,https://secure.soap.example.com/service3,60,N

# 特殊 SOAP 服務範例
SOAP_PAYMENT,SOAP_SubProcess,http://payment.soap.service.com/PaymentService,120,N
SOAP_INVENTORY,SOAP_SubProcess_AP,http://inventory.soap.service.com/InventoryService,90,N
SOAP_CUSTOMER,SOAP_SubProcess,http://customer.soap.service.com/CustomerService,60,N

# 維護中的服務範例
MAINT_001,SOAP_SubProcess,http://maintenance.soap.service.com/TestService,30,Y

# 說明:
# 1. SOAP_SubProcess: 處理完整的 XML 電文，適用於需要完整 XML 結構的 SOAP 服務
# 2. SOAP_SubProcess_AP: 只處理 AP 層內容，適用於只需要業務資料的 SOAP 服務
# 3. timeout 建議設定:
#    - 一般查詢服務: 30-60 秒
#    - 複雜處理服務: 60-120 秒
#    - 批次處理服務: 120-300 秒
# 4. maintainStat 設為 Y 時，該服務將進入維護模式，不會處理請求
