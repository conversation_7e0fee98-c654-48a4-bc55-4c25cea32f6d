# QueueListener 配置檔案範例
# 此檔案應該放置在 C:/o2odata/QWARE/config.properties (Windows) 或 /o2odata/QWARE/config.properties (Linux)

# EMS 日誌佇列設定
EMS.LOG.QUEUE=queue.log

# 日誌檔案路徑
LOG.FILEPATH=C:/o2odata/logs/

# XML Schema 檔案路徑
SCHEMA.FILEPATH=C:/o2odata/schema/business.xsd

# SOAP 驗證設定
# 設定為 true 可跳過 SOAP 回應的 Schema 驗證（建議在 SOAP 服務正常運作時設為 true）
# 設定為 false 會對 SOAP 回應進行基本的格式驗證
SKIP.SOAP.VALIDATION=true

# 其他可能的配置項目...
# DATABASE.URL=***********************************
# DATABASE.USER=username
# DATABASE.PASSWORD=password
