@echo off
echo ===== SOAP 2001 錯誤診斷工具 =====
echo.
echo 目標：診斷和解決 "2001 系統連線失敗" 錯誤
echo 服務：https://stage-posapi2.tixpress.tw/POSProxyService.svc
echo =====================================
echo.

REM 設定 CLASSPATH
set CLASSPATH=libs\*;bin

echo 1. 檢查必要目錄...
if not exist "bin" mkdir bin
if not exist "bin\com\pic\o2o\common" mkdir bin\com\pic\o2o\common

echo.
echo 2. 編譯診斷工具...

echo 編譯 HTTPSSOAPDiagnostic.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\HTTPSSOAPDiagnostic.java
if %ERRORLEVEL% neq 0 (
    echo ❌ HTTPSSOAPDiagnostic.java 編譯失敗
    goto :error
) else (
    echo ✅ HTTPSSOAPDiagnostic.java 編譯成功
)

echo 編譯 SSLCertificateHelper.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\SSLCertificateHelper.java
if %ERRORLEVEL% neq 0 (
    echo ❌ SSLCertificateHelper.java 編譯失敗
    goto :error
) else (
    echo ✅ SSLCertificateHelper.java 編譯成功
)

echo 編譯 SimpleSOAPClient.java...
javac -cp "%CLASSPATH%" -d bin src\com\pic\o2o\common\SimpleSOAPClient.java
if %ERRORLEVEL% neq 0 (
    echo ❌ SimpleSOAPClient.java 編譯失敗
    goto :error
) else (
    echo ✅ SimpleSOAPClient.java 編譯成功
)

echo 編譯主診斷程式...
javac -cp "%CLASSPATH%" -d . diagnose_soap_connection.java
if %ERRORLEVEL% neq 0 (
    echo ❌ 主診斷程式編譯失敗
    goto :error
) else (
    echo ✅ 主診斷程式編譯成功
)

echo.
echo 3. 執行診斷...
echo =====================================
echo.

REM 執行主診斷程式
java -cp "%CLASSPATH%;." diagnose_soap_connection

echo.
echo =====================================
echo.

echo 4. 執行 SSL 憑證專項診斷...
echo.
java -cp "%CLASSPATH%" com.pic.o2o.common.SSLCertificateHelper

echo.
echo =====================================
echo.

echo 5. 網路連線基本測試...
echo.

echo 測試 DNS 解析...
nslookup stage-posapi2.tixpress.tw
echo.

echo 測試 TCP 連線（443 埠）...
telnet stage-posapi2.tixpress.tw 443
echo.

echo 測試 ping 連線...
ping -n 4 stage-posapi2.tixpress.tw
echo.

echo =====================================
echo.

echo 6. 診斷結果總結
echo.
echo 常見的 2001 錯誤原因和解決方案：
echo.
echo 🔍 原因 1：SSL 憑證問題
echo   症狀：SSL 握手失敗、憑證驗證錯誤
echo   解決：
echo     - 更新 Java 版本
echo     - 添加 CA 憑證到信任庫
echo     - 檢查系統時間
echo     - 聯絡服務提供者
echo.
echo 🔍 原因 2：網路連線問題
echo   症狀：DNS 解析失敗、TCP 連線被拒絕
echo   解決：
echo     - 檢查網路連線
echo     - 檢查防火牆設定
echo     - 檢查代理伺服器設定
echo     - 聯絡網路管理員
echo.
echo 🔍 原因 3：服務端點問題
echo   症狀：HTTP 錯誤回應、服務不可用
echo   解決：
echo     - 確認服務 URL 正確
echo     - 檢查服務狀態
echo     - 聯絡服務提供者
echo     - 檢查 transfile.txt 設定
echo.
echo 🔍 原因 4：SOAP 請求格式問題
echo   症狀：HTTP 400/500 錯誤
echo   解決：
echo     - 檢查 SOAP 請求格式
echo     - 檢查 SOAPAction 標頭
echo     - 檢查 Content-Type 設定
echo     - 檢查業務邏輯內容
echo.

echo =====================================
echo.

echo 7. 下一步建議
echo.
echo 根據診斷結果，請執行以下步驟：
echo.
echo 如果是 SSL 憑證問題：
echo   1. 執行：keytool -list -keystore "%JAVA_HOME%\lib\security\cacerts"
echo   2. 檢查是否包含相關的 CA 憑證
echo   3. 如需要，添加憑證：keytool -import -alias tixpress -file cert.crt -keystore cacerts
echo.
echo 如果是網路問題：
echo   1. 檢查公司防火牆設定
echo   2. 檢查代理伺服器設定
echo   3. 聯絡網路管理員開放 443 埠
echo.
echo 如果是服務問題：
echo   1. 聯絡 tixpress 服務提供者
echo   2. 確認服務端點和認證資訊
echo   3. 檢查服務維護狀態
echo.

goto :end

:error
echo.
echo ❌ 診斷工具編譯失敗
echo 請檢查 Java 環境和相依函式庫
echo.
exit /b 1

:end
echo.
echo ===== 診斷完成 =====
echo.
echo 如需進一步協助，請提供診斷結果給技術支援團隊。
pause
