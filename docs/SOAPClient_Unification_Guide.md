# SOAPClient 統一格式指南

## 概述

本文件說明 `SOAPClient.java` 和 `SimpleSOAPClient.java` 的統一修改，確保兩個 SOAP 客戶端產生相同格式的標準 SOAP Envelope 請求。

## 🔄 修改摘要

### 主要改進

1. **統一 SOAP Envelope 格式**：兩個客戶端現在都產生相同的標準 SOAP 格式
2. **一致的 XML 轉換邏輯**：使用相同的轉換演算法和命名空間
3. **向後相容性**：保持現有功能不受影響
4. **自動備用機制**：如果標準 SOAP API 失敗，自動切換到 SimpleSOAPClient

## 📋 修改詳情

### 1. SOAPClient.java 修改

#### 更新的 createSOAPRequest 方法

**Before（舊版本）：**
```java
// 簡單的命名空間設定
QName bodyName = new QName("http://cosmed.com.tw/oltp", "request", "oltp");
SOAPElement bodyElement = soapBody.addBodyElement(bodyName);
```

**After（新版本）：**
```java
// 標準 SOAP Envelope 命名空間設定
envelope.addNamespaceDeclaration("SOAP-ENV", "http://schemas.xmlsoap.org/soap/envelope/");
envelope.addNamespaceDeclaration("SOAP-ENC", "http://schemas.xmlsoap.org/soap/encoding/");
envelope.addNamespaceDeclaration("xsi", "http://www.w3.org/2001/XMLSchema-instance");
envelope.addNamespaceDeclaration("xsd", "http://www.w3.org/2001/XMLSchema");
envelope.addNamespaceDeclaration("m0", "http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common");

// XML 轉換處理
String transformedContent = transformToSOAPFormat(requestContent);
addTransformedContentToBody(soapBody, transformedContent);
```

#### 新增的轉換方法

1. **`transformToSOAPFormat(String originalContent)`**
   - 將原始 XML 轉換為標準 SOAP 格式
   - 與 SimpleSOAPClient 使用相同的邏輯

2. **`extractRootElementName(String content)`**
   - 提取 XML 根元素名稱
   - 處理帶屬性的元素標籤

3. **`extractInnerContent(String content, String rootElementName)`**
   - 提取根元素內部的內容
   - 移除外層標籤

4. **`transformInnerElements(String innerContent)`**
   - 為所有內部元素添加 `m0:` 前綴
   - 使用正則表達式進行轉換

5. **`addTransformedContentToBody(SOAPBody soapBody, String transformedContent)`**
   - 將轉換後的內容添加到 SOAP Body
   - 包含備用處理機制

## 🔄 轉換範例

### 輸入 XML
```xml
<ManageTerminal>
    <manageTerminalRequest>
        <Channel>Test</Channel>
        <Checksum>B43DB40CD926E971464816D781CFF69B</Checksum>
        <ManageTerminalDateTime>20151015105959</ManageTerminalDateTime>
        <ManageType>101</ManageType>
        <MerchantCode>000000000000038</MerchantCode>
        <ProgramCode>00001</ProgramCode>
        <ShopCode>0000001028</ShopCode>
        <TerminalCode></TerminalCode>
        <TerminalSSN>20151015105959000001</TerminalSSN>
    </manageTerminalRequest>
</ManageTerminal>
```

### 輸出 SOAP Envelope（兩個客戶端相同）
```xml
<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" 
                   xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                   xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
                   xmlns:m0="http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common">
    <SOAP-ENV:Body>
        <m:ManageTerminal xmlns:m="http://ticketxpress.com.tw/">
            <m:manageTerminalRequest>
                <m0:Channel>Test</m0:Channel>
                <m0:Checksum>B43DB40CD926E971464816D781CFF69B</m0:Checksum>
                <m0:ManageTerminalDateTime>20151015105959</m0:ManageTerminalDateTime>
                <m0:ManageType>101</m0:ManageType>
                <m0:MerchantCode>000000000000038</m0:MerchantCode>
                <m0:ProgramCode>00001</m0:ProgramCode>
                <m0:ShopCode>0000001028</m0:ShopCode>
                <m0:TerminalCode></m0:TerminalCode>
                <m0:TerminalSSN>20151015105959000001</TerminalSSN>
            </m:manageTerminalRequest>
        </m:ManageTerminal>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>
```

## 🔧 命名空間對應

| 前綴 | 命名空間 URI | 用途 |
|------|-------------|------|
| `SOAP-ENV` | `http://schemas.xmlsoap.org/soap/envelope/` | SOAP Envelope |
| `SOAP-ENC` | `http://schemas.xmlsoap.org/soap/encoding/` | SOAP 編碼 |
| `xsi` | `http://www.w3.org/2001/XMLSchema-instance` | XML Schema 實例 |
| `xsd` | `http://www.w3.org/2001/XMLSchema` | XML Schema 定義 |
| `m0` | `http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common` | 內部元素 |
| `m` | `http://ticketxpress.com.tw/` | 根元素包裝 |

## 🧪 測試驗證

### 執行一致性測試
```bash
# 編譯測試
javac -cp "libs/*:bin" test/com/pic/o2o/common/SOAPClientConsistencyTest.java

# 執行測試
java -cp "libs/*:bin:test" org.junit.runner.JUnitCore com.pic.o2o.common.SOAPClientConsistencyTest
```

### 測試案例
1. **轉換邏輯一致性** - 驗證兩個客戶端產生相同的轉換結果
2. **根元素提取一致性** - 驗證元素名稱提取邏輯
3. **內部元素轉換一致性** - 驗證 m0 前綴添加
4. **空內容處理一致性** - 驗證空請求的處理
5. **純文字內容一致性** - 驗證非 XML 內容的包裝

## 🔄 自動備用機制

### SOAPService 中的智能切換
```java
try {
    // 首先嘗試使用標準 SOAP 客戶端
    SOAPClient client = getSOAPClient(serviceUrl, timeout);
    client.setRequestContent(requestContent);
    response = client.sendRequest();
    
} catch (NoClassDefFoundError | ClassNotFoundException e) {
    // 如果標準 SOAP 客戶端因為依賴問題失敗，使用簡化版本
    SimpleSOAPClient simpleClient = new SimpleSOAPClient(serviceUrl, timeout);
    simpleClient.setRequestContent(requestContent);
    response = simpleClient.sendRequest();
}
```

## ⚙️ 配置和使用

### 兩個客戶端現在可以互換使用

**使用 SOAPClient（標準 SOAP API）：**
```java
SOAPClient client = new SOAPClient(serviceUrl, timeout);
client.setRequestContent(xmlContent);
String response = client.sendRequest();
```

**使用 SimpleSOAPClient（HTTP POST）：**
```java
SimpleSOAPClient client = new SimpleSOAPClient(serviceUrl, timeout);
client.setRequestContent(xmlContent);
String response = client.sendRequest();
```

**兩者產生的 SOAP 請求格式完全相同！**

## 🚨 注意事項

### 1. 依賴項要求
- **SOAPClient**：需要完整的 SOAP API 依賴項
- **SimpleSOAPClient**：只需要基本的 HTTP 和 XML 處理

### 2. 效能考量
- **SOAPClient**：使用標準 SOAP API，功能更完整
- **SimpleSOAPClient**：使用 HTTP POST，更輕量級

### 3. 錯誤處理
- 兩個客戶端都有相同的錯誤處理邏輯
- 自動備用機制確保服務可用性

## ✅ 驗證檢查清單

部署前請確認：
- [ ] 兩個 SOAP 客戶端產生相同的 SOAP 格式
- [ ] 命名空間設定正確
- [ ] XML 轉換邏輯一致
- [ ] 錯誤處理機制正常
- [ ] 自動備用切換功能正常
- [ ] 單元測試通過
- [ ] 與現有 SOAP 服務相容

## 📋 維護指南

### 保持同步
如果需要修改轉換邏輯：
1. 同時更新兩個客戶端的相關方法
2. 執行一致性測試驗證
3. 更新相關文件

### 新增功能
新增 SOAP 功能時：
1. 優先在 SOAPClient 中實作
2. 在 SimpleSOAPClient 中實作相同功能
3. 確保兩者行為一致

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
