# 最終版命名空間移除和檔案寫入修復指南

## 問題概述

基於之前的修復工作，解決兩個關鍵問題的最終版本：

1. **命名空間移除仍有問題**：需要保留 XML 結構完整性，避免損壞結束標籤
2. **檔案寫入路徑和邏輯修正**：需要與 OLTP.java 保持一致，append 到現有檔案而非創建新檔案

## 🔍 問題分析

### 問題 1：命名空間移除的結構完整性

#### 之前的問題：
- 命名空間移除會意外刪除結束標籤中的正斜線 "/"
- 導致 `</Checksum>` 變成 `<Checksum>`，破壞 XML 結構
- XML 解析失敗，無法正確處理回應內容

#### 根本原因：
```java
// 有問題的正則表達式
cleanContent = cleanContent.replaceAll("</[^>]*?:", "</");
// 當沒有命名空間前綴時，會錯誤匹配並移除內容
```

### 問題 2：檔案寫入邏輯不一致

#### 當前問題：
- 創建新的回覆檔案，而不是 append 到現有檔案
- 檔案路徑和格式與 OLTP.java 不一致
- 缺乏與現有系統的整合

#### OLTP.java 的正確邏輯：
```java
// OLTP.java 中的 writeXmlAsFileWithoutEncryption 方法
// 1. 先寫入 "POS IN XML (SOAP - No Encryption)"
// 2. 然後 append "REPLY POS XML (SOAP - No Encryption)" 到同一個檔案
// 3. 使用路徑：GlobalVariable.XML_LOG_PATH (o2odata\OLTP\logs\XML\)
// 4. 檔案格式：timestamp_TERMINO_SOAP.txt
```

## 🔧 修復方案

### 1. 修復命名空間移除（保留結構完整性）

#### 修復前（有問題的方法）：
```java
// 會損壞 XML 結構的正則表達式
cleanContent = cleanContent.replaceAll("<[^>]*?:", "<");
cleanContent = cleanContent.replaceAll("</[^>]*?:", "</");
```

#### 修復後（安全的方法）：
```java
private String removeNamespaces(String xmlContent) {
  String cleanContent = xmlContent;
  
  // 步驟 1：移除所有命名空間宣告
  // 移除預設命名空間宣告 xmlns="..."
  cleanContent = cleanContent.replaceAll("\\s+xmlns=\"[^\"]*\"", "");
  
  // 移除命名空間前綴宣告 xmlns:prefix="..."
  cleanContent = cleanContent.replaceAll("\\s+xmlns:[^=\\s]+=\"[^\"]*\"", "");

  // 步驟 2：移除標籤中的命名空間前綴（保留結構完整性）
  // 使用更安全的方法，只有當確實存在冒號時才進行替換
  
  // 處理開始標籤：<prefix:tagname> -> <tagname>
  cleanContent = cleanContent.replaceAll("<([a-zA-Z0-9_.-]+):([a-zA-Z0-9_.-]+)([^>]*)>", "<$2$3>");
  
  // 處理結束標籤：</prefix:tagname> -> </tagname>
  cleanContent = cleanContent.replaceAll("</([a-zA-Z0-9_.-]+):([a-zA-Z0-9_.-]+)>", "</$2>");

  // 步驟 3：移除屬性中的命名空間前綴
  // 處理屬性：prefix:attribute="value" -> attribute="value"
  cleanContent = cleanContent.replaceAll("\\s+([a-zA-Z0-9_.-]+):([a-zA-Z0-9_.-]+)=", " $2=");

  // 額外檢查：確保沒有損壞的結束標籤
  if (cleanContent.contains("<Checksum>") && !cleanContent.contains("</Checksum>")) {
    System.out.println("❌ 檢測到損壞的結束標籤，返回原始內容");
    return xmlContent;
  }
  
  return cleanContent;
}
```

### 2. 修復檔案寫入邏輯（與 OLTP.java 一致）

#### 修復前（錯誤的邏輯）：
```java
// 創建新的回覆檔案
private void writeReplyXMLToFile(String replyXML, String termino) {
  String fileName = LOG_FILEPATH + timestamp + "_" + termino + "_REPLY_SOAP.txt";
  // 創建新檔案...
}
```

#### 修復後（正確的邏輯）：
```java
// append 到現有的 SOAP 檔案
private void appendReplyToSOAPFile(String replyXML, String termino) {
  try {
    // 1. 查找現有的 SOAP 檔案
    String existingFileName = findExistingSOAPFile(xmlLogPath, termino);
    
    if (existingFileName == null) {
      System.out.println("❌ 找不到對應的 SOAP 檔案，無法 append 回覆內容");
      return;
    }
    
    // 2. 解析回覆 XML 並提取 AP 層內容（與 OLTP.java 邏輯一致）
    Document replyDoc = DocumentHelper.parseText(replyXML);
    Element replyRoot = replyDoc.getRootElement();
    StringBuffer replyApStrBuffer = new StringBuffer();
    
    // 取得AP節點的XML字串，使用replaceAll()方法刪除AP標籤
    replyApStrBuffer.append(replyRoot.element("AP").asXML().replaceAll("<ns0:AP[^>]*>|</ns0:AP>", ""));
    String replyAPStr = replyApStrBuffer.toString();
    
    // 用原始AP層置換原AP層（不加密）
    Document forPosDoc = DocumentHelper.parseText(replyXML);
    forPosDoc.getRootElement().element("AP").clearContent();
    forPosDoc.getRootElement().element("AP").setText(replyAPStr);
    
    // 3. 設定為 Append 模式，直接將回覆內容 append 到同一個檔案
    OutputStreamWriter osw = new OutputStreamWriter(new FileOutputStream(existingFileName, true), Charset.forName("utf-8"));
    OutputFormat format = OutputFormat.createPrettyPrint();
    XMLWriter xmlwriter = new XMLWriter(osw, format);
    
    xmlwriter.println();
    xmlwriter.write("REPLY POS XML (SOAP - No Encryption):");
    xmlwriter.println();
    xmlwriter.write(forPosDoc);
    
    xmlwriter.close();
    osw.close();
    
  } catch (Exception e) {
    // 錯誤處理...
  }
}

// 查找現有的 SOAP 檔案
private String findExistingSOAPFile(String xmlLogPath, String termino) {
  try {
    File logDir = new File(xmlLogPath);
    File[] files = logDir.listFiles();
    
    // 查找包含指定 TERMINO 且以 _SOAP.txt 結尾的檔案
    for (File file : files) {
      if (file.isFile()) {
        String fileName = file.getName();
        // 檔案名格式：timestamp_TERMINO_SOAP.txt
        if (fileName.contains(termino) && fileName.endsWith("_SOAP.txt")) {
          return file.getAbsolutePath();
        }
      }
    }
    return null;
  } catch (Exception e) {
    return null;
  }
}
```

## 📋 修復檢查清單

### QueueListener.java 修改項目
- [ ] ✅ 修復 `removeNamespaces` 方法，使用安全的正則表達式
- [ ] ✅ 添加結束標籤完整性檢查
- [ ] ✅ 實作 `appendReplyToSOAPFile` 方法
- [ ] ✅ 實作 `findExistingSOAPFile` 方法
- [ ] ✅ 修改 `replyQueueMessage` 方法調用新的檔案寫入邏輯
- [ ] ✅ 確保與 OLTP.java 的檔案處理邏輯一致
- [ ] ✅ 添加詳細的調試資訊和錯誤處理

### 系統整合改進
- [ ] ✅ 使用與 OLTP.java 相同的檔案路徑配置
- [ ] ✅ 使用與 OLTP.java 相同的檔案格式
- [ ] ✅ append 回覆內容到現有檔案，而非創建新檔案
- [ ] ✅ 保持與現有系統的一致性

## 🧪 測試驗證

### 1. 執行自動測試
```bash
test_final_namespace_and_file_fix.bat
```

### 2. 測試案例覆蓋

#### 測試案例 1：命名空間移除（保留結構完整性）
```xml
<!-- 輸入 -->
<ManageTerminalResponse xmlns="http://ticketxpress.com.tw/">
  <ManageTerminalResult xmlns:a="http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common">
    <a:Checksum>a87519574d731f03955b107379c9c5be</a:Checksum>
    <a:Message>Success</a:Message>
    <a:ResponseCode>0000</a:ResponseCode>
  </ManageTerminalResult>
</ManageTerminalResponse>

<!-- 預期輸出（結構完整）-->
<ManageTerminalResponse>
  <ManageTerminalResult>
    <Checksum>a87519574d731f03955b107379c9c5be</Checksum>
    <Message>Success</Message>
    <ResponseCode>0000</ResponseCode>
  </ManageTerminalResult>
</ManageTerminalResponse>
```

#### 測試案例 2：檔案 append 功能
```
現有檔案內容：
POS IN XML (SOAP - No Encryption):
<?xml version="1.0" encoding="utf-8"?>
<ns0:OLTP xmlns:ns0="http://7-11.com.tw/online">
  <ns0:HEADER>
    <ns0:TERMINO>TEST_FINAL_789</ns0:TERMINO>
  </ns0:HEADER>
  <ns0:AP>Original Request Content</ns0:AP>
</ns0:OLTP>

append 後的檔案內容：
POS IN XML (SOAP - No Encryption):
<?xml version="1.0" encoding="utf-8"?>
<ns0:OLTP xmlns:ns0="http://7-11.com.tw/online">
  <ns0:HEADER>
    <ns0:TERMINO>TEST_FINAL_789</ns0:TERMINO>
  </ns0:HEADER>
  <ns0:AP>Original Request Content</ns0:AP>
</ns0:OLTP>

REPLY POS XML (SOAP - No Encryption):
<?xml version="1.0" encoding="utf-8"?>
<ns0:OLTP xmlns:ns0="http://7-11.com.tw/online">
  <ns0:HEADER>
    <ns0:TERMINO>TEST_FINAL_789</ns0:TERMINO>
  </ns0:HEADER>
  <ns0:AP><ManageTerminalResponse>...</ManageTerminalResponse></ns0:AP>
</ns0:OLTP>
```

### 3. 預期的測試結果

#### 命名空間移除測試：
```
=== 測試案例 1：命名空間移除（保留結構完整性）===
步驟 1：移除命名空間宣告
  ✅ 移除預設命名空間宣告
  ✅ 移除命名空間前綴宣告
步驟 2：移除標籤中的命名空間前綴
  ✅ 移除標籤命名空間前綴
步驟 3：移除屬性中的命名空間前綴
  ✅ 移除屬性命名空間前綴

包含預設命名空間: false
包含前綴命名空間: false
包含命名空間前綴: false
有效的結束標籤: true
結構完整性: true
測試結果: ✅ 成功
```

#### 檔案 append 測試：
```
=== 測試案例 3：檔案 append 功能測試 ===
=== 查找現有 SOAP 檔案 ===
✅ 找到匹配的 SOAP 檔案: 20250729152336123_TEST_FINAL_789_SOAP.txt
✅ 回覆電文成功 append 到檔案

=== 測試案例 4：驗證檔案內容 ===
✅ 檔案存在
檔案大小: > 500 bytes
包含 POS IN XML: true
包含 REPLY POS XML: true
檔案內容驗證: ✅ 成功
```

## 🔍 故障排除

### 問題 1：命名空間仍然存在或結構損壞
**解決方案**：
```java
// 檢查正則表達式是否正確匹配
System.out.println("處理前: " + xmlContent);
System.out.println("處理後: " + cleanContent);

// 驗證結構完整性
if (cleanContent.contains("<Checksum>") && !cleanContent.contains("</Checksum>")) {
  System.out.println("❌ 檢測到損壞的結束標籤");
  return xmlContent;
}
```

### 問題 2：找不到現有 SOAP 檔案
**解決方案**：
```java
// 檢查檔案路徑和 TERMINO 匹配
System.out.println("搜尋路徑: " + xmlLogPath);
System.out.println("TERMINO: " + termino);
File[] files = logDir.listFiles();
for (File file : files) {
  System.out.println("檢查檔案: " + file.getName());
}
```

### 問題 3：檔案 append 失敗
**解決方案**：
```java
// 檢查檔案權限和磁碟空間
File existingFile = new File(existingFileName);
System.out.println("檔案可寫: " + existingFile.canWrite());
System.out.println("可用空間: " + existingFile.getParentFile().getFreeSpace());
```

## 📊 效果監控

### 成功指標
- ✅ AP 層內容完全乾淨，無任何命名空間
- ✅ XML 結構完整，所有結束標籤正確
- ✅ 回覆內容正確 append 到現有 SOAP 檔案
- ✅ 檔案路徑和格式與 OLTP.java 一致

### 監控方法
```java
// 在日誌中記錄處理結果
log.append("Namespace cleanup: ")
   .append(structureValid ? "SUCCESS" : "FAILED")
   .append(", File append: ")
   .append(fileAppended ? "SUCCESS" : "FAILED")
   .append(", File path: ")
   .append(existingFileName)
   .append("\r\n");
```

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
