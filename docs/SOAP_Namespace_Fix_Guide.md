# SOAP XML 命名空間修復指南

## 問題概述

**錯誤現象**：
- SOAPAction 已正確設定，但 SOAP 請求仍然失敗
- XML 命名空間錯誤：`元素 "m0:Channel" 的前置碼 "m0" 未連結`
- 服務端回應：`The server was unable to process the request due to an internal error`

**根本原因**：
- XML 元素使用了 `m0:` 前綴，但在 SOAP Envelope 中沒有定義對應的命名空間
- XML 轉換邏輯存在缺陷，導致命名空間前綴與定義不匹配

## 🔍 錯誤分析

### 錯誤日誌分析
```
2025/07/28 15:59:28.228	[localhost]	[SOAPService]	Using custom SOAPAction: http://ticketxpress.com.tw/IPOSProxy/ManageTerminal
2025/07/28 15:59:28.283	[localhost]	[SOAPClient]	HTTP Headers - Content-Type: text/xml; SOAPAction: http://ticketxpress.com.tw/IPOSProxy/ManageTerminal
2025/07/28 15:59:28.312	[localhost]	[SOAPClient]	Fallback: Adding content as text node: 元素 "m0:Channel" 的前置碼 "m0" 未連結。
2025/07/28 15:59:28.707	[localhost]	[SOAPClient]	SOAP Fault: The server was unable to process the request due to an internal error.
```

### 問題分解
1. **SOAPAction 正確**：`http://ticketxpress.com.tw/IPOSProxy/ManageTerminal` 已正確設定
2. **XML 解析失敗**：`m0:Channel` 前綴未定義，觸發 fallback 機制
3. **格式錯誤**：服務端收到格式錯誤的 SOAP 請求，返回內部錯誤

## 🔧 修復方案

### 1. 統一命名空間定義

#### 修復前（問題代碼）：
```java
// SOAP Envelope 中的命名空間定義
envelope.addNamespaceDeclaration("m0", "http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common");

// XML 轉換中使用的命名空間
soapContent.append("<m:").append(rootElementName).append(" xmlns:m=\"http://ticketxpress.com.tw/\">");
```

#### 修復後（正確代碼）：
```java
// SOAP Envelope 中的統一命名空間定義
envelope.addNamespaceDeclaration("m", "http://ticketxpress.com.tw/");
envelope.addNamespaceDeclaration("m0", "http://ticketxpress.com.tw/");

// XML 轉換中使用相同的命名空間
soapContent.append("<m:").append(rootElementName)
          .append(" xmlns:m=\"http://ticketxpress.com.tw/\"")
          .append(" xmlns:m0=\"http://ticketxpress.com.tw/\">");
```

### 2. 改進 XML 轉換邏輯

#### 修復前（問題邏輯）：
```java
// 可能導致重複前綴或未定義前綴
transformed = cleanContent.replaceAll("<([a-zA-Z][a-zA-Z0-9]*(?![:])[^>]*)", "<m0:$1");
```

#### 修復後（改進邏輯）：
```java
// 統一清理和重新添加前綴
String cleanContent = innerContent.replaceAll("<([a-zA-Z]+:)([a-zA-Z][a-zA-Z0-9]*)", "<$2");
cleanContent = cleanContent.replaceAll("</([a-zA-Z]+:)([a-zA-Z][a-zA-Z0-9]*)", "</$2");

// 統一使用 m0: 前綴
String transformed = cleanContent.replaceAll("<([a-zA-Z][a-zA-Z0-9]*)(\\s|>)", "<m0:$1$2");
transformed = transformed.replaceAll("</([a-zA-Z][a-zA-Z0-9]*)>", "</m0:$1>");
```

### 3. 增強錯誤處理機制

#### 新增 Raw XML Fallback：
```java
private void addRawXMLToBody(SOAPBody soapBody, String xmlContent) throws SOAPException {
    // 直接解析原始 XML 並添加到 SOAP Body
    javax.xml.parsers.DocumentBuilderFactory factory = javax.xml.parsers.DocumentBuilderFactory.newInstance();
    factory.setNamespaceAware(true);
    // ... 解析和轉換邏輯
}
```

## 📋 修復檢查清單

### SOAPClient.java 修改
- [ ] ✅ 修改 SOAP Envelope 命名空間定義
- [ ] ✅ 統一 `transformToSOAPFormat` 中的命名空間
- [ ] ✅ 改進 `transformInnerElements` 的前綴處理邏輯
- [ ] ✅ 添加 `addRawXMLToBody` fallback 機制
- [ ] ✅ 增強錯誤處理和日誌記錄

### 命名空間一致性
- [ ] ✅ 確保 SOAP Envelope 和 XML 內容使用相同的命名空間 URI
- [ ] ✅ 統一前綴定義（m: 和 m0: 都指向同一個命名空間）
- [ ] ✅ 清理衝突的命名空間前綴

### 錯誤處理改進
- [ ] ✅ 改進 XML 解析失敗時的 fallback 機制
- [ ] ✅ 添加詳細的調試資訊
- [ ] ✅ 記錄命名空間處理的每個步驟

## 🧪 測試驗證

### 1. 執行自動測試
```bash
test_soap_namespace_fix.bat
```

### 2. 手動驗證步驟

#### 檢查命名空間定義：
```xml
<!-- 修復後的 SOAP Envelope 應該包含 -->
<SOAP-ENV:Envelope 
    xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:m="http://ticketxpress.com.tw/"
    xmlns:m0="http://ticketxpress.com.tw/">
    <SOAP-ENV:Body>
        <m:ManageTerminal xmlns:m="http://ticketxpress.com.tw/" xmlns:m0="http://ticketxpress.com.tw/">
            <m0:manageTerminalRequest>
                <m0:Channel>WEB</m0:Channel>
                <m0:TerminalId>TEST001</m0:TerminalId>
            </m0:manageTerminalRequest>
        </m:ManageTerminal>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>
```

#### 檢查 Console 輸出：
```
=== SOAP Envelope 命名空間設定 ===
m: http://ticketxpress.com.tw/
m0: http://ticketxpress.com.tw/
================================

=== Transform Inner Elements Debug ===
Before transformation:
<manageTerminalRequest><Channel>WEB</Channel><TerminalId>TEST001</TerminalId></manageTerminalRequest>

After adding namespace prefixes:
- All elements: m0: prefix (mapped to http://ticketxpress.com.tw/)
<m0:manageTerminalRequest><m0:Channel>WEB</m0:Channel><m0:TerminalId>TEST001</m0:TerminalId></m0:manageTerminalRequest>
```

### 3. 驗證修復效果

#### 修復前的錯誤：
```
❌ 元素 "m0:Channel" 的前置碼 "m0" 未連結
❌ Fallback: Adding content as text node
❌ SOAP Fault: The server was unable to process the request due to an internal error
```

#### 修復後的預期結果：
```
✅ SOAP Envelope 命名空間設定完成
✅ XML validation passed!
✅ SOAP 請求成功發送
✅ 服務端正確處理請求
```

## 🔍 故障排除

### 問題 1：仍然出現命名空間錯誤
**可能原因**：
- 命名空間 URI 不匹配
- 前綴定義位置錯誤

**解決方案**：
```java
// 確保所有命名空間都指向相同的 URI
envelope.addNamespaceDeclaration("m", "http://ticketxpress.com.tw/");
envelope.addNamespaceDeclaration("m0", "http://ticketxpress.com.tw/");
```

### 問題 2：XML 格式仍然無效
**可能原因**：
- 正則表達式處理錯誤
- 元素嵌套問題

**解決方案**：
```java
// 使用更精確的正則表達式
String transformed = cleanContent.replaceAll("<([a-zA-Z][a-zA-Z0-9]*)(\\s|>)", "<m0:$1$2");
```

### 問題 3：服務端仍然返回錯誤
**可能原因**：
- SOAP 請求格式不符合服務端要求
- 業務邏輯錯誤

**解決方案**：
1. 檢查服務端的 WSDL 定義
2. 比較成功的 SOAP 請求範例
3. 聯絡服務提供者確認格式要求

## 📊 效果監控

### 成功指標
- ✅ 不再出現 "前置碼未連結" 錯誤
- ✅ XML 驗證通過
- ✅ 服務端正確處理請求
- ✅ 收到業務相關的回應（而非內部錯誤）

### 監控方法
```java
// 在日誌中記錄命名空間處理狀態
log.append("Namespace processing: ")
   .append(validation.isValid() ? "SUCCESS" : "FAILED")
   .append("\r\n");
```

## 🔄 後續改進

### 1. 動態命名空間檢測
```java
// 根據服務 URL 動態確定命名空間
private String getNamespaceForService(String serviceUrl) {
    if (serviceUrl.contains("tixpress")) {
        return "http://ticketxpress.com.tw/";
    }
    return "http://tempuri.org/";
}
```

### 2. WSDL 解析支援
```java
// 從 WSDL 自動獲取正確的命名空間定義
private Map<String, String> parseNamespacesFromWSDL(String wsdlUrl) {
    // 解析 WSDL 並提取命名空間定義
}
```

### 3. XML Schema 驗證
```java
// 使用 XML Schema 驗證 SOAP 請求格式
private boolean validateAgainstSchema(String xmlContent, String schemaUrl) {
    // 根據 Schema 驗證 XML 格式
}
```

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
