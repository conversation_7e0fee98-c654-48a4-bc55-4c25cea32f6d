# SOAP XML 格式修復指南

## 問題概述

雖然 SOAP 連線已經成功建立，但生成的 SOAP 請求 XML 格式與服務端期望的格式不匹配，導致服務端無法正確處理請求。

## 🔍 格式差異分析

### 問題格式 vs 正確格式對比

| 項目 | 問題格式 | 正確格式 | 狀態 |
|------|----------|----------|------|
| **m0 命名空間** | `http://ticketxpress.com.tw/` | `http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common` | ❌ 錯誤 |
| **manageTerminalRequest 前綴** | `<m0:manageTerminalRequest>` | `<m:manageTerminalRequest>` | ❌ 錯誤 |
| **TerminalCode 格式** | `<TerminalCode/>` | `<m0:TerminalCode></m0:TerminalCode>` | ❌ 錯誤 |
| **SOAP Header** | `<SOAP-ENV:Header/>` | 無 Header | ❌ 多餘 |

### 詳細差異分析

#### 1. 命名空間 URI 錯誤
```xml
<!-- 問題格式 -->
xmlns:m0="http://ticketxpress.com.tw/"

<!-- 正確格式 -->
xmlns:m0="http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common"
```

#### 2. 元素前綴不一致
```xml
<!-- 問題格式 -->
<m0:manageTerminalRequest>
    <m0:Channel>Test</m0:Channel>
</m0:manageTerminalRequest>

<!-- 正確格式 -->
<m:manageTerminalRequest>
    <m0:Channel>Test</m0:Channel>
</m:manageTerminalRequest>
```

#### 3. 自閉合標籤問題
```xml
<!-- 問題格式 -->
<TerminalCode/>

<!-- 正確格式 -->
<m0:TerminalCode></m0:TerminalCode>
```

## 🔧 修復方案

### 1. 修正命名空間定義

#### SOAPClient.java - createSOAPRequest 方法
```java
// 修復前
envelope.addNamespaceDeclaration("m0", "http://ticketxpress.com.tw/");
envelope.addNamespaceDeclaration("m", "http://ticketxpress.com.tw/");

// 修復後
envelope.addNamespaceDeclaration("m0", "http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common");
envelope.addNamespaceDeclaration("m", "http://ticketxpress.com.tw/");
```

### 2. 修正元素前綴分配邏輯

#### transformInnerElements 方法修復
```java
// 修復前（統一使用 m0: 前綴）
String transformed = cleanContent.replaceAll("<([a-zA-Z][a-zA-Z0-9]*)(\\s|>)", "<m0:$1$2");

// 修復後（區分不同元素的前綴）
String transformed = cleanContent;

// 1. manageTerminalRequest 使用 m: 前綴
transformed = transformed.replaceAll("<(manageTerminalRequest)(\\s|>)", "<m:$1$2");
transformed = transformed.replaceAll("</(manageTerminalRequest)>", "</m:$1>");

// 2. 其他元素使用 m0: 前綴
transformed = transformed.replaceAll("<(?!m:)(?!m0:)([a-zA-Z][a-zA-Z0-9]*)(\\s|>)", "<m0:$1$2");
transformed = transformed.replaceAll("</(?!m:)(?!m0:)([a-zA-Z][a-zA-Z0-9]*)>", "</m0:$1>");
```

### 3. 移除空的 SOAP Header

#### createSOAPRequest 方法中添加
```java
// 移除空的 SOAP Header（根據正確格式，不應該包含空的 Header）
SOAPHeader soapHeader = envelope.getHeader();
if (soapHeader != null && !soapHeader.hasChildNodes()) {
    soapHeader.detachNode();
    System.out.println("✅ 已移除空的 SOAP Header");
}
```

### 4. 修正 copyDOMToSOAP 方法

#### 根據元素名稱分配正確的命名空間
```java
// 修復前
QName childName = new QName("http://ticketxpress.com.tw/", elementName, "m0");

// 修復後
QName childName;
if ("manageTerminalRequest".equals(elementName)) {
    // manageTerminalRequest 使用 m: 前綴
    childName = new QName("http://ticketxpress.com.tw/", elementName, "m");
} else {
    // 其他元素使用 m0: 前綴
    childName = new QName("http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common", elementName, "m0");
}
```

## 📋 修復檢查清單

### SOAPClient.java 修改項目
- [ ] ✅ 修正 SOAP Envelope 中的 m0 命名空間 URI
- [ ] ✅ 修正 transformInnerElements 方法的前綴分配邏輯
- [ ] ✅ 添加移除空 SOAP Header 的邏輯
- [ ] ✅ 修正 copyDOMToSOAP 方法的命名空間處理
- [ ] ✅ 移除 transformToSOAPFormat 中多餘的命名空間宣告

### 格式驗證項目
- [ ] ✅ m0 命名空間指向正確的 datacontract URI
- [ ] ✅ manageTerminalRequest 使用 m: 前綴
- [ ] ✅ 其他業務元素使用 m0: 前綴
- [ ] ✅ 不包含空的 SOAP Header
- [ ] ✅ 所有元素都有正確的開始和結束標籤

## 🧪 測試驗證

### 1. 執行自動測試
```bash
test_soap_format_fix.bat
```

### 2. 預期的測試結果
```
=== 格式檢查 ===
✅ manageTerminalRequest 使用正確的 m: 前綴
✅ 其他元素使用正確的 m0: 前綴
✅ TerminalCode 使用正確的完整標籤格式
```

### 3. 完整的正確 SOAP 電文格式
```xml
<SOAP-ENV:Envelope 
    xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" 
    xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
    xmlns:m0="http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common" 
    xmlns:m="http://ticketxpress.com.tw/">
    <SOAP-ENV:Body>
        <m:ManageTerminal>
            <m:manageTerminalRequest>
                <m0:Channel>Test</m0:Channel>
                <m0:Checksum>B43DB40CD926E971464816D781CFF69B</m0:Checksum>
                <m0:ManageTerminalDateTime>20151015105959</m0:ManageTerminalDateTime>
                <m0:ManageType>101</m0:ManageType>
                <m0:MerchantCode>000000000000038</m0:MerchantCode>
                <m0:ProgramCode>00001</m0:ProgramCode>
                <m0:ShopCode>0000001028</m0:ShopCode>
                <m0:TerminalCode></m0:TerminalCode>
                <m0:TerminalSSN>20151015105959000001</m0:TerminalSSN>
            </m:manageTerminalRequest>
        </m:ManageTerminal>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>
```

## 🔍 故障排除

### 問題 1：命名空間仍然錯誤
**症狀**：m0 命名空間仍指向錯誤的 URI
**解決方案**：
```java
// 確保使用正確的命名空間 URI
envelope.addNamespaceDeclaration("m0", "http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common");
```

### 問題 2：前綴分配錯誤
**症狀**：manageTerminalRequest 仍使用 m0: 前綴
**解決方案**：
```java
// 確保正則表達式的執行順序正確
// 先處理特殊元素，再處理一般元素
transformed = transformed.replaceAll("<(manageTerminalRequest)(\\s|>)", "<m:$1$2");
```

### 問題 3：自閉合標籤問題
**症狀**：空元素仍顯示為 `<element/>`
**解決方案**：
- 檢查 XML 解析器設定
- 確保使用完整的開始和結束標籤

## 📊 效果監控

### 成功指標
- ✅ SOAP 電文格式與服務端期望完全匹配
- ✅ 服務端能夠正確解析請求
- ✅ 收到正確的業務回應（而非格式錯誤）
- ✅ 不再出現 "internal error" 錯誤

### 監控方法
```java
// 在日誌中記錄格式驗證結果
log.append("SOAP format validation: ")
   .append(isFormatCorrect ? "PASSED" : "FAILED")
   .append("\r\n");
```

## 🔄 後續改進

### 1. 動態格式檢測
```java
// 根據服務 URL 動態確定正確的格式
private SOAPFormat getFormatForService(String serviceUrl) {
    if (serviceUrl.contains("tixpress")) {
        return new TixpressSOAPFormat();
    }
    return new DefaultSOAPFormat();
}
```

### 2. 格式驗證器
```java
// 驗證生成的 SOAP 電文格式
public boolean validateSOAPFormat(String soapXML, String expectedFormat) {
    // 比較生成的格式與期望格式
    return formatMatches(soapXML, expectedFormat);
}
```

### 3. 格式模板系統
```java
// 使用模板生成正確格式的 SOAP 電文
public String generateFromTemplate(String templateName, Map<String, String> data) {
    // 根據模板和數據生成 SOAP 電文
}
```

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
