# SOAP XML 除錯指南

## 概述

本文件提供 SOAP XML 格式錯誤的診斷和修復方法，特別針對命名空間前綴衝突問題。

## 🚨 常見錯誤分析

### 1. "m:ns0" 錯誤

**錯誤訊息：**
```
[Fatal Error] :1:7: 元素類型 "m:ns0" 之後必須緊接屬性設定 ">" 或 "/>"。
```

**原因分析：**
- 原始 XML 包含 `ns0:` 前綴的元素
- 轉換過程中錯誤地將 `ns0:` 前綴與 `m:` 前綴組合
- 結果產生無效的 `m:ns0:ElementName` 格式

**範例問題：**
```xml
<!-- 原始 XML -->
<ns0:AP xmlns:ns0="http://test.namespace">
    <ManageTerminal>
        <Channel>Test</Channel>
    </ManageTerminal>
</ns0:AP>

<!-- 錯誤的轉換結果 -->
<m:ns0:ManageTerminal xmlns:m="http://ticketxpress.com.tw/">
    <m0:ns0:Channel>Test</m0:Channel>  <!-- 無效！ -->
</m:ns0:ManageTerminal>
```

## 🔧 修復方案

### 1. 改進的 transformInnerElements 方法

**修復前：**
```java
// 直接添加前綴，沒有處理現有命名空間
String transformed = innerContent.replaceAll("<([a-zA-Z][a-zA-Z0-9]*)", "<m0:$1");
```

**修復後：**
```java
// 首先移除已存在的命名空間前綴
String cleanContent = innerContent.replaceAll("<(ns\\d+:)([a-zA-Z][a-zA-Z0-9]*)", "<$2");
cleanContent = cleanContent.replaceAll("</(ns\\d+:)([a-zA-Z][a-zA-Z0-9]*)", "</$2");

// 然後添加 m0: 前綴
String transformed = cleanContent.replaceAll("<([a-zA-Z][a-zA-Z0-9]*(?![:])[^>]*)", "<m0:$1");
transformed = transformed.replaceAll("</([a-zA-Z][a-zA-Z0-9]*)", "</m0:$1");
```

### 2. 正確的轉換流程

**步驟 1：移除現有命名空間**
```xml
<!-- 輸入 -->
<ns0:AP xmlns:ns0="http://test.namespace">
    <ns0:ManageTerminal>
        <ns0:Channel>Test</ns0:Channel>
    </ns0:ManageTerminal>
</ns0:AP>

<!-- 移除 ns0: 前綴後 -->
<AP xmlns:ns0="http://test.namespace">
    <ManageTerminal>
        <Channel>Test</Channel>
    </ManageTerminal>
</AP>
```

**步驟 2：提取內部內容**
```xml
<ManageTerminal>
    <Channel>Test</Channel>
</ManageTerminal>
```

**步驟 3：添加新的命名空間前綴**
```xml
<m0:ManageTerminal>
    <m0:Channel>Test</m0:Channel>
</m0:ManageTerminal>
```

**步驟 4：包裝在 SOAP 格式中**
```xml
<m:ManageTerminal xmlns:m="http://ticketxpress.com.tw/">
    <m0:ManageTerminal>
        <m0:Channel>Test</m0:Channel>
    </m0:ManageTerminal>
</m:ManageTerminal>
```

## 🔍 除錯工具使用

### 1. Console 輸出分析

執行 SOAP 請求時，查看 Console 輸出：

```
=== SOAPClient Debug Info ===
Original Request Content:
<ns0:AP xmlns:ns0="http://test.namespace">
    <ManageTerminal>
        <Channel>Test</Channel>
    </ManageTerminal>
</ns0:AP>

=== Transform Inner Elements Debug ===
Before transformation:
<ManageTerminal>
    <Channel>Test</Channel>
</ManageTerminal>

After removing existing namespaces:
<ManageTerminal>
    <Channel>Test</Channel>
</ManageTerminal>

After adding m0 prefix:
<m0:ManageTerminal>
    <m0:Channel>Test</m0:Channel>
</m0:ManageTerminal>

=== XML Validation Debug ===
✅ XML Validation PASSED
```

### 2. XML 驗證結果

如果出現驗證錯誤：

```
❌ XML Validation FAILED
Error: XML 解析錯誤 at line 1, column 7: 元素類型 "m:ns0" 之後必須緊接屬性設定 ">" 或 "/>"。

=== XML Error Analysis ===
Error at line 1, column 7
Error line content: <m:ns0:ManageTerminal xmlns:m="http://ticketxpress.com.tw/">
Error position:     ^
🔍 Detected namespace prefix conflict!
```

### 3. 自動修復過程

```
🔧 Attempting to clean namespace conflicts...

=== Cleaning Namespace Conflicts ===
Before cleaning:
<m:ns0:ManageTerminal xmlns:m="http://ticketxpress.com.tw/">

After cleaning:
<m:ManageTerminal xmlns:m="http://ticketxpress.com.tw/">

✅ XML validation passed after cleaning!
```

## 📋 除錯檢查清單

### 步驟 1：檢查原始 XML
- [ ] 確認原始 XML 格式正確
- [ ] 識別現有的命名空間前綴（ns0:, ns1: 等）
- [ ] 檢查是否有特殊字符或編碼問題

### 步驟 2：檢查轉換過程
- [ ] 查看 "Transform Inner Elements Debug" 輸出
- [ ] 確認命名空間前綴被正確移除
- [ ] 驗證 m0: 前綴被正確添加

### 步驟 3：檢查最終 SOAP Envelope
- [ ] 查看 "Complete SOAP Envelope" 輸出
- [ ] 確認所有命名空間宣告正確
- [ ] 驗證 XML 結構完整

### 步驟 4：驗證 XML 格式
- [ ] 查看 XML 驗證結果
- [ ] 如果失敗，檢查錯誤位置和原因
- [ ] 確認自動修復是否成功

## 🛠️ 手動修復方法

### 1. 預處理原始 XML

如果自動修復無效，可以手動預處理：

```java
// 在發送 SOAP 請求前預處理
String cleanedXML = originalXML.replaceAll("<ns\\d+:", "<");
cleanedXML = cleanedXML.replaceAll("</ns\\d+:", "</");
cleanedXML = cleanedXML.replaceAll("xmlns:ns\\d+=\"[^\"]*\"", "");
```

### 2. 自定義轉換邏輯

```java
public String customTransform(String xmlContent) {
    // 移除所有現有命名空間前綴
    String cleaned = xmlContent.replaceAll("<([a-zA-Z]+\\d*:)([a-zA-Z][a-zA-Z0-9]*)", "<$2");
    
    // 重新添加統一的前綴
    cleaned = cleaned.replaceAll("<([a-zA-Z][a-zA-Z0-9]*)", "<m0:$1");
    cleaned = cleaned.replaceAll("</([a-zA-Z][a-zA-Z0-9]*)", "</m0:$1");
    
    return cleaned;
}
```

## 🔧 常見問題解決

### 問題 1：多層命名空間衝突
```xml
<!-- 問題 -->
<m:ns0:ns1:Element>

<!-- 解決方案 -->
<m:Element>
```

### 問題 2：屬性中的命名空間
```xml
<!-- 問題 -->
<Element ns0:attr="value">

<!-- 解決方案 -->
<m0:Element attr="value">
```

### 問題 3：CDATA 區段處理
```xml
<!-- 問題 -->
<![CDATA[<ns0:Element>]]>

<!-- 解決方案 -->
<![CDATA[<Element>]]>
```

## 📞 故障排除步驟

1. **啟用詳細日誌**：確保所有 Console 輸出都被顯示
2. **逐步檢查**：按照除錯輸出逐步檢查每個轉換階段
3. **手動驗證**：使用 XML 編輯器驗證生成的 SOAP Envelope
4. **測試簡化版本**：使用簡單的 XML 測試轉換邏輯
5. **檢查服務端**：確認 SOAP 服務期望的格式

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
