# TERMINO 值獲取修復指南

## 問題概述

在測試統一回覆訊息邏輯時，發現 `getTerminoValue` 方法無法正確獲取 TERMINO 值，返回 null 或預設值，而不是從原始請求中獲取的實際 TERMINO 值。回覆電文的 TERMINO 應該與 POS IN XML（原始請求）中的 TERMINO 完全相同。

## 🔍 問題分析

### 根本原因
1. **上下文缺失**：方法沒有訪問原始請求的能力，只能看到回應 XML
2. **邏輯錯誤**：當前的邏輯是從回應 XML 中提取 TERMINO，但實際上應該從原始請求中提取
3. **生成新值**：當找不到 TERMINO 時，錯誤地生成新的時間戳值
4. **一致性問題**：回覆中的 TERMINO 與原始請求不一致

### 業務需求
- **TERMINO 一致性**：回覆訊息中的 TERMINO 值應該直接從原始請求的 HEADER 中提取
- **不生成新值**：不需要生成新的 TERMINO 值或使用預設值
- **請求回覆對應**：TERMINO 值應該在請求和回覆之間保持一致

### 問題代碼分析
```java
// 問題 1：getTerminoValue 方法
private String getTerminoValue(Element xml) {
  // xml 是回應 XML，可能是 SOAP 回應，沒有 HEADER
  Element headerElement = xml.element("HEADER");
  if (headerElement == null) {
    return null; // ❌ 無法獲取原始請求的 TERMINO
  }
  // ...
}

// 問題 2：getResponseTerminoValue 方法
private String getResponseTerminoValue(Element responseXml) {
  // 嘗試從回應 XML 中獲取，但應該從原始請求中獲取
  Element header = responseXml.element("HEADER");
  if (header != null) {
    return getElementTextSafe(header, "TERMINO", null);
  }
  // 生成預設的 TERMINO 值 ❌ 錯誤
  return Utility.getDateTime("yyyyMMddHHmmss") + "000001";
}
```

## 🔧 修復方案

### 1. 添加原始請求上下文存儲

#### 新增實例變數：
```java
// 存儲原始請求的 TERMINO 值，用於回覆訊息
private String originalTermino = null;
private Element originalRequestHeader = null;
```

#### 在 onMessage 方法中存儲原始請求資訊：
```java
// 存儲原始請求的 TERMINO 和 HEADER 資訊，用於回覆訊息
Element originalHeader = this.root.element("HEADER");
if (originalHeader != null) {
  this.originalTermino = originalHeader.elementText("TERMINO");
  this.originalRequestHeader = originalHeader.createCopy(); // 創建副本避免引用問題
  
  System.out.println("=== 存儲原始請求資訊 ===");
  System.out.println("原始 TERMINO: " + this.originalTermino);
  System.out.println("原始 FROM: " + originalHeader.elementText("FROM"));
  System.out.println("原始 TO: " + originalHeader.elementText("TO"));
  System.out.println("========================");
}
```

### 2. 修復 getTerminoValue 方法

#### 修復前（問題代碼）：
```java
private String getTerminoValue(Element xml) {
  // 只能從當前 XML（可能是回應）中獲取
  Element headerElement = xml.element("HEADER");
  if (headerElement == null) {
    return null; // ❌ 無法獲取原始請求的 TERMINO
  }
  // ...
}
```

#### 修復後（正確代碼）：
```java
private String getTerminoValue(Element xml) {
  try {
    // 優先使用存儲的原始請求 TERMINO
    if (this.originalTermino != null && !this.originalTermino.trim().isEmpty()) {
      System.out.println("✅ 使用原始請求的 TERMINO: " + this.originalTermino);
      return this.originalTermino;
    }
    
    // 如果沒有存儲的原始 TERMINO，嘗試從當前 XML 中提取
    Element headerElement = xml.element("HEADER");
    if (headerElement == null) {
      // 嘗試從存儲的原始請求 HEADER 中獲取
      if (this.originalRequestHeader != null) {
        String originalTermino = this.originalRequestHeader.elementText("TERMINO");
        if (originalTermino != null && !originalTermino.trim().isEmpty()) {
          return originalTermino;
        }
      }
      return getDefaultTermino();
    }
    
    String terminoValue = headerElement.elementText("TERMINO");
    return (terminoValue != null && !terminoValue.trim().isEmpty()) ? terminoValue : getDefaultTermino();
    
  } catch (Exception e) {
    return getDefaultTermino();
  }
}
```

### 3. 修復 getResponseTerminoValue 方法

#### 修復前（問題代碼）：
```java
private String getResponseTerminoValue(Element responseXml) {
  Element header = responseXml.element("HEADER");
  if (header != null) {
    String termino = getElementTextSafe(header, "TERMINO", null);
    if (termino != null) {
      return termino;
    }
  }
  // 生成預設的 TERMINO 值 ❌ 錯誤
  return Utility.getDateTime("yyyyMMddHHmmss") + "000001";
}
```

#### 修復後（正確代碼）：
```java
private String getResponseTerminoValue(Element responseXml) {
  try {
    // 優先使用存儲的原始請求 TERMINO
    if (this.originalTermino != null && !this.originalTermino.trim().isEmpty()) {
      System.out.println("✅ 使用原始請求的 TERMINO: " + this.originalTermino);
      return this.originalTermino;
    }
    
    // 嘗試從回應 XML 的 HEADER 中獲取
    Element header = responseXml.element("HEADER");
    if (header != null) {
      String termino = getElementTextSafe(header, "TERMINO", null);
      if (termino != null && !termino.trim().isEmpty()) {
        return termino;
      }
    }
    
    // 嘗試從存儲的原始請求 HEADER 中獲取
    if (this.originalRequestHeader != null) {
      String originalTermino = this.originalRequestHeader.elementText("TERMINO");
      if (originalTermino != null && !originalTermino.trim().isEmpty()) {
        return originalTermino;
      }
    }
    
    // 最後手段：生成預設值（但這不應該發生）
    String defaultTermino = Utility.getDateTime("yyyyMMddHHmmss") + "000001";
    System.out.println("⚠️ 無法獲取原始 TERMINO，生成預設值: " + defaultTermino);
    return defaultTermino;
    
  } catch (Exception e) {
    return Utility.getDateTime("yyyyMMddHHmmss") + "000001";
  }
}
```

### 4. 修復 buildOLTPHeader 方法

#### 修復前（問題代碼）：
```java
// 從原始請求或回應中獲取 HEADER 資訊
Element originalHeader = null;
if (!isSOAP) {
  originalHeader = responseXml.element("HEADER");
}

if (originalHeader == null) {
  // 使用預設值構建 ❌ 無法獲取原始請求資訊
  headerXml.append("\t\t<ns0:TERMINO>").append(getResponseTerminoValue(responseXml)).append("</ns0:TERMINO>\n");
}
```

#### 修復後（正確代碼）：
```java
// 優先使用存儲的原始請求 HEADER 資訊
Element originalHeader = this.originalRequestHeader;

if (originalHeader != null) {
  System.out.println("✅ 使用存儲的原始請求 HEADER，進行 FROM/TO 互換");
  
  // FROM/TO 互換：原請求的 TO 變成回覆的 FROM，原請求的 FROM 變成回覆的 TO
  String originalFrom = getElementTextSafe(originalHeader, "FROM", "UNKNOWN");
  String originalTo = getElementTextSafe(originalHeader, "TO", "UNKNOWN");
  headerXml.append("\t\t<ns0:FROM>").append(originalTo).append("</ns0:FROM>\n");
  headerXml.append("\t\t<ns0:TO>").append(originalFrom).append("</ns0:TO>\n");
  
  // TERMINO 保持不變（使用原始請求的 TERMINO）
  String originalTermino = getElementTextSafe(originalHeader, "TERMINO", this.originalTermino);
  headerXml.append("\t\t<ns0:TERMINO>").append(originalTermino).append("</ns0:TERMINO>\n");
  
  System.out.println("原始請求 TERMINO: " + originalTermino + " -> 回覆 TERMINO: " + originalTermino);
}
```

## 📋 修復檢查清單

### QueueListener.java 修改項目
- [ ] ✅ 添加 `originalTermino` 實例變數
- [ ] ✅ 添加 `originalRequestHeader` 實例變數
- [ ] ✅ 修改 `onMessage` 方法存儲原始請求資訊
- [ ] ✅ 修復 `getTerminoValue` 方法優先使用原始 TERMINO
- [ ] ✅ 修復 `getResponseTerminoValue` 方法確保一致性
- [ ] ✅ 修復 `buildOLTPHeader` 方法使用原始請求資訊
- [ ] ✅ 添加詳細的調試資訊和錯誤處理

### 業務邏輯改進
- [ ] ✅ 確保 TERMINO 值在請求和回覆之間保持一致
- [ ] ✅ 不再生成新的 TERMINO 值
- [ ] ✅ 正確進行 FROM/TO 欄位互換
- [ ] ✅ 提供完整的錯誤處理和 fallback 機制

## 🧪 測試驗證

### 1. 執行自動測試
```bash
test_termino_fix.bat
```

### 2. 預期的測試結果
```
=== 模擬設定原始請求資訊 ===
✅ 原始請求資訊設定完成
原始 TERMINO: 20250722972549010000011637
原始 FROM: BU00100001
原始 TO: BU01600010

=== 測試案例 1：SOAP 回應（沒有 HEADER）===
SOAP 回應 getTerminoValue 結果: 20250722972549010000011637
預期結果: 20250722972549010000011637
結果正確: true

=== 測試案例 2：HTTP 回應（有 HEADER）===
HTTP 回應 getTerminoValue 結果: 20250722972549010000011637
預期結果: 20250722972549010000011637
結果正確: true

buildOLTPHeader 包含正確的 TERMINO: true
包含 FROM/TO 欄位: true
```

### 3. 修復前後對比

#### 修復前（問題行為）：
```
❌ getTerminoValue 結果: null
❌ getTerminoValue 結果: DEFAULT_REPLY_QUEUE
❌ getResponseTerminoValue 結果: 20250728183528000001 (生成的新值)
❌ 結果正確: false
❌ 回覆 TERMINO 與原始請求不一致
```

#### 修復後（正確行為）：
```
✅ 使用原始請求的 TERMINO: 20250722972549010000011637
✅ getTerminoValue 結果: 20250722972549010000011637
✅ getResponseTerminoValue 結果: 20250722972549010000011637
✅ 結果正確: true
✅ 回覆 TERMINO 與原始請求完全一致
```

## 🔍 故障排除

### 問題 1：仍然返回 null 或預設值
**可能原因**：
- 原始請求資訊沒有正確存儲
- 存儲的時機不對

**解決方案**：
```java
// 檢查原始請求資訊是否正確存儲
System.out.println("存儲的原始 TERMINO: " + this.originalTermino);
System.out.println("存儲的原始 HEADER: " + (this.originalRequestHeader != null ? "存在" : "null"));
```

### 問題 2：FROM/TO 互換不正確
**可能原因**：
- 原始請求 HEADER 資訊缺失
- 互換邏輯錯誤

**解決方案**：
```java
// 檢查原始請求的 FROM/TO 值
String originalFrom = getElementTextSafe(originalHeader, "FROM", "UNKNOWN");
String originalTo = getElementTextSafe(originalHeader, "TO", "UNKNOWN");
System.out.println("原始 FROM: " + originalFrom + " -> 回覆 TO: " + originalFrom);
System.out.println("原始 TO: " + originalTo + " -> 回覆 FROM: " + originalTo);
```

### 問題 3：TERMINO 值不一致
**可能原因**：
- 多個地方使用不同的 TERMINO 獲取邏輯
- 存儲的原始值被覆蓋

**解決方案**：
```java
// 統一使用存儲的原始 TERMINO
if (this.originalTermino != null && !this.originalTermino.trim().isEmpty()) {
  return this.originalTermino; // 始終返回原始值
}
```

## 📊 效果監控

### 成功指標
- ✅ 回覆訊息中的 TERMINO 與原始請求完全相同
- ✅ 不再生成新的 TERMINO 值
- ✅ FROM/TO 欄位正確互換
- ✅ 所有測試案例都返回正確的 TERMINO 值

### 監控方法
```java
// 在日誌中記錄 TERMINO 處理結果
log.append("Original TERMINO: ").append(this.originalTermino)
   .append(", Reply TERMINO: ").append(replyTermino)
   .append(", Consistent: ").append(this.originalTermino.equals(replyTermino))
   .append("\r\n");
```

### Console 輸出檢查
```
=== 存儲原始請求資訊 ===
原始 TERMINO: 20250722972549010000011637
原始 FROM: BU00100001
原始 TO: BU01600010
========================

=== 獲取 TERMINO 值（修復版）===
✅ 使用原始請求的 TERMINO: 20250722972549010000011637
==============================

原始請求 TERMINO: 20250722972549010000011637 -> 回覆 TERMINO: 20250722972549010000011637
```

## 🔄 後續改進

### 1. TERMINO 管理器
```java
public class TerminoManager {
    private String originalTermino;
    private Element originalHeader;
    
    public void setOriginalRequest(Element requestHeader) {
        this.originalTermino = requestHeader.elementText("TERMINO");
        this.originalHeader = requestHeader.createCopy();
    }
    
    public String getTermino() {
        return this.originalTermino;
    }
}
```

### 2. 請求上下文管理
```java
public class RequestContext {
    private static final ThreadLocal<RequestContext> CONTEXT = new ThreadLocal<>();
    
    private String termino;
    private String from;
    private String to;
    
    public static void setOriginalRequest(Element header) {
        RequestContext context = new RequestContext();
        context.termino = header.elementText("TERMINO");
        context.from = header.elementText("FROM");
        context.to = header.elementText("TO");
        CONTEXT.set(context);
    }
    
    public static RequestContext get() {
        return CONTEXT.get();
    }
}
```

### 3. 一致性驗證器
```java
public class ConsistencyValidator {
    public static boolean validateTerminoConsistency(String originalTermino, String replyTermino) {
        return originalTermino != null && originalTermino.equals(replyTermino);
    }
}
```

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
