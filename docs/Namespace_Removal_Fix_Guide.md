# 命名空間移除修復指南

## 問題概述

`removeNamespaces` 方法中的正則表達式過度移除內容，導致 XML 結構損壞。具體問題是結束標籤中的正斜線 "/" 被錯誤移除，導致 XML 無法正確解析。

## 🔍 問題分析

### 錯誤現象
#### 修復前的錯誤輸出：
```xml
<ManageTerminalResponse><ManageTerminalResult><Checksum>a87519574d731f03955b107379c9c5be<Checksum><Message>Success<Message><ResponseCode>0000<ResponseCode><ServerDate>20250729<ServerDate><ServerTime>151026<ServerTime><WorkKey>eK+DO15GrIpkI/2muc5i3YryM2wrzTMYQQfoSELTw/833SOEtTXwSQ==<WorkKey></ManageTerminalResult></ManageTerminalResponse>
```

#### 預期的正確輸出：
```xml
<ManageTerminalResponse><ManageTerminalResult><Checksum>a87519574d731f03955b107379c9c5be</Checksum><Message>Success</Message><ResponseCode>0000</ResponseCode><ServerDate>20250729</ServerDate><ServerTime>151026</ServerTime><WorkKey>eK+DO15GrIpkI/2muc5i3YryM2wrzTMYQQfoSELTw/833SOEtTXwSQ==</WorkKey></ManageTerminalResult></ManageTerminalResponse>
```

### 根本原因分析
#### 有問題的正則表達式：
```java
// 修復前（問題代碼）
cleanContent = cleanContent.replaceAll("<[^>]*?:", "<");      // ❌ 過度匹配
cleanContent = cleanContent.replaceAll("</[^>]*?:", "</");    // ❌ 錯誤移除正斜線
cleanContent = cleanContent.replaceAll("\\s+[^=\\s]*?:", " "); // ❌ 過度匹配屬性
```

#### 問題分析：
1. **`</[^>]*?:`** 會匹配任何結束標籤中冒號前的內容
2. 當 XML 中沒有命名空間前綴時，`[^>]*?` 會匹配標籤名稱
3. 導致結束標籤的正斜線和標籤名稱被移除
4. 結果：`</Checksum>` 變成 `<Checksum>`

## 🔧 修復方案

### 1. 精確的正則表達式匹配

#### 修復前（問題代碼）：
```java
// 過度匹配，會損壞沒有命名空間的標籤
cleanContent = cleanContent.replaceAll("<[^>]*?:", "<");
cleanContent = cleanContent.replaceAll("</[^>]*?:", "</");
cleanContent = cleanContent.replaceAll("\\s+[^=\\s]*?:", " ");
```

#### 修復後（精確代碼）：
```java
// 精確匹配 prefix:tagname 格式，只移除 prefix: 部分
cleanContent = cleanContent.replaceAll("<([a-zA-Z0-9_-]+):([a-zA-Z0-9_-]+)", "<$2");
cleanContent = cleanContent.replaceAll("</([a-zA-Z0-9_-]+):([a-zA-Z0-9_-]+)", "</$2");
cleanContent = cleanContent.replaceAll("\\s+([a-zA-Z0-9_-]+):([a-zA-Z0-9_-]+)=", " $2=");
```

### 2. 正則表達式詳細說明

#### 開始標籤處理：
```java
// 匹配：<prefix:tagname 格式
// 替換：<tagname
cleanContent = cleanContent.replaceAll("<([a-zA-Z0-9_-]+):([a-zA-Z0-9_-]+)", "<$2");

// 範例：
// 輸入：<ns0:ManageTerminalResponse>
// 輸出：<ManageTerminalResponse>
```

#### 結束標籤處理：
```java
// 匹配：</prefix:tagname 格式
// 替換：</tagname
cleanContent = cleanContent.replaceAll("</([a-zA-Z0-9_-]+):([a-zA-Z0-9_-]+)", "</$2");

// 範例：
// 輸入：</ns0:ManageTerminalResponse>
// 輸出：</ManageTerminalResponse>
```

#### 屬性處理：
```java
// 匹配：prefix:attribute= 格式
// 替換：attribute=
cleanContent = cleanContent.replaceAll("\\s+([a-zA-Z0-9_-]+):([a-zA-Z0-9_-]+)=", " $2=");

// 範例：
// 輸入：<element ns0:version="1.0">
// 輸出：<element version="1.0">
```

### 3. XML 結構完整性驗證

#### 新增 validateXMLStructure 方法：
```java
private boolean validateXMLStructure(String xmlContent) {
  try {
    // 1. 檢查標籤配對
    int openTagCount = 0;
    int closeTagCount = 0;
    
    // 計算開始標籤和結束標籤數量
    String[] openTags = xmlContent.split("<[^/!?][^>]*[^/]>");
    openTagCount = openTags.length - 1;
    
    String[] closeTags = xmlContent.split("</[^>]+>");
    closeTagCount = closeTags.length - 1;
    
    boolean tagCountMatch = (openTagCount == closeTagCount);
    
    // 2. 檢查是否有有效的結束標籤
    boolean hasValidCloseTags = xmlContent.contains("</");
    
    // 3. 嘗試解析 XML
    boolean parseValid = false;
    try {
      DocumentHelper.parseText(xmlContent);
      parseValid = true;
    } catch (Exception e) {
      parseValid = false;
    }
    
    return tagCountMatch && hasValidCloseTags && parseValid;
    
  } catch (Exception e) {
    return false;
  }
}
```

### 4. 錯誤處理和回退機制

#### 安全的處理流程：
```java
private String removeNamespaces(String xmlContent) {
  try {
    // 執行命名空間移除
    String cleanContent = performNamespaceRemoval(xmlContent);
    
    // 驗證結果
    boolean structureValid = validateXMLStructure(cleanContent);
    
    if (!structureValid) {
      System.out.println("⚠️ XML 結構驗證失敗，返回原始內容");
      return xmlContent; // 回退到原始內容
    }
    
    return cleanContent;
    
  } catch (Exception e) {
    System.out.println("❌ 移除命名空間時發生錯誤: " + e.getMessage());
    return xmlContent; // 回退到原始內容
  }
}
```

## 📋 修復檢查清單

### QueueListener.java 修改項目
- [ ] ✅ 修改 `removeNamespaces` 方法的正則表達式
- [ ] ✅ 使用精確匹配 `prefix:tagname` 格式
- [ ] ✅ 分別處理開始標籤、結束標籤和屬性
- [ ] ✅ 新增 `validateXMLStructure` 方法
- [ ] ✅ 添加 XML 解析驗證
- [ ] ✅ 實作錯誤處理和回退機制
- [ ] ✅ 改進調試資訊和日誌記錄

### 功能改進
- [ ] ✅ 確保 XML 結構完整性
- [ ] ✅ 只移除實際的命名空間前綴
- [ ] ✅ 保留所有有效的 XML 結構字符
- [ ] ✅ 提供詳細的驗證和調試資訊

## 🧪 測試驗證

### 1. 執行自動測試
```bash
test_namespace_fix.bat
```

### 2. 測試案例覆蓋

#### 測試案例 1：有命名空間前綴的 XML
```xml
<!-- 輸入 -->
<ns0:ManageTerminalResponse xmlns:ns0="http://ticketxpress.com.tw/">
  <ns0:ManageTerminalResult>
    <ns0:Checksum>a87519574d731f03955b107379c9c5be</ns0:Checksum>
    <ns0:Message>Success</ns0:Message>
  </ns0:ManageTerminalResult>
</ns0:ManageTerminalResponse>

<!-- 預期輸出 -->
<ManageTerminalResponse>
  <ManageTerminalResult>
    <Checksum>a87519574d731f03955b107379c9c5be</Checksum>
    <Message>Success</Message>
  </ManageTerminalResult>
</ManageTerminalResponse>
```

#### 測試案例 2：沒有命名空間前綴的 XML
```xml
<!-- 輸入 -->
<ManageTerminalResponse>
  <ManageTerminalResult>
    <Checksum>a87519574d731f03955b107379c9c5be</Checksum>
    <Message>Success</Message>
  </ManageTerminalResult>
</ManageTerminalResponse>

<!-- 預期輸出（應該保持不變）-->
<ManageTerminalResponse>
  <ManageTerminalResult>
    <Checksum>a87519574d731f03955b107379c9c5be</Checksum>
    <Message>Success</Message>
  </ManageTerminalResult>
</ManageTerminalResponse>
```

#### 測試案例 3：混合命名空間的 XML
```xml
<!-- 輸入 -->
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
  <s:Body>
    <ns0:ManageTerminalResponse xmlns:ns0="http://ticketxpress.com.tw/">
      <ns0:ManageTerminalResult>
        <ns0:Checksum>test</ns0:Checksum>
      </ns0:ManageTerminalResult>
    </ns0:ManageTerminalResponse>
  </s:Body>
</s:Envelope>

<!-- 預期輸出 -->
<Envelope>
  <Body>
    <ManageTerminalResponse>
      <ManageTerminalResult>
        <Checksum>test</Checksum>
      </ManageTerminalResult>
    </ManageTerminalResponse>
  </Body>
</Envelope>
```

### 3. 預期的測試結果
```
=== 測試案例 1：有命名空間前綴的 XML ===
測試類型: 有命名空間前綴
包含命名空間前綴: false
包含命名空間宣告: false
有效的結束標籤: true
正確的結構: true
測試結果: ✅ 成功
結構驗證: ✅ 有效

=== 測試案例 2：沒有命名空間前綴的 XML ===
測試類型: 沒有命名空間前綴
包含命名空間前綴: false
包含命名空間宣告: false
有效的結束標籤: true
正確的結構: true
測試結果: ✅ 成功
結構驗證: ✅ 有效
```

## 🔍 故障排除

### 問題 1：仍然出現結構損壞
**可能原因**：
- 正則表達式沒有正確匹配特殊字符
- XML 包含不標準的命名空間格式

**解決方案**：
```java
// 檢查具體的匹配情況
System.out.println("處理前: " + xmlContent);
System.out.println("處理後: " + cleanContent);
System.out.println("匹配的開始標籤: " + Arrays.toString(xmlContent.split("<([a-zA-Z0-9_-]+):([a-zA-Z0-9_-]+)")));
```

### 問題 2：驗證失敗但 XML 看起來正確
**可能原因**：
- 驗證邏輯過於嚴格
- 特殊的 XML 格式沒有被考慮

**解決方案**：
```java
// 調整驗證邏輯
private boolean validateXMLStructure(String xmlContent) {
  try {
    // 主要依賴 XML 解析器驗證
    DocumentHelper.parseText(xmlContent);
    return true;
  } catch (Exception e) {
    System.out.println("XML 解析失敗: " + e.getMessage());
    return false;
  }
}
```

### 問題 3：特殊字符處理問題
**可能原因**：
- XML 內容包含特殊字符
- 編碼問題

**解決方案**：
```java
// 添加特殊字符處理
cleanContent = cleanContent.replaceAll("&lt;", "<");
cleanContent = cleanContent.replaceAll("&gt;", ">");
cleanContent = cleanContent.replaceAll("&amp;", "&");
```

## 📊 效果監控

### 成功指標
- ✅ 結束標籤保持完整（包含正斜線 "/"）
- ✅ 命名空間前綴被正確移除
- ✅ XML 結構驗證通過
- ✅ 處理後的 XML 可以正確解析

### 監控方法
```java
// 在日誌中記錄處理結果
log.append("Namespace removal: ")
   .append(structureValid ? "SUCCESS" : "FAILED")
   .append(", Original length: ").append(xmlContent.length())
   .append(", Clean length: ").append(cleanContent.length())
   .append("\r\n");
```

### Console 輸出檢查
```
=== 移除命名空間（修復版）===
原始內容長度: 245
清理後內容長度: 180
移除的字符數量: 65
XML 結構完整性: ✅ 有效
==================

=== 驗證 XML 結構完整性 ===
開始標籤數量: 3
結束標籤數量: 3
標籤配對: ✅
結束標籤有效: ✅
XML 解析測試: ✅ 成功
整體結構: ✅ 有效
==========================
```

## 🔄 後續改進

### 1. 命名空間處理策略
```java
public enum NamespaceStrategy {
    REMOVE_ALL,      // 移除所有命名空間
    REMOVE_SPECIFIC, // 只移除特定命名空間
    PRESERVE_STRUCTURE // 保留結構，只移除宣告
}
```

### 2. XML 驗證器增強
```java
public class XMLValidator {
    public static ValidationResult validate(String xmlContent) {
        // 更詳細的驗證邏輯
        return new ValidationResult(isValid, errors, warnings);
    }
}
```

### 3. 性能優化
```java
public class NamespaceRemover {
    private static final Pattern NAMESPACE_PREFIX_PATTERN = 
        Pattern.compile("<([a-zA-Z0-9_-]+):([a-zA-Z0-9_-]+)");
    
    public static String removeNamespaces(String xmlContent) {
        // 使用預編譯的正則表達式提高性能
    }
}
```

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
