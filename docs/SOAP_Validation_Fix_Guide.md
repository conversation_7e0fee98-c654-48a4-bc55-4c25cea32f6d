# SOAP 驗證修復指南

## 問題概述

SOAP 連線已經成功建立並且服務端正常回覆，但在 QueueListener.java 的 XML 驗證過程中出現 SAX 解析錯誤：`cvc-elt.1: 找不到元素 's:Envelope' 的宣告`。

## 🔍 問題分析

### 錯誤現象
```
2025/07/28 17:59:32.680	[localhost]	[QueueListener]	UUID: e00237d9-4528-45ee-969d-43361221f74a
org.xml.sax.SAXException: cvc-elt.1: 找不到元素 's:Envelope' 的宣告。
	at com.pic.o2o.common.QueueListener.validator(QueueListener.java:405)
	at com.pic.o2o.common.QueueListener.onMessage(QueueListener.java:178)
```

### 根本原因
1. **XML Schema 不包含 SOAP 定義**：業務用的 XML Schema 檔案不包含 SOAP Envelope 的定義
2. **驗證邏輯不區分內容類型**：`validator` 方法對所有 XML 內容使用相同的 Schema 驗證
3. **SOAP 回應被誤用業務 Schema 驗證**：服務端回應的 SOAP Envelope 被當作業務 XML 進行驗證

### 技術細節
```java
// 問題代碼（修復前）
private void validator(String document) throws SAXException, IOException {
    SchemaFactory factory = SchemaFactory.newInstance("http://www.w3.org/2001/XMLSchema");
    Source schemaFile = new StreamSource(new File(SCHEMA_FILEPATH)); // 業務 Schema
    Schema schema = factory.newSchema(schemaFile);
    // ... 對所有 XML 使用相同的 Schema 驗證
}

// SOAP 回應格式
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>...</s:Body>
</s:Envelope>
// 問題：業務 Schema 中沒有 s:Envelope 的定義
```

## 🔧 修復方案

### 1. 添加 SOAP 回應檢測

#### 新增 isSOAPResponse 方法：
```java
private boolean isSOAPResponse(String document) {
    try {
        // 檢查是否包含 SOAP Envelope 相關的命名空間和元素
        return document.contains("soap:Envelope") || 
               document.contains("s:Envelope") || 
               document.contains("SOAP-ENV:Envelope") ||
               document.contains("http://schemas.xmlsoap.org/soap/envelope/") ||
               document.contains("http://www.w3.org/2003/05/soap-envelope");
    } catch (Exception e) {
        System.out.println("檢查 SOAP 回應時發生錯誤: " + e.getMessage());
        return false;
    }
}
```

### 2. 實作 SOAP 專用驗證

#### 新增 validateSOAPResponse 方法：
```java
private void validateSOAPResponse(String document) throws SAXException {
    try {
        // 使用基本的 XML 格式驗證（不使用 Schema）
        javax.xml.parsers.DocumentBuilderFactory factory = 
            javax.xml.parsers.DocumentBuilderFactory.newInstance();
        factory.setNamespaceAware(true);
        factory.setValidating(false); // 不使用 DTD 驗證
        
        javax.xml.parsers.DocumentBuilder builder = factory.newDocumentBuilder();
        
        // 解析 SOAP 回應
        java.io.ByteArrayInputStream bis = new java.io.ByteArrayInputStream(document.getBytes("UTF-8"));
        org.w3c.dom.Document doc = builder.parse(bis);
        
        // 檢查 SOAP 結構
        org.w3c.dom.Element root = doc.getDocumentElement();
        String rootName = root.getLocalName();
        
        // 驗證是否為有效的 SOAP Envelope
        if (!"Envelope".equals(rootName)) {
            throw new SAXException("無效的 SOAP 結構：根元素不是 Envelope，而是 " + rootName);
        }
        
        System.out.println("✅ SOAP 回應格式驗證通過");
        
    } catch (Exception e) {
        throw new SAXException("SOAP 回應驗證失敗: " + e.getMessage(), e);
    }
}
```

### 3. 修改 validator 方法

#### 添加智能分支邏輯：
```java
private void validator(String document) throws SAXException, IOException {
    try {
        System.out.println("=== XML Schema Validation Debug ===");
        
        // 檢查是否為 SOAP 回應
        if (isSOAPResponse(document)) {
            System.out.println("🔍 檢測到 SOAP 回應");
            
            if (SKIP_SOAP_VALIDATION) {
                System.out.println("⚠️ 配置為跳過 SOAP 驗證，直接通過");
                return;
            } else {
                System.out.println("使用 SOAP 專用驗證");
                validateSOAPResponse(document);
                return;
            }
        }
        
        // 原有的 Schema 驗證邏輯（用於業務 XML）
        SchemaFactory factory = SchemaFactory.newInstance("http://www.w3.org/2001/XMLSchema");
        // ... 原有邏輯
        
    } catch (Exception e) {
        System.out.println("❌ XML Schema 驗證失敗: " + e.getMessage());
        throw e;
    }
}
```

### 4. 添加配置選項

#### 新增配置變數：
```java
public static boolean SKIP_SOAP_VALIDATION = false;
```

#### 在 loadProperties 中載入配置：
```java
// 載入 SOAP 驗證配置（預設為 false，即不跳過驗證）
String skipSoapValidation = PROPS.getString("SKIP.SOAP.VALIDATION", "false");
SKIP_SOAP_VALIDATION = "true".equalsIgnoreCase(skipSoapValidation);
```

## 📋 修復檢查清單

### QueueListener.java 修改項目
- [ ] ✅ 添加 `SKIP_SOAP_VALIDATION` 配置變數
- [ ] ✅ 修改 `loadProperties` 方法載入新配置
- [ ] ✅ 修改 `validator` 方法添加 SOAP 檢測邏輯
- [ ] ✅ 新增 `isSOAPResponse` 檢測方法
- [ ] ✅ 新增 `validateSOAPResponse` 專用驗證方法
- [ ] ✅ 增強錯誤處理和調試資訊

### 配置檔案更新
- [ ] ✅ 在 `config.properties` 中添加 `SKIP.SOAP.VALIDATION` 設定
- [ ] ✅ 根據需求設定為 `true`（跳過）或 `false`（驗證）

### 測試驗證
- [ ] ✅ 執行測試腳本驗證修復效果
- [ ] ✅ 確認不再出現 SAX 解析錯誤
- [ ] ✅ 驗證 SOAP 回應能正常處理

## 🧪 測試驗證

### 1. 執行自動測試
```bash
test_soap_validation_fix.bat
```

### 2. 預期的測試結果
```
=== SOAP 驗證修復測試 ===
isSOAPResponse 結果: true
✅ 正確檢測到 SOAP 回應
✅ SOAP 回應驗證通過
SKIP_SOAP_VALIDATION 設定: false
✅ 完整的 validator 方法測試通過
```

### 3. 配置選項說明

#### 選項 1：跳過 SOAP 驗證（推薦）
```properties
# config.properties
SKIP.SOAP.VALIDATION=true
```
- **優點**：避免 Schema 衝突，處理速度快
- **缺點**：不檢查 SOAP 格式錯誤
- **適用**：SOAP 服務穩定運作時

#### 選項 2：使用 SOAP 基本驗證
```properties
# config.properties
SKIP.SOAP.VALIDATION=false
```
- **優點**：檢查基本的 SOAP 格式
- **缺點**：可能有額外的處理開銷
- **適用**：需要驗證 SOAP 格式時

## 🔍 故障排除

### 問題 1：仍然出現 SAX 解析錯誤
**可能原因**：
- 配置檔案未正確載入
- SOAP 檢測邏輯未正確識別

**解決方案**：
```java
// 檢查配置載入
System.out.println("SKIP_SOAP_VALIDATION: " + SKIP_SOAP_VALIDATION);

// 檢查 SOAP 檢測
System.out.println("isSOAPResponse: " + isSOAPResponse(document));
```

### 問題 2：SOAP 驗證失敗
**可能原因**：
- SOAP 回應格式異常
- 編碼問題

**解決方案**：
```java
// 檢查 SOAP 回應內容
System.out.println("SOAP Response:");
System.out.println(document);

// 檢查編碼
java.io.ByteArrayInputStream bis = new java.io.ByteArrayInputStream(document.getBytes("UTF-8"));
```

### 問題 3：配置檔案路徑問題
**可能原因**：
- config.properties 檔案路徑錯誤
- 權限問題

**解決方案**：
```java
// 檢查配置檔案路徑
System.out.println("Config file path: " + configFilePath);
System.out.println("File exists: " + new File(configFilePath).exists());
```

## 📊 效果監控

### 成功指標
- ✅ 不再出現 "找不到元素 's:Envelope' 的宣告" 錯誤
- ✅ SOAP 回應能夠正常通過驗證流程
- ✅ 系統能夠正確處理服務端的回應內容
- ✅ 業務 XML 仍然使用原有的 Schema 驗證

### 監控方法
```java
// 在日誌中記錄驗證結果
log.append("Validation type: ")
   .append(isSOAP ? "SOAP" : "Business")
   .append(", Result: ")
   .append(validationPassed ? "PASSED" : "FAILED")
   .append("\r\n");
```

### Console 輸出檢查
```
=== XML Schema Validation Debug ===
🔍 檢測到 SOAP 回應
⚠️ 配置為跳過 SOAP 驗證，直接通過
==================================
```

## 🔄 後續改進

### 1. SOAP Schema 支援
```java
// 未來可以添加專門的 SOAP Schema 驗證
private static final String SOAP_SCHEMA_PATH = "soap-envelope.xsd";

private void validateSOAPWithSchema(String document) throws SAXException {
    // 使用 SOAP 專用的 Schema 進行驗證
}
```

### 2. 驗證策略模式
```java
public interface ValidationStrategy {
    void validate(String document) throws SAXException;
}

public class BusinessXMLValidator implements ValidationStrategy { ... }
public class SOAPResponseValidator implements ValidationStrategy { ... }
```

### 3. 配置管理改進
```java
public class ValidationConfig {
    private boolean skipSOAPValidation;
    private String businessSchemaPath;
    private String soapSchemaPath;
    
    // 配置管理邏輯
}
```

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
