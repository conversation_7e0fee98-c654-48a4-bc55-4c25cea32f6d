# OLTP-SOAP 整合指南

## 概述

本文件說明如何在 OLTP 協議中整合 SOAP 功能。SOAP 轉換只應用於 `<ns0:AP>` 元素內部的業務邏輯內容，而保持外層的 OLTP 協議結構不變。

## 🎯 轉換範圍

### ✅ 轉換範圍（僅 AP 內容）
- `<ns0:AP>` 元素內部的業務邏輯內容
- 業務資料的命名空間前綴轉換
- SOAP Envelope 包裝

### ❌ 保持不變（OLTP 結構）
- `<ns0:OLTP>` 根元素及其命名空間
- `<ns0:HEADER>` 完整結構
- `<ns0:AP>` 標籤本身
- 所有 OLTP 協議相關的元素

## 🔄 轉換範例

### Before（原始 OLTP XML）
```xml
<?xml version="1.0" encoding="utf-8"?>
<ns0:OLTP xmlns:ns0="http://7-11.com.tw/online">
	<ns0:HEADER>
		<ns0:VER>01.01</ns0:VER>
		<ns0:FROM>BU00100001</ns0:FROM>
		<ns0:TERMINO>20150304972549010000011400</ns0:TERMINO>
		<ns0:TO>BU01600010</ns0:TO>
		<ns0:BUSINESS>0160100</ns0:BUSINESS>
		<ns0:DATE>20150304</ns0:DATE>
		<ns0:TIME>140000</ns0:TIME>
		<ns0:STATCODE>0000</ns0:STATCODE>
		<ns0:STATDESC/>
	</ns0:HEADER>
	<ns0:AP>
		<ManageTerminal>
			<manageTerminalRequest>
				<Channel>Test</Channel>
				<Checksum>B43DB40CD926E971464816D781CFF69B</Checksum>
				<ManageTerminalDateTime>20151015105959</ManageTerminalDateTime>
				<ManageType>101</ManageType>
				<MerchantCode>000000000000038</MerchantCode>
				<ProgramCode>00001</ProgramCode>
				<ShopCode>0000001028</ShopCode>
				<TerminalCode></TerminalCode>
				<TerminalSSN>20151015105959000001</TerminalSSN>
			</manageTerminalRequest>
		</ManageTerminal>
	</ns0:AP>
</ns0:OLTP>
```

### After（OLTP + SOAP 整合）
```xml
<?xml version="1.0" encoding="utf-8"?> 
<ns0:OLTP xmlns:ns0="http://7-11.com.tw/online"> 
	<ns0:HEADER> 
		<ns0:VER>01.01</ns0:VER> 
		<ns0:FROM>BU00100001</ns0:FROM> 
		<ns0:TERMINO>20150304972549010000011400</ns0:TERMINO> 
		<ns0:TO>BU01600010</ns0:TO> 
		<ns0:BUSINESS>0160100</ns0:BUSINESS> 
		<ns0:DATE>20150304</ns0:DATE> 
		<ns0:TIME>140000</ns0:TIME> 
		<ns0:STATCODE>0000</ns0:STATCODE> 
		<ns0:STATDESC/> 
	</ns0:HEADER> 
	<ns0:AP> 
		<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" 
		                   xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" 
		                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
		                   xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
		                   xmlns:m0="http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common">
			<SOAP-ENV:Body>
				<m:ManageTerminal xmlns:m="http://ticketxpress.com.tw/">
					<m:manageTerminalRequest>
						<m0:Channel>Test</m0:Channel>
						<m0:Checksum>B43DB40CD926E971464816D781CFF69B</m0:Checksum>
						<m0:ManageTerminalDateTime>20151015105959</m0:ManageTerminalDateTime>
						<m0:ManageType>101</m0:ManageType>
						<m0:MerchantCode>000000000000038</m0:MerchantCode>
						<m0:ProgramCode>00001</ProgramCode>
						<m0:ShopCode>0000001028</m0:ShopCode>
						<m0:TerminalCode></m0:TerminalCode>
						<m0:TerminalSSN>20151015105959000001</TerminalSSN>
					</m:manageTerminalRequest>
				</m:ManageTerminal>
			</SOAP-ENV:Body>
		</SOAP-ENV:Envelope>
	</ns0:AP> 
</ns0:OLTP>
```

## 🔧 轉換流程

### 步驟 1：檢測 OLTP 格式
```java
if (content.contains("<ns0:OLTP") && content.contains("<ns0:AP>")) {
    // 這是 OLTP 格式，需要特殊處理
    return transformOLTPWithSOAP(content);
}
```

### 步驟 2：提取 AP 內容
```java
// 找到 <ns0:AP> 和 </ns0:AP> 標籤
int apStartTag = oltpXml.indexOf("<ns0:AP>");
int contentStart = oltpXml.indexOf('>', apStartTag) + 1;
int apEndTag = oltpXml.indexOf("</ns0:AP>");

// 提取 AP 內容
String apContent = oltpXml.substring(contentStart, apEndTag).trim();
```

### 步驟 3：轉換 AP 內容為 SOAP 格式
```java
// 對 AP 內容進行 SOAP 轉換
String transformedContent = transformToSOAPFormat(apContent);

// 包裝在 SOAP Envelope 中
String soapWrappedContent = createSOAPEnvelopeForAPContent(transformedContent);
```

### 步驟 4：替換回 OLTP 結構
```java
// 將 SOAP 包裝的內容放回 OLTP 的 AP 部分
String result = replaceAPContent(oltpXml, soapWrappedContent);
```

## 🧪 Console 輸出範例

執行 OLTP-SOAP 轉換時，您會看到以下除錯輸出：

```
=== SimpleSOAPClient Debug Info ===
Original Request Content:
<?xml version="1.0" encoding="utf-8"?>
<ns0:OLTP xmlns:ns0="http://7-11.com.tw/online">
	<ns0:HEADER>
		<ns0:VER>01.01</ns0:VER>
		...
	</ns0:HEADER>
	<ns0:AP>
		<ManageTerminal>
			<manageTerminalRequest>
				<Channel>Test</Channel>
				...
			</manageTerminalRequest>
		</ManageTerminal>
	</ns0:AP>
</ns0:OLTP>

=== OLTP-SOAP Transformation Debug ===
Input OLTP XML:
[完整的 OLTP XML]

=== Transforming OLTP with SOAP ===
Extracted AP Content:
<ManageTerminal>
	<manageTerminalRequest>
		<Channel>Test</Channel>
		...
	</manageTerminalRequest>
</ManageTerminal>

SOAP Wrapped AP Content:
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" ...>
	<SOAP-ENV:Body>
		<m:ManageTerminal xmlns:m="http://ticketxpress.com.tw/">
			<m:manageTerminalRequest>
				<m0:Channel>Test</m0:Channel>
				...
			</m:manageTerminalRequest>
		</m:ManageTerminal>
	</SOAP-ENV:Body>
</SOAP-ENV:Envelope>

Final OLTP with SOAP:
[完整的轉換結果]
```

## 📋 驗證檢查清單

### OLTP 結構保持檢查
- [ ] `<ns0:OLTP>` 根元素保持不變
- [ ] `xmlns:ns0="http://7-11.com.tw/online"` 命名空間保持不變
- [ ] `<ns0:HEADER>` 完整結構保持不變
- [ ] 所有 HEADER 子元素（VER, FROM, TO 等）保持不變
- [ ] `<ns0:AP>` 和 `</ns0:AP>` 標籤保持不變

### SOAP 轉換檢查
- [ ] AP 內容被 SOAP Envelope 包裝
- [ ] 包含正確的 SOAP 命名空間
- [ ] 業務元素添加了 `m0:` 前綴
- [ ] 根業務元素添加了 `m:` 前綴和命名空間

## 🔄 與現有功能的相容性

### SOAP_SubProcess 方法
- 自動檢測 OLTP 格式
- 只對 AP 內容進行轉換
- 保持與現有 HTTP_SubProcess 相同的錯誤處理

### writeXmlAsFileWithoutEncryption 方法
- 記錄完整的 OLTP 結構（包含 SOAP 包裝的 AP）
- 檔案名稱包含 `_SOAP` 後綴
- 不對內容進行加密

## 🧪 測試驗證

### 執行測試
```bash
# 編譯測試
javac -cp "libs/*:bin" test/com/pic/o2o/common/OLTPSOAPTransformationTest.java

# 執行測試
java -cp "libs/*:bin:test" org.junit.runner.JUnitCore com.pic.o2o.common.OLTPSOAPTransformationTest
```

### 測試案例
1. **完整 OLTP-SOAP 轉換** - 驗證完整流程
2. **AP 內容提取** - 驗證正確提取 AP 內容
3. **內容替換** - 驗證 SOAP 內容正確替換
4. **非 OLTP XML 處理** - 驗證一般 XML 的處理
5. **空 AP 內容處理** - 驗證邊界情況

## ⚠️ 注意事項

1. **協議相容性**：確保 SOAP 服務能夠處理嵌入在 OLTP 結構中的 SOAP 請求
2. **命名空間管理**：避免 OLTP 和 SOAP 命名空間之間的衝突
3. **錯誤處理**：SOAP 錯誤需要正確映射到 OLTP 錯誤碼
4. **效能考量**：額外的 XML 解析和轉換可能影響效能

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
