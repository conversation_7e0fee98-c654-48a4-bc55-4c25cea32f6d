# 增強版命名空間移除和檔案寫入修復指南

## 問題概述

修復 QueueListener.java 中的兩個關鍵問題：

1. **AP 層仍然包含命名空間**：修改後的 `removeNamespaces` 方法沒有正確處理所有類型的命名空間
2. **回覆電文檔案寫入功能失效**：`writeReplyXMLToFile` 方法沒有被正確執行

## 🔍 問題分析

### 問題 1：命名空間移除不完整

#### 當前錯誤輸出：
```xml
<ManageTerminalResponse xmlns="http://ticketxpress.com.tw/"><ManageTerminalResult xmlns:a="http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><a:Checksum>a87519574d731f03955b107379c9c5be</a:Checksum><a:Message>Success</a:Message><a:ResponseCode>0000</a:ResponseCode><a:ServerDate>20250729</a:ServerDate><a:ServerTime>152336</a:ServerTime><a:WorkKey>eK+DO15GrIpkI/2muc5i3YryM2wrzTMYQQfoSELTw/833SOEtTXwSQ==</a:WorkKey></ManageTerminalResult></ManageTerminalResponse>
```

#### 預期正確輸出：
```xml
<ManageTerminalResponse><ManageTerminalResult><Checksum>a87519574d731f03955b107379c9c5be</Checksum><Message>Success</Message><ResponseCode>0000</ResponseCode><ServerDate>20250729</ServerDate><ServerTime>152336</ServerTime><WorkKey>eK+DO15GrIpkI/2muc5i3YryM2wrzTMYQQfoSELTw/833SOEtTXwSQ==</WorkKey></ManageTerminalResult></ManageTerminalResponse>
```

#### 問題分析：
1. **預設命名空間宣告**：`xmlns="http://ticketxpress.com.tw/"` 沒有被移除
2. **命名空間前綴宣告**：`xmlns:a="..."`, `xmlns:i="..."` 沒有被移除
3. **帶前綴的標籤**：`<a:Checksum>`, `<a:Message>`, `<a:ResponseCode>` 等沒有被處理

### 問題 2：檔案寫入功能失效

#### 可能原因：
1. **路徑配置問題**：`LOG_FILEPATH` 可能未正確配置
2. **權限問題**：目錄可能沒有寫入權限
3. **錯誤處理不足**：異常被靜默處理，沒有詳細的調試資訊

## 🔧 修復方案

### 1. 增強 removeNamespaces 方法

#### 修復前（不完整的處理）：
```java
// 只處理了部分命名空間類型
cleanContent = cleanContent.replaceAll("\\s+xmlns:[^=]+=\"[^\"]*\"", "");
cleanContent = cleanContent.replaceAll("\\s+xmlns=\"[^\"]*\"", "");
cleanContent = cleanContent.replaceAll("<([a-zA-Z0-9_-]+):([a-zA-Z0-9_-]+)", "<$2");
```

#### 修復後（完整的處理）：
```java
private String removeNamespaces(String xmlContent) {
  try {
    String cleanContent = xmlContent;
    int step = 1;

    // 步驟 1：移除所有命名空間宣告
    System.out.println("步驟 " + step++ + "：移除命名空間宣告");
    
    // 移除預設命名空間宣告 xmlns="..."
    String beforeDefault = cleanContent;
    cleanContent = cleanContent.replaceAll("\\s+xmlns=\"[^\"]*\"", "");
    if (!beforeDefault.equals(cleanContent)) {
      System.out.println("  ✅ 移除預設命名空間宣告");
    }
    
    // 移除命名空間前綴宣告 xmlns:prefix="..."
    String beforePrefix = cleanContent;
    cleanContent = cleanContent.replaceAll("\\s+xmlns:[^=\\s]+=\"[^\"]*\"", "");
    if (!beforePrefix.equals(cleanContent)) {
      System.out.println("  ✅ 移除命名空間前綴宣告");
    }

    // 步驟 2：移除開始標籤中的命名空間前綴
    System.out.println("步驟 " + step++ + "：移除開始標籤中的命名空間前綴");
    String beforeOpenTags = cleanContent;
    // 支援更多字符類型：a-zA-Z0-9_.-
    cleanContent = cleanContent.replaceAll("<([a-zA-Z0-9_.-]+):([a-zA-Z0-9_.-]+)", "<$2");
    if (!beforeOpenTags.equals(cleanContent)) {
      System.out.println("  ✅ 移除開始標籤命名空間前綴");
    }
    
    // 步驟 3：移除結束標籤中的命名空間前綴
    System.out.println("步驟 " + step++ + "：移除結束標籤中的命名空間前綴");
    String beforeCloseTags = cleanContent;
    cleanContent = cleanContent.replaceAll("</([a-zA-Z0-9_.-]+):([a-zA-Z0-9_.-]+)", "</$2");
    if (!beforeCloseTags.equals(cleanContent)) {
      System.out.println("  ✅ 移除結束標籤命名空間前綴");
    }

    // 步驟 4：移除屬性中的命名空間前綴
    System.out.println("步驟 " + step++ + "：移除屬性中的命名空間前綴");
    String beforeAttributes = cleanContent;
    cleanContent = cleanContent.replaceAll("\\s+([a-zA-Z0-9_.-]+):([a-zA-Z0-9_.-]+)=", " $2=");
    if (!beforeAttributes.equals(cleanContent)) {
      System.out.println("  ✅ 移除屬性命名空間前綴");
    }

    // 步驟 5：清理格式
    cleanContent = cleanContent.replaceAll("\\s+", " ");
    cleanContent = cleanContent.replaceAll(">\\s+<", "><");
    cleanContent = cleanContent.trim();

    return cleanContent;
    
  } catch (Exception e) {
    return xmlContent; // 返回原始內容
  }
}
```

### 2. 增強 writeReplyXMLToFile 方法

#### 修復前（缺乏調試資訊）：
```java
private void writeReplyXMLToFile(String replyXML, String termino) {
  try {
    String fileName = LOG_FILEPATH + Utility.getDateTime("yyyyMMddHHmmssSSS") + "_" + termino + "_REPLY_SOAP.txt";
    // 簡單的檔案寫入
    OutputStreamWriter osw = new OutputStreamWriter(new FileOutputStream(fileName), Charset.forName("utf-8"));
    // ...
  } catch (Exception e) {
    // 簡單的錯誤處理
  }
}
```

#### 修復後（詳細的調試和錯誤處理）：
```java
private void writeReplyXMLToFile(String replyXML, String termino) {
  try {
    System.out.println("=== 寫入回覆電文到檔案（增強版）===");
    System.out.println("開始執行檔案寫入功能...");
    
    // 詳細的參數檢查
    if (replyXML == null || replyXML.trim().isEmpty()) {
      System.out.println("❌ 回覆 XML 為空，跳過檔案寫入");
      return;
    }
    
    System.out.println("✅ 回覆 XML 參數檢查通過");
    System.out.println("回覆 XML 長度: " + replyXML.length());
    
    // 檢查 LOG_FILEPATH 配置
    System.out.println("LOG_FILEPATH 配置: " + LOG_FILEPATH);
    if (LOG_FILEPATH == null || LOG_FILEPATH.trim().isEmpty()) {
      System.out.println("❌ LOG_FILEPATH 未配置，使用當前目錄");
      String currentDir = System.getProperty("user.dir") + File.separator;
      System.out.println("使用當前目錄: " + currentDir);
    }
    
    // 組出完整檔案路徑
    String basePath = (LOG_FILEPATH != null && !LOG_FILEPATH.trim().isEmpty()) ? 
                      LOG_FILEPATH : (System.getProperty("user.dir") + File.separator);
    String fileName = basePath + Utility.getDateTime("yyyyMMddHHmmssSSS") + "_" + termino + "_REPLY_SOAP.txt";
    System.out.println("完整檔案路徑: " + fileName);
    
    // 確保目錄存在
    File parentDir = new File(fileName).getParentFile();
    System.out.println("父目錄路徑: " + (parentDir != null ? parentDir.getAbsolutePath() : "null"));
    
    if (parentDir != null) {
      if (!parentDir.exists()) {
        System.out.println("目錄不存在，嘗試創建...");
        boolean created = parentDir.mkdirs();
        System.out.println("目錄創建結果: " + (created ? "✅ 成功" : "❌ 失敗"));
      } else {
        System.out.println("✅ 目錄已存在");
      }
      
      // 檢查目錄權限
      System.out.println("目錄可讀: " + parentDir.canRead());
      System.out.println("目錄可寫: " + parentDir.canWrite());
    }
    
    // 寫入檔案
    System.out.println("開始寫入檔案...");
    OutputStreamWriter osw = new OutputStreamWriter(new FileOutputStream(fileName), Charset.forName("utf-8"));
    OutputFormat format = OutputFormat.createPrettyPrint();
    XMLWriter xmlwriter = new XMLWriter(osw, format);
    
    // 寫入內容
    xmlwriter.write("REPLY POS XML (SOAP - No Encryption):");
    xmlwriter.println();
    
    Document replyDoc = DocumentHelper.parseText(replyXML);
    xmlwriter.write(replyDoc);
    
    xmlwriter.close();
    osw.close();
    
    // 驗證檔案是否成功創建
    File createdFile = new File(fileName);
    if (createdFile.exists()) {
      System.out.println("✅ 檔案創建成功");
      System.out.println("檔案大小: " + createdFile.length() + " bytes");
    } else {
      System.out.println("❌ 檔案創建失敗");
    }
    
  } catch (Exception e) {
    System.out.println("❌ 寫入回覆電文檔案時發生錯誤: " + e.getMessage());
    System.out.println("錯誤類型: " + e.getClass().getSimpleName());
    e.printStackTrace();
  }
}
```

## 📋 修復檢查清單

### QueueListener.java 修改項目
- [ ] ✅ 增強 `removeNamespaces` 方法處理所有命名空間類型
- [ ] ✅ 添加步驟化的命名空間移除過程
- [ ] ✅ 支援預設命名空間宣告移除
- [ ] ✅ 支援前綴命名空間宣告移除
- [ ] ✅ 支援標籤和屬性中的命名空間前綴移除
- [ ] ✅ 增強 `writeReplyXMLToFile` 方法添加詳細調試
- [ ] ✅ 修復檔案路徑配置和權限檢查
- [ ] ✅ 改進錯誤處理和驗證機制

### 功能改進
- [ ] ✅ 確保 AP 層內容完全乾淨（無任何命名空間）
- [ ] ✅ 回覆電文檔案寫入功能正常運作
- [ ] ✅ 提供詳細的調試資訊和錯誤報告
- [ ] ✅ 完整的錯誤處理和回退機制

## 🧪 測試驗證

### 1. 執行自動測試
```bash
test_enhanced_namespace_and_file_fix.bat
```

### 2. 測試案例覆蓋

#### 測試案例 1：複雜命名空間的 XML
```xml
<!-- 輸入 -->
<ManageTerminalResponse xmlns="http://ticketxpress.com.tw/">
  <ManageTerminalResult xmlns:a="http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
    <a:Checksum>a87519574d731f03955b107379c9c5be</a:Checksum>
    <a:Message>Success</a:Message>
    <a:ResponseCode>0000</a:ResponseCode>
  </ManageTerminalResult>
</ManageTerminalResponse>

<!-- 預期輸出 -->
<ManageTerminalResponse>
  <ManageTerminalResult>
    <Checksum>a87519574d731f03955b107379c9c5be</Checksum>
    <Message>Success</Message>
    <ResponseCode>0000</ResponseCode>
  </ManageTerminalResult>
</ManageTerminalResponse>
```

#### 測試案例 2：多層嵌套命名空間的 XML
```xml
<!-- 輸入 -->
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
  <s:Body>
    <ns0:ManageTerminalResponse xmlns:ns0="http://ticketxpress.com.tw/" xmlns="http://default.namespace.com">
      <ns0:ManageTerminalResult xmlns:a="http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common">
        <a:Checksum ns0:type="string">test123</a:Checksum>
      </ns0:ManageTerminalResult>
    </ns0:ManageTerminalResponse>
  </s:Body>
</s:Envelope>

<!-- 預期輸出 -->
<Envelope>
  <Body>
    <ManageTerminalResponse>
      <ManageTerminalResult>
        <Checksum type="string">test123</Checksum>
      </ManageTerminalResult>
    </ManageTerminalResponse>
  </Body>
</Envelope>
```

### 3. 預期的測試結果

#### 命名空間移除測試：
```
=== 測試案例 1：複雜命名空間的 XML ===
步驟 1：移除命名空間宣告
  ✅ 移除預設命名空間宣告
  ✅ 移除命名空間前綴宣告
步驟 2：移除開始標籤中的命名空間前綴
  ✅ 移除開始標籤命名空間前綴
步驟 3：移除結束標籤中的命名空間前綴
  ✅ 移除結束標籤命名空間前綴
步驟 4：移除屬性中的命名空間前綴
  ✅ 移除屬性命名空間前綴

包含預設命名空間: false
包含前綴命名空間: false
包含命名空間前綴: false
有效的 XML 結構: true
測試結果: ✅ 成功
```

#### 檔案寫入測試：
```
=== 測試案例 3：檔案寫入功能測試 ===
=== 寫入回覆電文到檔案（增強版）===
開始執行檔案寫入功能...
✅ 回覆 XML 參數檢查通過
回覆 XML 長度: 456
LOG_FILEPATH 配置: test_output/
✅ 目錄已存在
目錄可讀: true
目錄可寫: true
開始寫入檔案...
✅ 標題寫入完成
✅ XML 解析成功
✅ XML 內容寫入完成
✅ 檔案流關閉完成
✅ 檔案創建成功
檔案大小: 456 bytes

檔案寫入測試結果: ✅ 成功
檔案大小正常: ✅ 是
```

## 🔍 故障排除

### 問題 1：某些命名空間仍然存在
**可能原因**：
- 特殊的命名空間格式沒有被正則表達式匹配
- XML 包含非標準的命名空間宣告

**解決方案**：
```java
// 添加更多的命名空間清理規則
cleanContent = cleanContent.replaceAll("\\s+xmlns:[^\\s=]+\\s*=\\s*\"[^\"]*\"", "");
cleanContent = cleanContent.replaceAll("\\s+xmlns\\s*=\\s*\"[^\"]*\"", "");
```

### 問題 2：檔案寫入仍然失敗
**可能原因**：
- 磁碟空間不足
- 檔案名稱包含非法字符
- 防毒軟體阻止檔案寫入

**解決方案**：
```java
// 檢查磁碟空間
File parentDir = new File(fileName).getParentFile();
long freeSpace = parentDir.getFreeSpace();
System.out.println("可用磁碟空間: " + freeSpace + " bytes");

// 清理檔案名稱
termino = termino.replaceAll("[^a-zA-Z0-9_-]", "_");
```

### 問題 3：XML 結構驗證失敗
**可能原因**：
- 命名空間移除導致 XML 格式錯誤
- 特殊字符處理問題

**解決方案**：
```java
// 在每個步驟後驗證 XML
try {
  DocumentHelper.parseText(cleanContent);
  System.out.println("✅ 步驟 " + step + " XML 格式有效");
} catch (Exception e) {
  System.out.println("❌ 步驟 " + step + " XML 格式無效，回退");
  return xmlContent;
}
```

## 📊 效果監控

### 成功指標
- ✅ AP 層內容不包含任何命名空間宣告和前綴
- ✅ 回覆電文檔案正確創建且內容完整
- ✅ 所有測試案例都通過驗證
- ✅ 系統性能沒有明顯影響

### 監控方法
```java
// 在日誌中記錄處理結果
log.append("Namespace cleanup steps: ")
   .append(stepsCompleted)
   .append(", File written: ")
   .append(fileWritten ? "SUCCESS" : "FAILED")
   .append(", File size: ")
   .append(fileSize)
   .append(" bytes\r\n");
```

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
