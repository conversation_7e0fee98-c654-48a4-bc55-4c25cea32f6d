# SOAP 協議整合指南

## 概述

本文件說明如何在 CosmedApi OLTP 系統中使用新增的 SOAP 協議支援功能。SOAP 功能提供與現有 HTTP 協議相同的架構和錯誤處理機制。

## 功能特色

### 🔧 核心功能
- **SOAP_SubProcess**: 處理完整 XML 電文的 SOAP 服務調用
- **SOAP_SubProcess_AP**: 僅處理 AP 層內容的 SOAP 服務調用
- **連線池管理**: 自動管理 SOAP 客戶端連線，提升效能
- **錯誤處理**: 完整的錯誤分類和處理機制
- **日誌記錄**: 詳細的請求和回應日誌

### 🛡️ 錯誤處理
- **1001 系統繁忙**: 連線逾時或處理逾時
- **2001 系統連線失敗**: 無法連接到 SOAP 服務
- **3001 交易處理失敗**: 一般處理錯誤

## 配置設定

### 1. transfile.txt 配置

在 `transfile.txt` 中添加 SOAP 服務配置：

```
# 格式: tocode,subProcess,target,timeout,maintainStat
S001,SOAP_SubProcess,http://soap.example.com/service,30,N
S002,SOAP_SubProcess_AP,http://soap.example.com/service,45,N
```

### 2. 配置參數說明

| 參數 | 說明 | 範例 |
|------|------|------|
| tocode | 交易代碼 | S001 |
| subProcess | 處理類型 | SOAP_SubProcess 或 SOAP_SubProcess_AP |
| target | SOAP 服務 URL | http://soap.example.com/service |
| timeout | 逾時時間（秒） | 30 |
| maintainStat | 維護狀態 | N（正常）或 Y（維護中） |

## 使用方式

### SOAP_SubProcess

處理完整的 XML 電文，適用於需要完整 XML 結構的 SOAP 服務。

**輸入**: 完整的 XML 電文
**輸出**: 完整的 XML 回應

```xml
<!-- 輸入範例 -->
<root>
    <HEADER>
        <FROM>POS001</FROM>
        <TO>S001</TO>
        <TERMINO>TEST001</TERMINO>
    </HEADER>
    <AP>
        <businessData>業務資料</businessData>
    </AP>
</root>
```

### SOAP_SubProcess_AP

僅處理 AP 層內容，適用於只需要業務資料的 SOAP 服務。

**輸入**: AP 層的 XML 內容
**輸出**: 處理後的業務資料

```xml
<!-- 輸入範例（僅 AP 層） -->
<businessData>業務資料</businessData>
```

## 開發指南

### 1. 添加新的 SOAP 服務

1. 在 `transfile.txt` 中添加配置
2. 確保 SOAP 服務端點可訪問
3. 測試服務連線和回應格式

### 2. 錯誤處理最佳實務

```java
// 在 SOAP 服務中實作適當的錯誤處理
try {
    String response = SOAPService.sendSOAPRequest(target, timeout, request);
    // 處理成功回應
} catch (SocketTimeoutException e) {
    // 處理逾時錯誤
} catch (ConnectException e) {
    // 處理連線錯誤
} catch (Exception e) {
    // 處理一般錯誤
}
```

### 3. 日誌監控

系統會自動記錄以下資訊：
- SOAP 請求開始時間
- 服務 URL 和逾時設定
- 處理時間統計
- 錯誤詳細資訊

## 測試

### 單元測試

執行 SOAP 功能的單元測試：

```bash
# 執行所有 SOAP 相關測試
java -cp "libs/*:bin" org.junit.runner.JUnitCore com.pic.oltp.SOAPSubProcessTest
```

### 整合測試

1. 設定測試用的 SOAP 服務
2. 在 `transfile.txt` 中配置測試項目
3. 發送測試電文驗證功能

## 效能調優

### 1. 連線池設定

系統自動管理 SOAP 客戶端連線池：
- 相同 URL 和逾時設定的請求會重用連線
- 定期清理不活躍的連線

### 2. 逾時設定建議

| 服務類型 | 建議逾時時間 |
|----------|--------------|
| 查詢服務 | 30-60 秒 |
| 更新服務 | 60-120 秒 |
| 批次處理 | 120-300 秒 |

### 3. 監控指標

使用 `SOAPService.logServiceStatistics()` 監控：
- 活躍連線數量
- 請求處理時間
- 錯誤率統計

## 故障排除

### 常見問題

1. **連線逾時**
   - 檢查網路連線
   - 增加逾時時間
   - 確認 SOAP 服務狀態

2. **SOAP Fault**
   - 檢查請求格式
   - 驗證服務端點
   - 查看詳細錯誤日誌

3. **XML 解析錯誤**
   - 確認回應格式正確
   - 檢查字元編碼
   - 驗證 XML 結構

### 日誌分析

查看系統日誌中的 SOAP 相關訊息：
- `[SOAPClient]`: 客戶端操作日誌
- `[SOAPService]`: 服務層操作日誌
- `[OLTP]`: 主要處理流程日誌

## 版本資訊

- **版本**: 1.3
- **新增日期**: 2024年
- **相容性**: 與現有 HTTP 功能完全相容
- **依賴**: Java SOAP API (javax.xml.soap)

## 支援

如有問題或建議，請聯繫開發團隊或查看系統日誌獲取詳細錯誤資訊。
