# SOAP HTTP 標頭設定指南

## 概述

本文件說明 SOAP 客戶端中 HTTP 標頭的正確設定，特別是 `SOAPAction` 和 `Content-Type` 標頭的配置。

## 🔧 HTTP 標頭設定

### 必要的 HTTP 標頭

| 標頭名稱 | 值 | 說明 |
|---------|---|------|
| `Content-Type` | `text/xml; charset=utf-8` | 指定內容類型和字符編碼 |
| `SOAPAction` | 動態設定 | 指定要執行的 SOAP 操作 |
| `User-Agent` | `CosmedApi-OLTP-SOAPClient/1.3` | 客戶端識別 |
| `Accept` | `text/xml, application/soap+xml` | 接受的回應格式 |

### SOAPAction 動態設定規則

#### 1. 自動推斷規則
```java
// 根據業務操作自動推斷
if (content.contains("ManageTerminal")) {
    return "http://ticketxpress.com.tw/IPOSProxy/ManageTerminal";
} else if (content.contains("ProcessPayment")) {
    return "http://ticketxpress.com.tw/IPOSProxy/ProcessPayment";
} else if (content.contains("QueryTransaction")) {
    return "http://ticketxpress.com.tw/IPOSProxy/QueryTransaction";
} else if (content.contains("CancelTransaction")) {
    return "http://ticketxpress.com.tw/IPOSProxy/CancelTransaction";
} else {
    // 使用根元素名稱
    return "http://ticketxpress.com.tw/IPOSProxy/" + rootElementName;
}
```

#### 2. 支援的業務操作

| 業務操作 | SOAPAction |
|---------|-----------|
| ManageTerminal | `http://ticketxpress.com.tw/IPOSProxy/ManageTerminal` |
| ProcessPayment | `http://ticketxpress.com.tw/IPOSProxy/ProcessPayment` |
| QueryTransaction | `http://ticketxpress.com.tw/IPOSProxy/QueryTransaction` |
| CancelTransaction | `http://ticketxpress.com.tw/IPOSProxy/CancelTransaction` |
| 其他操作 | `http://ticketxpress.com.tw/IPOSProxy/{RootElementName}` |
| 預設 | `http://ticketxpress.com.tw/IPOSProxy/DefaultAction` |

## 🔄 使用方式

### 1. 自動 SOAPAction 設定

```java
// SimpleSOAPClient
SimpleSOAPClient client = new SimpleSOAPClient(serviceUrl, timeout);
client.setRequestContent(xmlContent); // 自動推斷 SOAPAction

// SOAPClient
SOAPClient client = new SOAPClient(serviceUrl, timeout);
client.setRequestContent(xmlContent); // 自動推斷 SOAPAction
```

### 2. 手動 SOAPAction 設定

```java
// SimpleSOAPClient
SimpleSOAPClient client = new SimpleSOAPClient(serviceUrl, timeout);
client.setRequestContent(xmlContent);
client.setSOAPAction("http://custom.service.com/CustomAction");

// SOAPClient
SOAPClient client = new SOAPClient(serviceUrl, timeout);
client.setRequestContent(xmlContent);
client.setSOAPAction("http://custom.service.com/CustomAction");
```

### 3. 透過 SOAPService 設定

```java
// 使用自動推斷的 SOAPAction
String response = SOAPService.sendSOAPRequest(serviceUrl, timeout, requestContent);

// 使用自訂 SOAPAction
String response = SOAPService.sendSOAPRequest(serviceUrl, timeout, requestContent, 
                                            "http://custom.service.com/CustomAction");
```

## 🧪 Console 輸出範例

### SimpleSOAPClient 輸出
```
=== HTTP Headers Setup (SimpleSOAP) ===
Content-Type: text/xml; charset=utf-8
SOAPAction: http://ticketxpress.com.tw/IPOSProxy/ManageTerminal
User-Agent: CosmedApi-OLTP-SimpleSOAPClient/1.3
======================================
```

### SOAPClient 輸出
```
=== HTTP Headers Setup ===
Content-Type: text/xml; charset=utf-8
SOAPAction: http://ticketxpress.com.tw/IPOSProxy/ManageTerminal
==========================
```

## 📋 Postman 範例對比

### Postman 設定
```
POST http://soap.service.com/endpoint
Content-Type: text/xml
SOAPAction: http://ticketxpress.com.tw/IPOSProxy/ManageTerminal
```

### 我們的實作
```java
// HTTP 標頭設定
connection.setRequestProperty("Content-Type", "text/xml; charset=utf-8");
connection.setRequestProperty("SOAPAction", "http://ticketxpress.com.tw/IPOSProxy/ManageTerminal");
connection.setRequestProperty("User-Agent", "CosmedApi-OLTP-SimpleSOAPClient/1.3");
connection.setRequestProperty("Accept", "text/xml, application/soap+xml");
```

## 🔧 設定驗證

### 1. 檢查 SOAPAction 推斷
```java
// 測試自動推斷
String content = "<ManageTerminal>...</ManageTerminal>";
SimpleSOAPClient client = new SimpleSOAPClient(url, timeout);
client.setRequestContent(content);

// 檢查推斷結果（透過反射或日誌）
```

### 2. 檢查 HTTP 標頭
查看 Console 輸出中的標頭設定：
```
=== HTTP Headers Setup ===
Content-Type: text/xml; charset=utf-8
SOAPAction: http://ticketxpress.com.tw/IPOSProxy/ManageTerminal
```

### 3. 檢查網路請求
使用網路監控工具（如 Wireshark）驗證實際發送的 HTTP 標頭。

## 🧪 測試驗證

### 執行測試
```bash
# 編譯測試
javac -cp "libs/*:bin" test/com/pic/o2o/common/SOAPHTTPHeadersTest.java

# 執行測試
java -cp "libs/*:bin:test" org.junit.runner.JUnitCore com.pic.o2o.common.SOAPHTTPHeadersTest
```

### 測試案例
1. **ManageTerminal SOAPAction 推斷** - 驗證正確推斷
2. **ProcessPayment SOAPAction 推斷** - 驗證不同操作的推斷
3. **預設 SOAPAction** - 驗證未知操作的處理
4. **手動 SOAPAction 設定** - 驗證自訂設定
5. **客戶端一致性** - 驗證兩個客戶端的一致性
6. **OLTP 內容處理** - 驗證 OLTP 格式的 SOAPAction 推斷

## ⚠️ 注意事項

### 1. SOAPAction 格式
- 必須是完整的 URI 格式
- 通常包含服務命名空間和操作名稱
- 某些服務可能要求空的 SOAPAction（`""`）

### 2. Content-Type 設定
- 使用 `text/xml` 而非 `application/soap+xml`（SOAP 1.1 標準）
- 必須指定 `charset=utf-8`
- 與 Postman 範例保持一致

### 3. 錯誤處理
- 如果 SOAPAction 不正確，服務可能返回 HTTP 500 錯誤
- 檢查服務文件以確認正確的 SOAPAction 格式
- 使用日誌記錄實際發送的標頭值

### 4. 服務相容性
- 不同的 SOAP 服務可能有不同的 SOAPAction 要求
- 某些服務忽略 SOAPAction，某些服務嚴格要求
- 建議提供手動設定選項以應對特殊需求

## 🔄 升級指南

### 從舊版本升級
1. **自動推斷**：現有程式碼會自動獲得 SOAPAction 推斷功能
2. **手動設定**：可選擇性地添加手動 SOAPAction 設定
3. **向後相容**：所有現有功能保持不變

### 新功能使用
```java
// 新增：支援自訂 SOAPAction 的 SOAPService 方法
String response = SOAPService.sendSOAPRequest(serviceUrl, timeout, content, customAction);

// 新增：手動設定 SOAPAction
client.setSOAPAction("http://custom.action.com/Operation");
```

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
