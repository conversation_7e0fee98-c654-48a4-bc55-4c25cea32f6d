# SOAP 2001 錯誤修復指南

## 錯誤概述

**錯誤碼**：2001  
**錯誤訊息**：系統連線失敗  
**目標服務**：https://stage-posapi2.tixpress.tw/POSProxyService.svc  
**錯誤類型**：java.net.ConnectException: SOAP 服務連線失敗  

## 🔍 錯誤分析

### 錯誤堆疊追蹤
```
java.net.ConnectException: SOAP 服務連線失敗
	at com.pic.o2o.common.SOAPService.sendSOAPRequest(SOAPService.java:171)
	at com.pic.o2o.common.SOAPService.sendSOAPRequest(SOAPService.java:55)
	at com.pic.oltp.OLTP.SOAP_SubProcess(OLTP.java:738)
```

### 時間分析
- **開始時間**：16:37:20.889
- **失敗時間**：16:37:22.821
- **耗時**：約 2 秒（遠少於 23 秒逾時設定）
- **結論**：快速失敗，可能是連線被立即拒絕

## 🧪 診斷步驟

### 1. 執行自動診斷
```bash
diagnose_2001_error.bat
```

這個腳本會執行：
- HTTPS SOAP 連線診斷
- SSL 憑證檢查
- 網路連線測試
- DNS 解析驗證

### 2. 手動網路測試
```bash
# DNS 解析測試
nslookup stage-posapi2.tixpress.tw

# TCP 連線測試
telnet stage-posapi2.tixpress.tw 443

# Ping 測試
ping stage-posapi2.tixpress.tw

# SSL 憑證檢查
openssl s_client -connect stage-posapi2.tixpress.tw:443 -servername stage-posapi2.tixpress.tw
```

## 🔧 常見原因和解決方案

### 原因 1：SSL 憑證問題 ⭐⭐⭐⭐⭐

#### 症狀
- SSL 握手失敗
- 憑證驗證錯誤
- PKIX path building failed

#### 解決方案

**方案 1：更新 Java 版本**
```bash
# 檢查當前 Java 版本
java -version

# 建議升級到 Java 8u261+ 或 Java 11+
```

**方案 2：添加 CA 憑證到信任庫**
```bash
# 下載憑證
openssl s_client -connect stage-posapi2.tixpress.tw:443 -servername stage-posapi2.tixpress.tw < /dev/null | openssl x509 -outform PEM > tixpress.crt

# 添加到 Java 信任庫
keytool -import -alias tixpress -file tixpress.crt -keystore "%JAVA_HOME%\lib\security\cacerts" -storepass changeit
```

**方案 3：程式碼層面跳過憑證驗證（僅測試用）**
```java
// 在 SOAP_SubProcess 方法開始時添加
SSLCertificateHelper.setupTrustAllCertificates();
```

### 原因 2：網路連線問題 ⭐⭐⭐⭐

#### 症狀
- DNS 解析失敗
- TCP 連線被拒絕
- 網路逾時

#### 解決方案

**方案 1：檢查防火牆設定**
- 確認 443 埠已開放
- 檢查出站規則
- 聯絡網路管理員

**方案 2：檢查代理伺服器設定**
```java
// 設定 HTTP 代理
System.setProperty("https.proxyHost", "proxy.company.com");
System.setProperty("https.proxyPort", "8080");

// 設定代理認證（如需要）
System.setProperty("https.proxyUser", "username");
System.setProperty("https.proxyPassword", "password");
```

**方案 3：檢查 DNS 設定**
- 確認 DNS 伺服器設定正確
- 嘗試使用不同的 DNS 伺服器
- 檢查 hosts 檔案

### 原因 3：服務端點問題 ⭐⭐⭐

#### 症狀
- HTTP 錯誤回應（4xx, 5xx）
- 服務不可用
- 錯誤的端點 URL

#### 解決方案

**方案 1：驗證服務端點**
```bash
# 使用瀏覽器訪問
https://stage-posapi2.tixpress.tw/POSProxyService.svc

# 檢查 WSDL
https://stage-posapi2.tixpress.tw/POSProxyService.svc?wsdl
```

**方案 2：檢查 transfile.txt 設定**
```
# 確認 URL 正確
MANAGE_TERMINAL,SOAP_SubProcess,https://stage-posapi2.tixpress.tw/POSProxyService.svc,23,N
```

**方案 3：聯絡服務提供者**
- 確認服務狀態
- 檢查認證資訊
- 確認 IP 白名單

### 原因 4：SOAP 請求格式問題 ⭐⭐

#### 症狀
- HTTP 400 Bad Request
- HTTP 500 Internal Server Error
- SOAP Fault 回應

#### 解決方案

**方案 1：檢查 SOAP 請求格式**
```xml
<!-- 確認 SOAP Envelope 格式正確 -->
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/">
    <SOAP-ENV:Body>
        <m:ManageTerminal xmlns:m="http://ticketxpress.com.tw/">
            <!-- 業務內容 -->
        </m:ManageTerminal>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>
```

**方案 2：檢查 HTTP 標頭**
```java
// 確認必要的 HTTP 標頭
Content-Type: text/xml; charset=utf-8
SOAPAction: http://ticketxpress.com.tw/IPOSProxy/ManageTerminal
```

## 🛠️ 修復實作

### 1. 臨時修復（跳過 SSL 驗證）

在 `OLTP.java` 的 `SOAP_SubProcess` 方法中添加：

```java
private void SOAP_SubProcess(Element root, Boolean[] result, StringBuffer errorMsg, StringBuffer xmlfileName,
        String target, int timeout, String toCPStr) {
    
    try {
        // 臨時解決方案：跳過 SSL 憑證驗證（僅用於測試）
        if (target.startsWith("https://")) {
            SSLCertificateHelper.setupTrustAllCertificates();
            System.out.println("⚠️ 已啟用 SSL 憑證跳過模式（僅用於測試）");
        }
        
        // 原有的 SOAP 請求邏輯
        SOAPService.setDefaultSettings(GlobalVariable.HOST, GlobalVariable.logTrans);
        String responseStr = SOAPService.sendSOAPRequest(target, timeout, toCPStr);
        
        // ... 其餘邏輯保持不變
        
    } catch (Exception e) {
        // 錯誤處理
    }
}
```

### 2. 永久修復（添加憑證）

**步驟 1：下載憑證**
```bash
# 執行診斷工具獲取憑證資訊
java -cp "libs/*;bin" com.pic.o2o.common.SSLCertificateHelper
```

**步驟 2：添加憑證到信任庫**
```bash
# 使用管理員權限執行
keytool -import -alias tixpress-stage -file tixpress.crt -keystore "%JAVA_HOME%\lib\security\cacerts" -storepass changeit
```

**步驟 3：重啟應用程式**

### 3. 網路修復

**檢查代理設定**
```java
// 在應用程式啟動時設定
if (needProxy) {
    System.setProperty("https.proxyHost", "your.proxy.server");
    System.setProperty("https.proxyPort", "8080");
}
```

## 📋 驗證檢查清單

### SSL 憑證檢查
- [ ] Java 版本是否為最新
- [ ] 系統時間是否正確
- [ ] 憑證是否已添加到信任庫
- [ ] 憑證是否未過期

### 網路連線檢查
- [ ] DNS 解析是否正常
- [ ] TCP 443 埠是否可達
- [ ] 防火牆是否允許連線
- [ ] 代理設定是否正確

### 服務端點檢查
- [ ] URL 是否正確
- [ ] 服務是否正常運作
- [ ] 認證資訊是否正確
- [ ] IP 是否在白名單中

### SOAP 請求檢查
- [ ] SOAP Envelope 格式是否正確
- [ ] HTTP 標頭是否完整
- [ ] SOAPAction 是否正確
- [ ] 業務內容是否有效

## 🔄 監控和預防

### 1. 添加連線監控
```java
// 在 SOAP_SubProcess 中添加詳細日誌
System.out.println("=== SOAP 連線監控 ===");
System.out.println("目標: " + target);
System.out.println("逾時: " + timeout);
System.out.println("開始時間: " + new Date());

// 執行連線前的預檢
HTTPSSOAPDiagnostic.DiagnosticResult preCheck = 
    HTTPSSOAPDiagnostic.diagnoseHTTPS(target, timeout);

if (!preCheck.isSuccess()) {
    System.out.println("⚠️ 預檢失敗: " + preCheck.getSummary());
}
```

### 2. 設定重試機制
```java
// 添加重試邏輯
int maxRetries = 3;
for (int i = 0; i < maxRetries; i++) {
    try {
        String responseStr = SOAPService.sendSOAPRequest(target, timeout, toCPStr);
        // 成功則跳出
        break;
    } catch (ConnectException e) {
        if (i == maxRetries - 1) {
            throw e; // 最後一次重試失敗
        }
        System.out.println("重試 " + (i + 1) + "/" + maxRetries);
        Thread.sleep(1000); // 等待 1 秒後重試
    }
}
```

## 📞 技術支援

如果問題持續存在，請收集以下資訊並聯絡技術支援：

1. **診斷報告**：`diagnose_2001_error.bat` 的完整輸出
2. **錯誤日誌**：完整的堆疊追蹤
3. **網路環境**：防火牆、代理設定
4. **Java 環境**：版本、信任庫資訊
5. **服務資訊**：URL、認證方式

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
