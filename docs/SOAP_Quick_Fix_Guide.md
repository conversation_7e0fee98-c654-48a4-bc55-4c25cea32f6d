# SOAP ClassNotFoundException 快速修復指南

## 🚨 問題描述

當測試 SOAP 功能時出現以下錯誤：
```
java.lang.NoClassDefFoundError: org/jvnet/staxex/util/XMLStreamReaderToXMLStreamWriter$Breakpoint
```

## ⚡ 立即解決方案

### 方案一：添加缺少的依賴項（推薦）

**步驟 1：下載必要的 JAR 檔案**

請下載以下 JAR 檔案並放入 `libs` 目錄：

```
必要檔案清單：
├── javax.xml.soap-api-1.4.0.jar
├── saaj-impl-1.5.1.jar
├── stax-ex-1.8.jar              ← 關鍵檔案
├── streambuffer-1.5.7.jar       ← 關鍵檔案
├── woodstox-core-6.2.4.jar      ← 關鍵檔案
├── stax2-api-4.2.1.jar          ← 關鍵檔案
├── activation-1.1.1.jar
├── jaxb-api-2.3.1.jar
├── jaxb-core-2.3.0.1.jar
└── jaxb-impl-2.3.1.jar
```

**步驟 2：驗證檔案**

執行以下命令驗證關鍵檔案：
```bash
# 檢查 stax-ex JAR 是否包含必要的類別
jar -tf libs/stax-ex-1.8.jar | grep XMLStreamReaderToXMLStreamWriter

# 應該看到類似輸出：
# org/jvnet/staxex/util/XMLStreamReaderToXMLStreamWriter.class
# org/jvnet/staxex/util/XMLStreamReaderToXMLStreamWriter$Breakpoint.class
```

**步驟 3：重新啟動應用程式**

### 方案二：使用簡化版 SOAP 客戶端（備用方案）

如果無法取得所有依賴項，系統已自動實作備用方案：

1. **SimpleSOAPClient** 會自動啟用
2. 使用 HTTP POST 方式發送 SOAP 請求
3. 避免複雜的 SOAP API 依賴

**無需額外配置，系統會自動切換！**

## 📋 下載連結

### 關鍵依賴項下載

| 檔案名稱 | 下載連結 |
|----------|----------|
| `stax-ex-1.8.jar` | [Maven Central](https://repo1.maven.org/maven2/org/jvnet/staxex/stax-ex/1.8/stax-ex-1.8.jar) |
| `streambuffer-1.5.7.jar` | [Maven Central](https://repo1.maven.org/maven2/com/sun/xml/stream/buffer/streambuffer/1.5.7/streambuffer-1.5.7.jar) |
| `woodstox-core-6.2.4.jar` | [Maven Central](https://repo1.maven.org/maven2/com/fasterxml/woodstox/woodstox-core/6.2.4/woodstox-core-6.2.4.jar) |
| `stax2-api-4.2.1.jar` | [Maven Central](https://repo1.maven.org/maven2/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar) |

### 基礎 SOAP 依賴項

| 檔案名稱 | 下載連結 |
|----------|----------|
| `javax.xml.soap-api-1.4.0.jar` | [Maven Central](https://repo1.maven.org/maven2/javax/xml/soap/javax.xml.soap-api/1.4.0/javax.xml.soap-api-1.4.0.jar) |
| `saaj-impl-1.5.1.jar` | [Maven Central](https://repo1.maven.org/maven2/com/sun/xml/messaging/saaj/saaj-impl/1.5.1/saaj-impl-1.5.1.jar) |

## 🔧 驗證修復

### 測試步驟

1. **檢查日誌**
   ```
   查看日誌中是否出現：
   [SOAPService] Starting SOAP request to: [URL]
   ```

2. **測試 SOAP 請求**
   ```
   在 transfile.txt 中添加測試配置：
   TEST_SOAP,SOAP_SubProcess,http://your-soap-service/endpoint,30,N
   ```

3. **確認回應**
   ```
   成功的日誌應該顯示：
   [SOAPService] SOAP request completed successfully
   ```

### 備用方案確認

如果看到以下日誌，表示系統已切換到備用方案：
```
[SOAPService] Falling back to SimpleSOAPClient due to dependency issue
[SimpleSOAPClient] Simple SOAP Request to: [URL]
```

## 🛠️ 進階故障排除

### 檢查 Classpath

確認所有 JAR 檔案都在 classpath 中：
```bash
# 檢查 Java 程序的 classpath
jps -v | grep [您的程序名稱]
```

### 檢查 JAR 檔案完整性

```bash
# 驗證 JAR 檔案沒有損壞
jar -tf libs/stax-ex-1.8.jar > /dev/null && echo "OK" || echo "Corrupted"
```

### 版本相容性檢查

確保所有依賴項版本相互兼容：
- Java 8+ 
- SAAJ 1.5.x
- StAX-ex 1.8.x
- Woodstox 6.2.x

## 📞 支援

如果問題仍然存在：

1. **檢查完整錯誤堆疊**
2. **確認所有依賴項都已正確安裝**
3. **查看系統日誌中的詳細錯誤訊息**
4. **嘗試使用備用的 SimpleSOAPClient**

## ✅ 成功指標

修復成功後，您應該看到：
- ✅ 沒有 ClassNotFoundException 錯誤
- ✅ SOAP 請求成功發送
- ✅ 收到 SOAP 服務回應
- ✅ 日誌顯示正常的處理流程

---

**注意**：SimpleSOAPClient 提供與標準 SOAP 客戶端相同的功能，但使用更簡單的實作方式，避免複雜的依賴問題。
