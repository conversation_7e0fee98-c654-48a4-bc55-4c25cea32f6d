# SOAP 格式轉換指南

## 概述

本文件說明 SOAP_SubProcess 功能的兩個主要改進：
1. **跳過加解密處理**：SOAP 請求不進行 AP 層加密
2. **標準 SOAP Envelope 格式**：自動轉換為標準 SOAP 格式

## 🔄 格式轉換範例

### Before（原始 XML 格式）
```xml
<ManageTerminal>
    <manageTerminalRequest>
        <Channel>Test</Channel>
        <Checksum>B43DB40CD926E971464816D781CFF69B</Checksum>
        <ManageTerminalDateTime>20151015105959</ManageTerminalDateTime>
        <ManageType>101</ManageType>
        <MerchantCode>000000000000038</MerchantCode>
        <ProgramCode>00001</ProgramCode>
        <ShopCode>0000001028</ShopCode>
        <TerminalCode></TerminalCode>
        <TerminalSSN>20151015105959000001</TerminalSSN>
    </manageTerminalRequest>
</ManageTerminal>
```

### After（標準 SOAP Envelope 格式）
```xml
<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" 
                   xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                   xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
                   xmlns:m0="http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common">
    <SOAP-ENV:Body>
        <m:ManageTerminal xmlns:m="http://ticketxpress.com.tw/">
            <m:manageTerminalRequest>
                <m0:Channel>Test</m0:Channel>
                <m0:Checksum>B43DB40CD926E971464816D781CFF69B</m0:Checksum>
                <m0:ManageTerminalDateTime>20151015105959</m0:ManageTerminalDateTime>
                <m0:ManageType>101</m0:ManageType>
                <m0:MerchantCode>000000000000038</m0:MerchantCode>
                <m0:ProgramCode>00001</m0:ProgramCode>
                <m0:ShopCode>0000001028</m0:ShopCode>
                <m0:TerminalCode></m0:TerminalCode>
                <m0:TerminalSSN>20151015105959000001</m0:TerminalSSN>
            </m:manageTerminalRequest>
        </m:ManageTerminal>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>
```

## 🛠️ 轉換規則

### 1. SOAP Envelope 結構
- **根元素**：`<SOAP-ENV:Envelope>`
- **命名空間**：
  - `xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/"`
  - `xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/"`
  - `xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"`
  - `xmlns:xsd="http://www.w3.org/2001/XMLSchema"`
  - `xmlns:m0="http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common"`

### 2. Body 結構轉換
- **原始根元素** → **`<m:RootElement xmlns:m="http://ticketxpress.com.tw/">`**
- **內部元素** → **添加 `m0:` 前綴**

### 3. 轉換邏輯
1. 移除 XML 宣告（如果存在）
2. 提取根元素名稱
3. 將根元素包裝在 `m:` 命名空間中
4. 為所有內部元素添加 `m0:` 前綴
5. 包裝在標準 SOAP Envelope 中

## 📁 檔案修改摘要

### 1. OLTP.java 修改
- ✅ 添加 `writeXmlAsFileWithoutEncryption()` 方法
- ✅ 修改 `SOAP_SubProcess()` 使用不加密寫檔
- ✅ 更新錯誤處理使用不加密寫檔

### 2. SimpleSOAPClient.java 修改
- ✅ 更新 `createSOAPEnvelope()` 方法
- ✅ 添加 `transformToSOAPFormat()` 方法
- ✅ 添加 XML 解析和轉換工具方法

### 3. 新增測試檔案
- ✅ `SimpleSOAPClientTest.java` - 完整的單元測試

## 🧪 測試驗證

### 執行單元測試
```bash
# 編譯測試
javac -cp "libs/*:bin" test/com/pic/o2o/common/SimpleSOAPClientTest.java

# 執行測試
java -cp "libs/*:bin:test" org.junit.runner.JUnitCore com.pic.o2o.common.SimpleSOAPClientTest
```

### 測試案例
1. **完整 XML 轉換測試** - 驗證 ManageTerminal 格式轉換
2. **空內容處理測試** - 驗證空請求的處理
3. **純文字內容測試** - 驗證非 XML 內容的包裝
4. **根元素提取測試** - 驗證 XML 解析功能
5. **內部元素轉換測試** - 驗證命名空間前綴添加

## 🔍 日誌和除錯

### SOAP 專用日誌檔案
- **檔案命名格式**：`{timestamp}_{termino}_SOAP.txt`
- **內容**：原始 XML（未加密）+ SOAP 回應（未加密）
- **標識**：`POS IN XML (SOAP - No Encryption)` 和 `REPLY SOAP XML (No Encryption)`

### 除錯資訊
查看日誌中的以下訊息：
```
[SimpleSOAPClient] Simple SOAP Request to: [URL]
[SimpleSOAPClient] Simple SOAP Response received successfully
[OLTP] SOAP 請求使用不加密的寫檔方法
```

## ⚙️ 配置範例

### transfile.txt 配置
```
# SOAP 服務配置範例
MANAGE_TERMINAL,SOAP_SubProcess,http://soap.service.com/ManageTerminal,60,N
PAYMENT_PROCESS,SOAP_SubProcess,https://payment.soap.com/ProcessPayment,120,N
```

## 🚨 注意事項

### 1. 加密處理
- **HTTP_SubProcess**：使用加密寫檔（`writeXmlAsFile`）
- **SOAP_SubProcess**：使用不加密寫檔（`writeXmlAsFileWithoutEncryption`）

### 2. 命名空間
- **m 命名空間**：`http://ticketxpress.com.tw/` - 用於根元素
- **m0 命名空間**：`http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common` - 用於內部元素

### 3. 錯誤處理
- 轉換失敗時會記錄錯誤並使用預設包裝
- 保持與 HTTP_SubProcess 相同的錯誤碼和處理邏輯

## 📋 檢查清單

部署前請確認：
- [ ] 所有 SOAP 依賴項已安裝
- [ ] 單元測試通過
- [ ] 日誌檔案正確生成（包含 `_SOAP.txt` 後綴）
- [ ] SOAP 請求格式符合期望
- [ ] 錯誤處理正常運作
- [ ] 不加密寫檔功能正常

## 🔧 故障排除

### 常見問題
1. **格式轉換失敗**
   - 檢查原始 XML 格式是否正確
   - 查看日誌中的轉換錯誤訊息

2. **命名空間問題**
   - 確認 SOAP 服務期望的命名空間
   - 必要時調整 `transformToSOAPFormat` 方法

3. **檔案寫入問題**
   - 確認 `XML_LOG_PATH` 配置正確
   - 檢查檔案權限

---

**版本**：1.3  
**更新日期**：2024年  
**相容性**：與現有 HTTP_SubProcess 完全相容
