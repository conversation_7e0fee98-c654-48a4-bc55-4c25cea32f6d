# SOAPAction 動態配置指南

## 概述

本文件說明如何從 `transfile.txt` 配置檔案中動態讀取 SOAPAction，解決 SOAP 連線中 "SOAPAction 標頭為空" 的問題。

## 🎯 問題背景

### 原始問題
- **錯誤**：SOAP 連線失敗，SOAPAction 標頭為空
- **原因**：SOAPAction 在程式碼中寫死，無法靈活配置
- **影響**：無法支援不同的 SOAP 服務和操作

### 解決方案
- **動態配置**：從 `transfile.txt` 讀取 SOAPAction
- **向後相容**：支援舊格式配置檔案
- **智能推斷**：當配置為空時自動推斷合適的 SOAPAction

## 📋 配置檔案格式

### 新格式（推薦）
```
tocode,subProcess,target,timeout,maintainStat,maintainStart,maintainEnd,soapaction
```

### 欄位說明
| 欄位 | 位置 | 說明 | 範例 |
|------|------|------|------|
| `tocode` | 1 | 交易代碼 | `MANAGE_TERMINAL` |
| `subProcess` | 2 | 子程序名稱 | `SOAP_SubProcess` |
| `target` | 3 | 目標 URL | `https://stage-posapi2.tixpress.tw/POSProxyService.svc` |
| `timeout` | 4 | 逾時時間（秒） | `30` |
| `maintainStat` | 5 | 維護狀態 | `0` (正常) / `1` (維護中) |
| `maintainStart` | 6 | 維護開始時間 | `202412250200` |
| `maintainEnd` | 7 | 維護結束時間 | `202412250400` |
| `soapaction` | 8 | **SOAPAction 標頭值** | `http://ticketxpress.com.tw/IPOSProxy/ManageTerminal` |

## 🔧 配置範例

### 完整配置範例
```
# SOAP 服務配置
MANAGE_TERMINAL,SOAP_SubProcess,https://stage-posapi2.tixpress.tw/POSProxyService.svc,30,0,,,http://ticketxpress.com.tw/IPOSProxy/ManageTerminal
PROCESS_PAYMENT,SOAP_SubProcess,https://stage-posapi2.tixpress.tw/POSProxyService.svc,30,0,,,http://ticketxpress.com.tw/IPOSProxy/ProcessPayment
QUERY_TRANSACTION,SOAP_SubProcess,https://stage-posapi2.tixpress.tw/POSProxyService.svc,30,0,,,http://ticketxpress.com.tw/IPOSProxy/QueryTransaction
CANCEL_TRANSACTION,SOAP_SubProcess,https://stage-posapi2.tixpress.tw/POSProxyService.svc,30,0,,,http://ticketxpress.com.tw/IPOSProxy/CancelTransaction
```

### 帶維護時間的配置
```
# 維護期間的配置
MANAGE_TERMINAL_MAINT,SOAP_SubProcess,https://stage-posapi2.tixpress.tw/POSProxyService.svc,30,1,202412250200,202412250400,http://ticketxpress.com.tw/IPOSProxy/ManageTerminal
```

### 向後相容配置
```
# 舊格式（SOAPAction 會自動推斷）
OLD_FORMAT_SOAP,SOAP_SubProcess,https://legacy.soap.service.com/endpoint,25,0

# HTTP 服務（不需要 SOAPAction）
HTTP_SERVICE,HTTP_SubProcess,http://api.example.com/service,15,0
```

## 🔍 SOAPAction 推斷邏輯

### 推斷規則

#### 1. 基於 URL 的推斷
```java
if (target.contains("POSProxyService")) {
    // 針對 POSProxyService 的特殊處理
    if (content.contains("ManageTerminal")) {
        return "http://ticketxpress.com.tw/IPOSProxy/ManageTerminal";
    } else if (content.contains("ProcessPayment")) {
        return "http://ticketxpress.com.tw/IPOSProxy/ProcessPayment";
    }
    // ... 其他操作
}
```

#### 2. 基於內容的推斷
```java
// 從 XML 內容中提取根元素
String rootElement = extractRootElementName(content);
if (rootElement != null) {
    return "http://tempuri.org/" + rootElement;
}
```

#### 3. 預設值
```java
// 最終預設值
return "http://tempuri.org/ProcessRequest";
```

### 推斷優先級
1. **配置檔案中的 SOAPAction**（最高優先級）
2. **基於 URL 和內容的智能推斷**
3. **通用預設值**（最低優先級）

## 🛠️ 實作細節

### 1. transfile.txt 解析
```java
// 新的解析邏輯
String[] tocodeInfo = info.split(",");
String soapAction = null;

if (tocodeInfo.length >= 8) {
    // 新格式：包含 SOAPAction
    soapAction = tocodeInfo[7];
} else {
    // 舊格式：使用預設值
    soapAction = null;
}
```

### 2. SOAP_SubProcess 方法簽名
```java
// 修改前
private void SOAP_SubProcess(Element root, Boolean[] result, StringBuffer errorMsg, 
                           StringBuffer xmlfileName, String target, int timeout, String toCPStr)

// 修改後
private void SOAP_SubProcess(Element root, Boolean[] result, StringBuffer errorMsg, 
                           StringBuffer xmlfileName, String target, int timeout, String toCPStr, String soapAction)
```

### 3. SOAPAction 處理流程
```java
// SOAPAction 處理邏輯
String finalSoapAction = soapAction;
if (finalSoapAction == null || finalSoapAction.trim().isEmpty()) {
    // 自動推斷
    finalSoapAction = inferDefaultSOAPAction(target, toCPStr);
    System.out.println("使用推斷的 SOAPAction: " + finalSoapAction);
} else {
    System.out.println("使用配置的 SOAPAction: " + finalSoapAction);
}

// 傳遞給 SOAPService
String responseStr = SOAPService.sendSOAPRequest(target, timeout, toCPStr, finalSoapAction);
```

## 📋 遷移檢查清單

### 程式碼修改
- [ ] ✅ 修改 `GlobalVariable.java` 的 transfile.txt 解析邏輯
- [ ] ✅ 修改 `OLTP.java` 的配置解析，支援第8個欄位
- [ ] ✅ 修改 `SOAP_SubProcess` 方法簽名，添加 SOAPAction 參數
- [ ] ✅ 實作 `inferDefaultSOAPAction` 推斷方法
- [ ] ✅ 修改 SOAPService 呼叫，傳遞 SOAPAction

### 配置檔案更新
- [ ] 備份現有的 `transfile.txt`
- [ ] 更新配置格式，添加 SOAPAction 欄位
- [ ] 測試新配置是否正確解析
- [ ] 驗證向後相容性

### 測試驗證
- [ ] 編譯所有修改的程式碼
- [ ] 執行 `test_soapaction_config.bat` 驗證功能
- [ ] 測試 SOAP 連線是否正常
- [ ] 檢查日誌中的 SOAPAction 值

## 🧪 測試步驟

### 1. 執行自動測試
```bash
test_soapaction_config.bat
```

### 2. 手動測試配置
```bash
# 檢查配置解析
echo MANAGE_TERMINAL,SOAP_SubProcess,https://stage-posapi2.tixpress.tw/POSProxyService.svc,30,0,,,http://ticketxpress.com.tw/IPOSProxy/ManageTerminal > test_transfile.txt

# 測試程式碼
java -cp "libs/*;bin" com.pic.oltp.OLTP
```

### 3. 驗證 SOAPAction 設定
檢查 Console 輸出中的診斷資訊：
```
=== SOAP_SubProcess 診斷資訊 ===
目標 URL: https://stage-posapi2.tixpress.tw/POSProxyService.svc
逾時設定: 30 秒
SOAPAction: http://ticketxpress.com.tw/IPOSProxy/ManageTerminal
使用配置的 SOAPAction: http://ticketxpress.com.tw/IPOSProxy/ManageTerminal
正在發送 SOAP 請求到: https://stage-posapi2.tixpress.tw/POSProxyService.svc
使用 SOAPAction: http://ticketxpress.com.tw/IPOSProxy/ManageTerminal
```

## 🔧 常見問題和解決方案

### 問題 1：配置檔案格式錯誤
**症狀**：解析失敗或 SOAPAction 為空
**解決**：
- 檢查逗號分隔符是否正確
- 確認欄位數量是否足夠
- 檢查是否有多餘的空格或特殊字符

### 問題 2：SOAPAction 仍然為空
**症狀**：即使配置了 SOAPAction，仍然顯示為空
**解決**：
- 檢查配置檔案是否正確載入
- 確認 tocode 是否匹配
- 檢查陣列索引是否正確（第8個欄位，索引為7）

### 問題 3：向後相容性問題
**症狀**：舊格式配置無法正常工作
**解決**：
- 確認舊格式配置的欄位數量檢查邏輯
- 檢查預設值推斷是否正常工作
- 驗證錯誤處理邏輯

## 📊 效果監控

### 成功指標
- ✅ SOAP 連線成功率提升
- ✅ "SOAPAction 標頭為空" 錯誤消失
- ✅ 不同 SOAP 服務可以使用不同的 SOAPAction
- ✅ 配置變更無需重新編譯程式碼

### 監控方法
```java
// 在日誌中記錄 SOAPAction 使用情況
log.append("SOAPAction: ").append(finalSoapAction)
   .append(" (source: ").append(soapAction != null ? "config" : "inferred").append(")")
   .append("\r\n");
```

## 🔄 未來擴展

### 1. 支援多個 SOAPAction
```
# 未來可能的格式
MULTI_ACTION,SOAP_SubProcess,https://service.com/soap,30,0,,,action1;action2;action3
```

### 2. 條件式 SOAPAction
```
# 基於請求內容的條件式配置
CONDITIONAL_ACTION,SOAP_SubProcess,https://service.com/soap,30,0,,,ManageTerminal:action1,ProcessPayment:action2
```

### 3. SOAPAction 模板
```
# 使用變數的 SOAPAction 模板
TEMPLATE_ACTION,SOAP_SubProcess,https://service.com/soap,30,0,,,http://service.com/{operation}
```

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
