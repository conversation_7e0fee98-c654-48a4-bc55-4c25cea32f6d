# SOAP 功能所需依賴項

## 必要 JAR 檔案

以下是實作 SOAP 功能所需的 JAR 檔案清單。請將這些檔案添加到專案的 `libs` 目錄中。

### 核心 SOAP 依賴

| 檔案名稱 | 版本 | 用途 | 下載連結 |
|----------|------|------|----------|
| `javax.xml.soap-api-1.4.0.jar` | 1.4.0 | SOAP API | [Maven Central](https://mvnrepository.com/artifact/javax.xml.soap/javax.xml.soap-api/1.4.0) |
| `saaj-impl-1.5.1.jar` | 1.5.1 | SOAP API 實作 | [Maven Central](https://mvnrepository.com/artifact/com.sun.xml.messaging.saaj/saaj-impl/1.5.1) |
| `activation-1.1.1.jar` | 1.1.1 | JavaBeans Activation Framework | [Maven Central](https://mvnrepository.com/artifact/javax.activation/activation/1.1.1) |
| `jaxb-api-2.3.1.jar` | 2.3.1 | JAXB API | [Maven Central](https://mvnrepository.com/artifact/javax.xml.bind/jaxb-api/2.3.1) |
| `jaxb-core-2.3.0.1.jar` | 2.3.0.1 | JAXB 核心 | [Maven Central](https://mvnrepository.com/artifact/org.glassfish.jaxb/jaxb-core/2.3.0.1) |
| `jaxb-impl-2.3.1.jar` | 2.3.1 | JAXB 實作 | [Maven Central](https://mvnrepository.com/artifact/com.sun.xml.bind/jaxb-impl/2.3.1) |

### 🚨 必要的 StAX 依賴項（解決 ClassNotFoundException）

| 檔案名稱 | 版本 | 用途 | 下載連結 |
|----------|------|------|----------|
| `stax-ex-1.8.jar` | 1.8 | StAX API 擴展 | [Maven Central](https://mvnrepository.com/artifact/org.jvnet.staxex/stax-ex/1.8) |
| `streambuffer-1.5.7.jar` | 1.5.7 | Stream Buffer 實作 | [Maven Central](https://mvnrepository.com/artifact/com.sun.xml.stream.buffer/streambuffer/1.5.7) |
| `woodstox-core-6.2.4.jar` | 6.2.4 | Woodstox StAX 實作 | [Maven Central](https://mvnrepository.com/artifact/com.fasterxml.woodstox/woodstox-core/6.2.4) |
| `stax2-api-4.2.1.jar` | 4.2.1 | StAX2 API | [Maven Central](https://mvnrepository.com/artifact/org.codehaus.woodstox/stax2-api/4.2.1) |

### SOAP 進階功能依賴

| 檔案名稱 | 版本 | 用途 | 下載連結 |
|----------|------|------|----------|
| `FastInfoset-1.2.16.jar` | 1.2.16 | Fast Infoset 實作 | [Maven Central](https://mvnrepository.com/artifact/com.sun.xml.fastinfoset/FastInfoset/1.2.16) |
| `mimepull-1.9.11.jar` | 1.9.11 | MIME 處理 | [Maven Central](https://mvnrepository.com/artifact/org.jvnet.mimepull/mimepull/1.9.11) |

### 測試依賴

| 檔案名稱 | 版本 | 用途 | 下載連結 |
|----------|------|------|----------|
| `junit-4.13.1.jar` | 4.13.1 | 單元測試框架 | [Maven Central](https://mvnrepository.com/artifact/junit/junit/4.13.1) |
| `mockito-core-3.6.0.jar` | 3.6.0 | 模擬測試框架 | [Maven Central](https://mvnrepository.com/artifact/org.mockito/mockito-core/3.6.0) |
| `mockito-inline-3.6.0.jar` | 3.6.0 | 靜態方法模擬 | [Maven Central](https://mvnrepository.com/artifact/org.mockito/mockito-inline/3.6.0) |
| `byte-buddy-1.10.18.jar` | 1.10.18 | Mockito 依賴 | [Maven Central](https://mvnrepository.com/artifact/net.bytebuddy/byte-buddy/1.10.18) |
| `byte-buddy-agent-1.10.18.jar` | 1.10.18 | Mockito 依賴 | [Maven Central](https://mvnrepository.com/artifact/net.bytebuddy/byte-buddy-agent/1.10.18) |
| `objenesis-3.1.jar` | 3.1 | Mockito 依賴 | [Maven Central](https://mvnrepository.com/artifact/org.objenesis/objenesis/3.1) |

## 安裝步驟

1. 下載上述所有 JAR 檔案
2. 將檔案複製到專案的 `libs` 目錄
3. 更新專案的 classpath 設定

## Maven 依賴配置 (參考用)

如果您使用 Maven 管理依賴，可以參考以下配置：

```xml
<!-- SOAP API -->
<dependency>
    <groupId>javax.xml.soap</groupId>
    <artifactId>javax.xml.soap-api</artifactId>
    <version>1.4.0</version>
</dependency>

<!-- SOAP 實作 -->
<dependency>
    <groupId>com.sun.xml.messaging.saaj</groupId>
    <artifactId>saaj-impl</artifactId>
    <version>1.5.1</version>
</dependency>

<!-- JavaBeans Activation Framework -->
<dependency>
    <groupId>javax.activation</groupId>
    <artifactId>activation</artifactId>
    <version>1.1.1</version>
</dependency>

<!-- JAXB API -->
<dependency>
    <groupId>javax.xml.bind</groupId>
    <artifactId>jaxb-api</artifactId>
    <version>2.3.1</version>
</dependency>

<!-- JAXB 核心 -->
<dependency>
    <groupId>org.glassfish.jaxb</groupId>
    <artifactId>jaxb-core</artifactId>
    <version>2.3.0.1</version>
</dependency>

<!-- JAXB 實作 -->
<dependency>
    <groupId>com.sun.xml.bind</groupId>
    <artifactId>jaxb-impl</artifactId>
    <version>2.3.1</version>
</dependency>

<!-- StAX API 擴展 -->
<dependency>
    <groupId>org.jvnet.staxex</groupId>
    <artifactId>stax-ex</artifactId>
    <version>1.8</version>
</dependency>

<!-- Fast Infoset 實作 -->
<dependency>
    <groupId>com.sun.xml.fastinfoset</groupId>
    <artifactId>FastInfoset</artifactId>
    <version>1.2.16</version>
</dependency>

<!-- MIME 處理 -->
<dependency>
    <groupId>org.jvnet.mimepull</groupId>
    <artifactId>mimepull</artifactId>
    <version>1.9.11</version>
</dependency>
```

## 相容性注意事項

- 這些依賴項與 Java 8 及更高版本相容
- 如果使用 Java 9 或更高版本，可能需要添加額外的模組宣告
- 確保所有依賴項版本相互兼容，避免版本衝突

## 🚨 緊急修復：ClassNotFoundException 解決方案

### 問題：`NoClassDefFoundError: org/jvnet/staxex/util/XMLStreamReaderToXMLStreamWriter$Breakpoint`

**立即解決步驟：**

1. **下載並添加以下 JAR 檔案到 libs 目錄：**
   ```
   stax-ex-1.8.jar
   streambuffer-1.5.7.jar
   woodstox-core-6.2.4.jar
   stax2-api-4.2.1.jar
   ```

2. **驗證 JAR 檔案完整性：**
   ```bash
   # 檢查 JAR 檔案是否包含必要的類別
   jar -tf libs/stax-ex-1.8.jar | grep XMLStreamReaderToXMLStreamWriter
   ```

3. **重新啟動應用程式**

### 問題：其他 SOAP 相關 ClassNotFoundException

**檢查清單：**
- [ ] `javax.xml.soap-api-1.4.0.jar` - SOAP API
- [ ] `saaj-impl-1.5.1.jar` - SOAP 實作
- [ ] `stax-ex-1.8.jar` - StAX 擴展
- [ ] `streambuffer-1.5.7.jar` - Stream Buffer
- [ ] `woodstox-core-6.2.4.jar` - StAX 實作
- [ ] `stax2-api-4.2.1.jar` - StAX2 API

## 故障排除

如果遇到 `ClassNotFoundException` 或 `NoClassDefFoundError`，請檢查：

1. 所有必要的 JAR 檔案是否已添加到 classpath
2. JAR 檔案版本是否相互兼容
3. 是否有 JAR 檔案損壞或不完整
4. **特別檢查 StAX 相關依賴項是否完整**

如果遇到 `UnsupportedClassVersionError`，表示 JAR 檔案與您的 Java 版本不相容，請使用相容的版本。
