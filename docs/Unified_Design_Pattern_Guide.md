# 統一設計模式指南

## 概述

本文件說明如何統一 HTTP 和 SOAP 服務的設計模式，消除對 GlobalVariable 的直接依賴，採用依賴注入模式。

## 🎯 設計目標

### 問題分析
**HTTP 服務（正確模式）**：
- ✅ 使用依賴注入：參數透過建構函式傳入
- ✅ 無直接依賴：不直接 import GlobalVariable
- ✅ 可測試性：容易進行單元測試
- ✅ 靈活性：可在不同環境使用不同配置

**SOAP 服務（原有問題）**：
- ❌ 直接依賴：直接使用 GlobalVariable.HOST
- ❌ 緊耦合：無法在不同環境使用
- ❌ 難以測試：依賴全域狀態
- ❌ 不一致：與 HTTP 服務設計不一致

## ✅ 統一解決方案

### 1. SOAPService 依賴注入改造

#### Before（直接依賴）：
```java
// 直接使用 GlobalVariable
log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
   .append("\t").append(GlobalVariable.HOST)  // 直接依賴
   .append("\t[").append(SOAPService.class.getSimpleName())
   .append("]\tSOAP request to: ").append(serviceUrl)
   .append("\r\n");
Utility.writeLog(GlobalVariable.logTrans, log);  // 直接依賴
```

#### After（依賴注入）：
```java
// 使用預設設定（可配置）
private static String defaultHost = "UNKNOWN_HOST";
private static String defaultLogTrans = "TRANSLOG";

// 設定方法（依賴注入）
public static void setDefaultSettings(String host, String logTrans) {
    defaultHost = host;
    defaultLogTrans = logTrans;
}

// 使用注入的參數
log.append(Utility.getDateTime("yyyy/MM/dd HH:mm:ss.SSS"))
   .append("\t").append(host)  // 使用注入的參數
   .append("\t[").append(SOAPService.class.getSimpleName())
   .append("]\tSOAP request to: ").append(serviceUrl)
   .append("\r\n");
Utility.writeLog(logTransName, log);  // 使用注入的參數
```

### 2. OLTP.java 中的統一呼叫方式

#### HTTP 呼叫（現有模式）：
```java
// 在 QueueService.main() 中
new HTTPService(GlobalVariable.HTTP_PORT, GlobalVariable.HOST, 
               GlobalVariable.logConsole, GlobalVariable.HTTP_CONTEXT, new O2OHandler());
```

#### SOAP 呼叫（修正後）：
```java
// 在 OLTP.java 的 SOAP_SubProcess 中
SOAPService.setDefaultSettings(GlobalVariable.HOST, GlobalVariable.logTrans);
String responseStr = SOAPService.sendSOAPRequest(target, timeout, toCPStr);
```

### 3. 多層次的參數化支援

#### 層次 1：使用預設設定
```java
// 最簡單的呼叫方式（向後相容）
String response = SOAPService.sendSOAPRequest(serviceUrl, timeout, requestContent);
```

#### 層次 2：自訂 SOAPAction
```java
// 支援自訂 SOAPAction
String response = SOAPService.sendSOAPRequest(serviceUrl, timeout, requestContent, soapAction);
```

#### 層次 3：完全參數化
```java
// 完全控制所有參數
String response = SOAPService.sendSOAPRequest(serviceUrl, timeout, requestContent, 
                                            soapAction, host, logTransName);
```

## 🔧 實作細節

### SOAPService 修改要點

#### 1. 移除 GlobalVariable import
```java
// Before
import com.pic.oltp.GlobalVariable;

// After
// 移除 import，使用依賴注入
```

#### 2. 添加預設設定
```java
// 預設的日誌和主機設定（向後相容）
private static String defaultHost = "UNKNOWN_HOST";
private static String defaultLogTrans = "TRANSLOG";

public static void setDefaultSettings(String host, String logTrans) {
    defaultHost = host;
    defaultLogTrans = logTrans;
}
```

#### 3. 參數化方法簽名
```java
// 向後相容的方法
public static String sendSOAPRequest(String serviceUrl, int timeout, String requestContent)

// 支援 SOAPAction 的方法
public static String sendSOAPRequest(String serviceUrl, int timeout, String requestContent, String soapAction)

// 完全參數化的方法
public static String sendSOAPRequest(String serviceUrl, int timeout, String requestContent, 
                                   String soapAction, String host, String logTransName)
```

### SimpleSOAPClient 和 SOAPClient 修改

#### 1. 添加日誌設定屬性
```java
// 日誌設定（可選，用於依賴注入）
private String host = "UNKNOWN_HOST";
private String logTransName = "TRANSLOG";

public void setLogSettings(String host, String logTransName) {
    this.host = host;
    this.logTransName = logTransName;
}
```

#### 2. 替換所有 GlobalVariable 使用
```java
// Before
.append(GlobalVariable.HOST)
Utility.writeLog(GlobalVariable.logTrans, log);

// After
.append(host)
Utility.writeLog(logTransName, log);
```

## 📋 遷移檢查清單

### SOAPService.java
- [ ] 移除 `import com.pic.oltp.GlobalVariable;`
- [ ] 添加 `defaultHost` 和 `defaultLogTrans` 靜態變數
- [ ] 添加 `setDefaultSettings()` 方法
- [ ] 替換所有 `GlobalVariable.HOST` 為參數化變數
- [ ] 替換所有 `GlobalVariable.logTrans` 為參數化變數
- [ ] 添加多層次的方法重載

### SimpleSOAPClient.java
- [ ] 移除或註解 `import com.pic.oltp.GlobalVariable;`
- [ ] 添加 `host` 和 `logTransName` 實例變數
- [ ] 添加 `setLogSettings()` 方法
- [ ] 替換所有 GlobalVariable 使用

### SOAPClient.java
- [ ] 執行與 SimpleSOAPClient 相同的修改

### OLTP.java
- [ ] 在 `SOAP_SubProcess` 方法開始時呼叫 `SOAPService.setDefaultSettings()`

## 🧪 測試驗證

### 1. 向後相容性測試
```java
// 確保現有程式碼仍能正常運作
String response = SOAPService.sendSOAPRequest(serviceUrl, timeout, requestContent);
```

### 2. 依賴注入測試
```java
// 測試自訂設定
SOAPService.setDefaultSettings("TEST_HOST", "TEST_LOG");
String response = SOAPService.sendSOAPRequest(serviceUrl, timeout, requestContent);
```

### 3. 完全參數化測試
```java
// 測試完全控制
String response = SOAPService.sendSOAPRequest(serviceUrl, timeout, requestContent, 
                                            "CustomAction", "CustomHost", "CustomLog");
```

## 🎯 優點總結

### 1. 設計一致性
- HTTP 和 SOAP 服務使用相同的依賴注入模式
- 統一的參數傳遞方式
- 一致的錯誤處理和日誌記錄

### 2. 可測試性
- 不依賴全域狀態
- 容易進行單元測試
- 可以模擬不同的環境設定

### 3. 靈活性
- 支援多種呼叫方式
- 可在不同環境使用不同配置
- 向後相容現有程式碼

### 4. 維護性
- 減少耦合度
- 更清晰的依賴關係
- 更容易理解和修改

## 🔄 未來擴展

### 1. 配置物件模式
```java
public class SOAPConfig {
    private String host;
    private String logTransName;
    private int timeout;
    private String soapAction;
    
    // getters and setters
}

public static String sendSOAPRequest(String serviceUrl, String requestContent, SOAPConfig config)
```

### 2. 建構函式注入
```java
public class SOAPService {
    private final String host;
    private final String logTransName;
    
    public SOAPService(String host, String logTransName) {
        this.host = host;
        this.logTransName = logTransName;
    }
}
```

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
