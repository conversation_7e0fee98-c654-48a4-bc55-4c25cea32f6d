# SOAP 檔案寫入整合指南

## 問題概述

基於之前的討論，XML 結構驗證已經修復完成，但檔案寫入功能仍需完善。本指南說明如何在 OLTP.java 中實作 SOAP 回覆 XML 的檔案寫入功能，確保與現有的 HTTP 檔案寫入邏輯保持一致。

## 🔍 現狀分析

### 現有的檔案寫入架構

#### OLTP.java 中的檔案寫入方法：
1. **`writeXmlAsFile`**：用於 HTTP 請求（有加密）
2. **`writeXmlAsFileWithoutEncryption`**：用於 SOAP 請求（無加密）

#### 現有的 SOAP 檔案寫入邏輯：
```java
// OLTP.java 中已存在的方法
private void writeXmlAsFileWithoutEncryption(Element posInXML, Element forPosXML, StringBuffer xmlFileName, StringBuffer errorMsg) {
  // 1. 檔案路徑：使用 GlobalVariable.XML_LOG_PATH
  // 2. 檔案格式：timestamp_TERMINO_SOAP.txt
  // 3. 寫入模式：先寫入 "POS IN XML (SOAP - No Encryption)"，然後 append "REPLY POS XML (SOAP - No Encryption)"
  // 4. AP 層處理：提取並清理 AP 層內容
}
```

### 發現的問題

#### 問題 1：AP 層命名空間未清理
```java
// 現有代碼只移除 AP 標籤，但不清理命名空間
ReplyapStrBuffer.append(forPosXML.element("AP").asXML().replaceAll("<ns0:AP[^>]*>|</ns0:AP>", ""));
// 結果：AP 層內容仍包含命名空間前綴和宣告
```

#### 問題 2：缺乏統一的命名空間清理機制
- QueueListener 中有命名空間清理功能
- OLTP.java 中沒有對應的功能
- 導致檔案寫入的 AP 層內容不乾淨

## 🔧 修復方案

### 1. 在 OLTP.java 中整合命名空間清理功能

#### 修復前（問題代碼）：
```java
// 只移除 AP 標籤，不清理命名空間
ReplyapStrBuffer.append(forPosXML.element("AP").asXML().replaceAll("<ns0:AP[^>]*>|</ns0:AP>", ""));
ReplyAPStr = ReplyapStrBuffer.toString();  // 仍包含命名空間
```

#### 修復後（完整代碼）：
```java
// 移除 AP 標籤並清理命名空間
ReplyapStrBuffer.append(forPosXML.element("AP").asXML().replaceAll("<ns0:AP[^>]*>|</ns0:AP>", ""));

// SOAP 版本：移除命名空間後使用清理過的AP層內容
String rawReplyAPStr = ReplyapStrBuffer.toString();
ReplyAPStr = removeNamespacesFromAPContent(rawReplyAPStr);
System.out.println("ReplyAPStr (命名空間已清理)=" + ReplyAPStr);
```

### 2. 實作命名空間清理方法

#### 新增 `removeNamespacesFromAPContent` 方法：
```java
private String removeNamespacesFromAPContent(String xmlContent) {
  try {
    String cleanContent = xmlContent;

    // 步驟 1：移除所有命名空間宣告
    // 移除預設命名空間宣告 xmlns="..."
    cleanContent = cleanContent.replaceAll("\\s+xmlns=\"[^\"]*\"", "");
    
    // 移除命名空間前綴宣告 xmlns:prefix="..."
    cleanContent = cleanContent.replaceAll("\\s+xmlns:[^=\\s]+=\"[^\"]*\"", "");

    // 步驟 2：移除標籤中的命名空間前綴（保留結構完整性）
    // 處理開始標籤：<prefix:tagname> -> <tagname>
    cleanContent = cleanContent.replaceAll("<([a-zA-Z0-9_.-]+):([a-zA-Z0-9_.-]+)([^>]*)>", "<$2$3>");
    
    // 處理結束標籤：</prefix:tagname> -> </tagname>
    cleanContent = cleanContent.replaceAll("</([a-zA-Z0-9_.-]+):([a-zA-Z0-9_.-]+)>", "</$2>");

    // 步驟 3：移除屬性中的命名空間前綴
    // 處理屬性：prefix:attribute="value" -> attribute="value"
    cleanContent = cleanContent.replaceAll("\\s+([a-zA-Z0-9_.-]+):([a-zA-Z0-9_.-]+)=", " $2=");

    // 步驟 4：清理格式
    cleanContent = cleanContent.replaceAll("\\s+", " ");
    cleanContent = cleanContent.replaceAll(">\\s+<", "><");
    cleanContent = cleanContent.trim();

    // 驗證 XML 結構完整性
    boolean structureValid = validateAPXMLStructure(cleanContent);
    
    if (!structureValid) {
      return xmlContent;  // 回退到原始內容
    }
    
    // 額外檢查：確保沒有損壞的結束標籤
    if (cleanContent.contains("<Checksum>") && !cleanContent.contains("</Checksum>")) {
      return xmlContent;  // 回退到原始內容
    }
    
    return cleanContent;

  } catch (Exception e) {
    return xmlContent; // 返回原始內容
  }
}
```

### 3. 實作 XML 結構驗證方法

#### 新增 `validateAPXMLStructure` 方法：
```java
private boolean validateAPXMLStructure(String xmlContent) {
  try {
    // 主要驗證：嘗試解析 XML（最可靠的方法）
    try {
      DocumentHelper.parseText("<root>" + xmlContent + "</root>");
      System.out.println("AP XML 解析測試: ✅ 成功");
      return true;
    } catch (Exception e) {
      System.out.println("AP XML 解析測試: ❌ 失敗 - " + e.getMessage());
      return false;
    }
  } catch (Exception e) {
    return false;
  }
}
```

## 📋 修復檢查清單

### OLTP.java 修改項目
- [ ] ✅ 修改 `writeXmlAsFileWithoutEncryption` 方法，整合命名空間清理
- [ ] ✅ 新增 `removeNamespacesFromAPContent` 方法
- [ ] ✅ 新增 `validateAPXMLStructure` 方法
- [ ] ✅ 確保 AP 層內容經過命名空間清理後寫入檔案
- [ ] ✅ 保持與現有 HTTP 檔案寫入邏輯的一致性

### 功能改進
- [ ] ✅ 統一的檔案寫入邏輯和路徑配置
- [ ] ✅ 完整的命名空間清理機制
- [ ] ✅ 統一的錯誤處理和驗證
- [ ] ✅ 保持架構一致性

## 🧪 測試驗證

### 1. 執行自動測試
```bash
test_soap_file_writing.bat
```

### 2. 測試案例覆蓋

#### 測試案例 1：AP 層命名空間清理功能
```xml
<!-- 輸入 -->
<ManageTerminalResponse xmlns="http://ticketxpress.com.tw/">
  <ManageTerminalResult xmlns:a="http://schemas.datacontract.org/2004/07/eVoucher.Authorization.Common">
    <a:Checksum>a87519574d731f03955b107379c9c5be</a:Checksum>
    <a:Message>Success</a:Message>
    <a:ResponseCode>0000</a:ResponseCode>
  </ManageTerminalResult>
</ManageTerminalResponse>

<!-- 預期輸出 -->
<ManageTerminalResponse>
  <ManageTerminalResult>
    <Checksum>a87519574d731f03955b107379c9c5be</Checksum>
    <Message>Success</Message>
    <ResponseCode>0000</ResponseCode>
  </ManageTerminalResult>
</ManageTerminalResponse>
```

#### 測試案例 2：檔案寫入功能模擬
```
檔案路徑：test_output/o2odata/OLTP/logs/XML/
檔案格式：timestamp_TEST_SOAP_FILE_123_SOAP.txt

檔案內容：
POS IN XML (SOAP - No Encryption):
<?xml version="1.0" encoding="utf-8"?>
<ns0:OLTP xmlns:ns0="http://7-11.com.tw/online">
  <ns0:HEADER>
    <ns0:TERMINO>TEST_SOAP_FILE_123</ns0:TERMINO>
  </ns0:HEADER>
  <ns0:AP>Original Request Content</ns0:AP>
</ns0:OLTP>

REPLY POS XML (SOAP - No Encryption):
<?xml version="1.0" encoding="utf-8"?>
<ns0:OLTP xmlns:ns0="http://7-11.com.tw/online">
  <ns0:HEADER>
    <ns0:TERMINO>TEST_SOAP_FILE_123</ns0:TERMINO>
  </ns0:HEADER>
  <ns0:AP><ManageTerminalResponse>...</ManageTerminalResponse></ns0:AP>
</ns0:OLTP>
```

### 3. 預期的測試結果

#### 命名空間清理測試：
```
=== 測試案例 1：AP 層命名空間清理功能 ===
=== 移除 AP 層命名空間（OLTP 版本）===
步驟 1：移除命名空間宣告
  ✅ 移除預設命名空間宣告
  ✅ 移除命名空間前綴宣告
步驟 2：移除標籤中的命名空間前綴
  ✅ 移除標籤命名空間前綴
步驟 3：移除屬性中的命名空間前綴
  ✅ 移除屬性命名空間前綴

包含預設命名空間: false
包含前綴命名空間: false
包含命名空間前綴: false
有效的 XML 結構: true
命名空間清理測試結果: ✅ 成功
```

#### 檔案寫入測試：
```
=== 測試案例 3：檔案寫入功能模擬 ===
檔案寫入完成
檔案名稱: 20250729160543123_TEST_SOAP_FILE_123_SOAP.txt
✅ 檔案創建成功
檔案大小: 1024 bytes
包含 POS IN XML: true
包含 REPLY POS XML: true
檔案內容完整性: ✅ 完整
```

## 🔍 故障排除

### 問題 1：命名空間仍然存在
**可能原因**：
- 正則表達式沒有正確匹配特殊格式的命名空間
- XML 包含非標準的命名空間宣告

**解決方案**：
```java
// 檢查具體的匹配情況
System.out.println("處理前: " + xmlContent);
System.out.println("處理後: " + cleanContent);

// 添加更多的命名空間清理規則
cleanContent = cleanContent.replaceAll("\\s+xmlns:[^\\s=]+\\s*=\\s*\"[^\"]*\"", "");
cleanContent = cleanContent.replaceAll("\\s+xmlns\\s*=\\s*\"[^\"]*\"", "");
```

### 問題 2：XML 結構驗證失敗
**可能原因**：
- 命名空間移除導致 XML 格式錯誤
- 特殊字符處理問題

**解決方案**：
```java
// 在每個步驟後驗證 XML
try {
  DocumentHelper.parseText("<root>" + cleanContent + "</root>");
  System.out.println("✅ 步驟完成，XML 格式有效");
} catch (Exception e) {
  System.out.println("❌ 步驟失敗，XML 格式無效，回退");
  return xmlContent;
}
```

### 問題 3：檔案寫入失敗
**可能原因**：
- 檔案路徑配置錯誤
- 權限問題
- 磁碟空間不足

**解決方案**：
```java
// 檢查檔案路徑和權限
File parentDir = new File(fileName).getParentFile();
System.out.println("檔案路徑: " + fileName);
System.out.println("父目錄存在: " + parentDir.exists());
System.out.println("父目錄可寫: " + parentDir.canWrite());
System.out.println("可用空間: " + parentDir.getFreeSpace() + " bytes");
```

## 📊 效果監控

### 成功指標
- ✅ AP 層內容完全乾淨，無任何命名空間
- ✅ 檔案正確創建在指定路徑
- ✅ 檔案包含請求和回覆兩部分內容
- ✅ XML 結構完整，可以正確解析

### 監控方法
```java
// 在日誌中記錄處理結果
System.out.println("SOAP 檔案寫入: " + 
  (fileName != null ? "SUCCESS" : "FAILED") + 
  ", 命名空間清理: " + 
  (structureValid ? "SUCCESS" : "FAILED") + 
  ", 檔案大小: " + 
  (new File(fileName).length()) + " bytes");
```

## 🏗️ 架構改進說明

### 檔案寫入統一處理
```
修復前：
QueueListener.java ──┐
                    ├── 重複的檔案寫入邏輯
OLTP.java ──────────┘

修復後：
QueueListener.java ──── 只處理訊息佇列
OLTP.java ──────────── 統一處理檔案寫入
├── writeXmlAsFile (HTTP - 有加密)
└── writeXmlAsFileWithoutEncryption (SOAP - 無加密)
```

### 命名空間清理統一處理
```
修復前：
QueueListener.java ──── 有命名空間清理功能
OLTP.java ──────────── 沒有命名空間清理功能

修復後：
QueueListener.java ──── 訊息處理時的命名空間清理
OLTP.java ──────────── 檔案寫入時的命名空間清理
├── removeNamespacesFromAPContent
└── validateAPXMLStructure
```

### 責任分離
- **QueueListener**：專注於訊息佇列處理和 SOAP 回應構建
- **OLTP.java**：專注於業務邏輯處理、檔案寫入和命名空間清理
- **統一配置**：檔案路徑、格式和錯誤處理統一管理

### 維護性改進
- **單一檔案寫入邏輯**：只需要在 OLTP.java 中維護
- **統一的命名空間清理**：在檔案寫入時確保內容乾淨
- **一致的錯誤處理**：檔案寫入和命名空間清理錯誤統一處理
- **配置統一**：檔案路徑和格式配置集中管理

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
