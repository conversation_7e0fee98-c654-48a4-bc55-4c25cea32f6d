# SOAP 檔案結構和相依性指南

## 概述

本文件說明 SOAP 相關檔案在 CosmedApi OLTP 專案中的正確位置、package 結構和相依性關係。

## 📁 檔案結構

### 正確的檔案位置

```
CosmedApi_OLTP_v1.3/OLTPAPApi_v1.3/
├── src/
│   └── com/
│       └── pic/
│           ├── oltp/
│           │   ├── OLTP.java                    # 主要業務邏輯類別
│           │   └── GlobalVariable.java          # 全域變數
│           └── o2o/
│               └── common/
│                   ├── Utility.java             # 工具類別
│                   ├── IOltpap.java             # 介面定義
│                   ├── HTTPService.java         # HTTP 服務
│                   ├── SimpleSOAPClient.java    # 簡化 SOAP 客戶端
│                   ├── SOAPClient.java          # 標準 SOAP 客戶端
│                   ├── SOAPService.java         # SOAP 服務管理
│                   ├── SOAPXMLValidator.java    # XML 驗證工具
│                   └── SOAPConnectionDiagnostic.java # 連線診斷工具
├── bin/                                         # 編譯後的 class 檔案
├── libs/                                        # 第三方函式庫
└── docs/                                        # 文件
```

### Package 宣告

| 檔案 | Package 宣告 | 說明 |
|------|-------------|------|
| `OLTP.java` | `package com.pic.oltp;` | 主要業務邏輯層 |
| `GlobalVariable.java` | `package com.pic.oltp;` | 全域設定 |
| `SimpleSOAPClient.java` | `package com.pic.o2o.common;` | 通用工具層 |
| `SOAPClient.java` | `package com.pic.o2o.common;` | 通用工具層 |
| `SOAPService.java` | `package com.pic.o2o.common;` | 通用工具層 |
| `SOAPXMLValidator.java` | `package com.pic.o2o.common;` | 通用工具層 |
| `SOAPConnectionDiagnostic.java` | `package com.pic.o2o.common;` | 通用工具層 |
| `Utility.java` | `package com.pic.o2o.common;` | 通用工具層 |

## 🔗 相依性關係

### Import 語句對應表

#### OLTP.java (主要業務邏輯)

```java
package com.pic.oltp;

// 標準 Java 函式庫

// 第三方函式庫

// 專案內部相依性
```

#### SOAP 相關檔案的 Import 語句

**SimpleSOAPClient.java:**
```java
package com.pic.o2o.common;

import java.io.*;
import java.net.*;
import org.apache.commons.lang.exception.ExceptionUtils;
import com.pic.o2o.common.Utility;           // 同 package 工具類別
import com.pic.oltp.GlobalVariable;          // 全域變數
```

**SOAPClient.java:**
```java
package com.pic.o2o.common;

import javax.xml.soap.*;
import javax.xml.transform.*;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.http.conn.ConnectTimeoutException;
import com.pic.o2o.common.Utility;           // 同 package 工具類別
import com.pic.oltp.GlobalVariable;          // 全域變數
```

**SOAPService.java:**
```java
package com.pic.o2o.common;

import java.io.*;
import java.net.*;
import javax.xml.soap.SOAPException;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.http.conn.ConnectTimeoutException;
import com.pic.o2o.common.Utility;           // 同 package 工具類別
import com.pic.oltp.GlobalVariable;          // 全域變數
```

**SOAPXMLValidator.java:**
```java
package com.pic.o2o.common;

import java.io.*;
import javax.xml.parsers.*;
import javax.xml.transform.*;
import org.w3c.dom.Document;
import org.xml.sax.*;
import com.pic.oltp.GlobalVariable;          // 全域變數
```

**SOAPConnectionDiagnostic.java:**
```java
package com.pic.o2o.common;

import java.io.*;
import java.net.*;
import java.util.*;
import com.pic.oltp.GlobalVariable;          // 全域變數
```

## 🔧 編譯順序

### 建議的編譯順序
1. **基礎工具類別**
   ```bash
   javac -cp "libs/*" -d bin src/com/pic/oltp/GlobalVariable.java
   javac -cp "libs/*" -d bin src/com/pic/o2o/common/Utility.java
   javac -cp "libs/*" -d bin src/com/pic/o2o/common/IOltpap.java
   ```

2. **SOAP 工具類別**
   ```bash
   javac -cp "libs/*;bin" -d bin src/com/pic/o2o/common/SOAPXMLValidator.java
   javac -cp "libs/*;bin" -d bin src/com/pic/o2o/common/SOAPConnectionDiagnostic.java
   javac -cp "libs/*;bin" -d bin src/com/pic/o2o/common/SimpleSOAPClient.java
   javac -cp "libs/*;bin" -d bin src/com/pic/o2o/common/SOAPClient.java
   javac -cp "libs/*;bin" -d bin src/com/pic/o2o/common/SOAPService.java
   ```

3. **主要業務邏輯**
   ```bash
   javac -cp "libs/*;bin" -d bin src/com/pic/oltp/OLTP.java
   ```

### 自動編譯腳本
使用提供的 `build_test_soap.bat` 腳本：
```bash
build_test_soap.bat
```

## ⚠️ 常見問題和解決方案

### 問題 1：找不到類別錯誤
```
error: cannot find symbol
symbol: class SOAPClient
```

**解決方案：**
1. 檢查 import 語句是否正確
2. 確認檔案位置符合 package 宣告
3. 檢查編譯順序

### 問題 2：Package 不存在錯誤
```
error: package com.pic.o2o.common does not exist
```

**解決方案：**
1. 確認目錄結構正確
2. 檢查 CLASSPATH 設定
3. 確認相依的類別已編譯

### 問題 3：重複類別定義
```
error: duplicate class
```

**解決方案：**
1. 清理 bin 目錄：`rmdir /s bin`
2. 重新建立目錄結構
3. 按順序重新編譯

## 📋 檢查清單

### 檔案位置檢查
- [ ] 所有 SOAP 檔案都在 `src/com/pic/o2o/common/` 目錄下
- [ ] OLTP.java 在 `src/com/pic/oltp/` 目錄下
- [ ] Package 宣告與目錄結構一致

### Import 語句檢查
- [ ] OLTP.java 正確 import SOAP 相關類別
- [ ] 所有 SOAP 檔案正確 import Utility 和 GlobalVariable
- [ ] 沒有循環相依性問題

### 編譯檢查
- [ ] 所有檔案能夠成功編譯
- [ ] 生成的 class 檔案在正確位置
- [ ] 沒有編譯警告或錯誤

### 功能檢查
- [ ] SOAP_SubProcess 方法能正常呼叫
- [ ] SOAP 客戶端能正確初始化
- [ ] 診斷工具能正常運作

## 🔄 維護指南

### 新增 SOAP 相關檔案
1. 放置在 `src/com/pic/o2o/common/` 目錄下
2. 使用 `package com.pic.o2o.common;` 宣告
3. 適當 import 必要的相依性
4. 更新編譯腳本

### 修改現有檔案
1. 保持 package 宣告不變
2. 檢查 import 語句的正確性
3. 測試編譯和功能
4. 更新相關文件

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
