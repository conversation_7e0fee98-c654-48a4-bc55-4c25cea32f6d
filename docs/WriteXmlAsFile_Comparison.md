# writeXmlAsFile vs writeXmlAsFileWithoutEncryption 對比

## 概述

本文件詳細對比 `writeXmlAsFile` 和 `writeXmlAsFileWithoutEncryption` 兩個方法的差異，確保 SOAP 版本保持所有原始邏輯，只移除加密處理。

## 🔄 方法對比

### 相同的功能和邏輯

| 功能 | writeXmlAsFile | writeXmlAsFileWithoutEncryption | 狀態 |
|------|----------------|--------------------------------|------|
| **檔案命名邏輯** | `{timestamp}_{termino}.txt` | `{timestamp}_{termino}_SOAP.txt` | ✅ 一致 |
| **AP 標籤移除** | `replaceAll("<ns0:AP[^>]*>|</ns0:AP>", "")` | `replaceAll("<ns0:AP[^>]*>|</ns0:AP>", "")` | ✅ 完全相同 |
| **Console 輸出** | `System.out.println("apStrBuffer="+...)` | `System.out.println("apStrBuffer="+...)` | ✅ 完全相同 |
| **Document 處理** | `DocumentHelper.parseText(forPosXML.asXML())` | `DocumentHelper.parseText(forPosXML.asXML())` | ✅ 完全相同 |
| **檔案寫入格式** | OutputFormat.createPrettyPrint() | OutputFormat.createPrettyPrint() | ✅ 完全相同 |
| **錯誤處理** | IOException/DocumentException/Exception | IOException/DocumentException/Exception | ✅ 完全相同 |
| **檔案名稱回傳** | `StringUtils.substringAfterLast(fileName, "/")` | `StringUtils.substringAfterLast(fileName, "/")` | ✅ 完全相同 |

### 主要差異

| 項目 | writeXmlAsFile | writeXmlAsFileWithoutEncryption |
|------|----------------|--------------------------------|
| **加密處理** | ✅ 使用 AES/GCM/PKCS5Padding 加密 | ❌ 不進行加密 |
| **AP 層內容** | 加密後的 Base64 字串 | 原始 XML 內容 |
| **檔案標題** | `"POS IN XML:"` | `"POS IN XML (SOAP - No Encryption):"` |
| **回應標題** | `"REPLY POS XML:"` | `"REPLY POS XML (SOAP - No Encryption):"` |
| **檔案後綴** | `.txt` | `_SOAP.txt` |

## 📝 詳細程式碼對比

### 1. AP 層處理邏輯

**writeXmlAsFile (加密版本):**
```java
// 取得AP節點的XML字串，使用replaceAll()方法刪除AP標籤
apStrBuffer.append(posInXML.element("AP").asXML().replaceAll("<ns0:AP[^>]*>|</ns0:AP>", ""));
System.out.println("apStrBuffer="+apStrBuffer.toString());
// 加密AP層
apStr=Base64.getEncoder().encodeToString(posin_cipher.doFinal(
        apStrBuffer.toString().getBytes("UTF-8")));
```

**writeXmlAsFileWithoutEncryption (不加密版本):**
```java
// 取得AP節點的XML字串，使用replaceAll()方法刪除AP標籤
apStrBuffer.append(posInXML.element("AP").asXML().replaceAll("<ns0:AP[^>]*>|</ns0:AP>", ""));
System.out.println("apStrBuffer="+apStrBuffer.toString());
// SOAP 版本：不進行加密，直接使用原始AP層內容
apStr = apStrBuffer.toString();
```

### 2. 回應處理邏輯

**writeXmlAsFile (加密版本):**
```java
// 取得AP節點的XML字串，使用replaceAll()方法刪除AP標籤
ReplyapStrBuffer.append(forPosXML.element("AP").asXML().replaceAll("<ns0:AP[^>]*>|</ns0:AP>", ""));
System.out.println("ReplyapStrBuffer="+ReplyapStrBuffer.toString());
// 加密AP層
ReplyAPStr=Base64.getEncoder().encodeToString(Replyin_cipher.doFinal(
        ReplyapStrBuffer.toString().getBytes("UTF-8")));
System.out.println("ReplyAPStr="+ReplyAPStr);
```

**writeXmlAsFileWithoutEncryption (不加密版本):**
```java
// 取得AP節點的XML字串，使用replaceAll()方法刪除AP標籤
ReplyapStrBuffer.append(forPosXML.element("AP").asXML().replaceAll("<ns0:AP[^>]*>|</ns0:AP>", ""));
System.out.println("ReplyapStrBuffer="+ReplyapStrBuffer.toString());
// SOAP 版本：不進行加密，直接使用原始AP層內容
ReplyAPStr = ReplyapStrBuffer.toString();
System.out.println("ReplyAPStr="+ReplyAPStr);
```

### 3. 檔案標題差異

**writeXmlAsFile:**
```java
xmlwriter.write("POS IN XML:");
// ...
xmlwriter.write("REPLY POS XML:");
```

**writeXmlAsFileWithoutEncryption:**
```java
xmlwriter.write("POS IN XML (SOAP - No Encryption):");
// ...
xmlwriter.write("REPLY POS XML (SOAP - No Encryption):");
```

## 🧪 測試驗證

### Console 輸出測試

兩個方法都會產生相同的 Console 輸出格式：

```
apStrBuffer=<ManageTerminal><manageTerminalRequest><Channel>Test</Channel>...
ReplyapStrBuffer=<ManageTerminalResponse><Result>SUCCESS</Result>...
ReplyAPStr=<ManageTerminalResponse><Result>SUCCESS</Result>...
```

**差異：**
- `writeXmlAsFile`: ReplyAPStr 顯示加密後的 Base64 字串
- `writeXmlAsFileWithoutEncryption`: ReplyAPStr 顯示原始 XML 內容

### 檔案內容測試

**加密版本檔案內容：**
```xml
POS IN XML:
<root>
    <HEADER>...</HEADER>
    <AP>eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...</AP>  <!-- Base64 加密內容 -->
</root>

REPLY POS XML:
<root>
    <HEADER>...</HEADER>
    <AP>eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...</AP>  <!-- Base64 加密內容 -->
</root>
```

**不加密版本檔案內容：**
```xml
POS IN XML (SOAP - No Encryption):
<root>
    <HEADER>...</HEADER>
    <AP><ManageTerminal><manageTerminalRequest>...</manageTerminalRequest></ManageTerminal></AP>  <!-- 原始 XML 內容 -->
</root>

REPLY POS XML (SOAP - No Encryption):
<root>
    <HEADER>...</HEADER>
    <AP><ManageTerminalResponse><Result>SUCCESS</Result>...</ManageTerminalResponse></AP>  <!-- 原始 XML 內容 -->
</root>
```

## ✅ 驗證檢查清單

### 功能一致性檢查

- [x] **AP 標籤移除邏輯** - 使用相同的正則表達式
- [x] **Console 輸出** - 保留所有 `System.out.println` 語句
- [x] **檔案處理邏輯** - 相同的 Document 操作和 XMLWriter 設定
- [x] **錯誤處理** - 相同的 try-catch 結構和錯誤訊息格式
- [x] **檔案命名** - 保持相同邏輯，只添加 `_SOAP` 後綴
- [x] **變數命名** - 保持相同的變數名稱和處理流程

### 差異確認

- [x] **移除加密** - 不使用 Cipher 和 Base64 編碼
- [x] **檔案標識** - 添加 `(SOAP - No Encryption)` 標識
- [x] **原始內容保留** - AP 層內容保持原始 XML 格式

## 🔧 使用場景

### 何時使用 writeXmlAsFile
- HTTP_SubProcess 和 HTTP_SubProcess_AP
- 需要加密保護的敏感資料
- 符合現有安全規範的場景

### 何時使用 writeXmlAsFileWithoutEncryption
- SOAP_SubProcess 和 SOAP_SubProcess_AP
- 需要查看原始 XML 內容進行除錯
- SOAP 協議本身提供安全保護的場景

## 📋 維護注意事項

1. **保持同步更新**：如果 `writeXmlAsFile` 方法有任何邏輯更新，需要同步更新 `writeXmlAsFileWithoutEncryption`
2. **測試覆蓋**：兩個方法都需要相應的單元測試
3. **文件更新**：任何變更都需要更新相關文件

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
