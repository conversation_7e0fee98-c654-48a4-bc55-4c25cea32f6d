# SOAP 連線故障排除指南

## 概述

本文件提供 SOAP_SubProcess 功能中 "2001" 系統連線失敗錯誤的完整診斷和修復指南。

## 🚨 錯誤碼 "2001" 分析

### 錯誤訊息
```
錯誤碼: 2001
錯誤描述: 系統連線失敗
```

### 觸發條件
1. **ConnectException** - 網路連線被拒絕
2. **SocketTimeoutException** - 連線或讀取逾時
3. **IOException** - 其他 I/O 錯誤
4. **服務回應為空** - SOAP 服務無回應

## 🔍 診斷步驟

### 1. 執行自動診斷
```java
// 使用診斷工具
SOAPConnectionDiagnostic.DiagnosticResult result = 
    SOAPConnectionDiagnostic.diagnose(serviceUrl, timeout, testContent);

System.out.println(result.toString());
```

### 2. 查看 Console 輸出
執行 SOAP_SubProcess 時，查看詳細的診斷資訊：

```
=== SOAP_SubProcess 診斷資訊 ===
目標 URL: http://soap.service.com/endpoint
逾時設定: 30 秒
請求內容長度: 1024 字元
請求內容預覽: <?xml version="1.0"...
==============================

=== 網路連線診斷 ===
檢查 URL: http://soap.service.com/endpoint
連線測試回應碼: 200
連線測試結果: 成功
==================

=== SimpleSOAPClient 連線診斷 ===
目標 URL: http://soap.service.com/endpoint
連線逾時: 30 秒
讀取逾時: 30 秒
URL 解析成功: http://soap.service.com/endpoint
協議: http
主機: soap.service.com
埠號: 80
路徑: /endpoint
HTTP 連線物件建立成功
```

### 3. 檢查日誌檔案
查看生成的 `_SOAP.txt` 日誌檔案：
- 檔案位置：`{XML_LOG_PATH}/{timestamp}_{termino}_SOAP.txt`
- 內容包含：原始請求和回應（未加密）

## 🔧 常見問題和解決方案

### 問題 1：URL 格式錯誤
**症狀：**
```
❌ URL 格式錯誤: no protocol: soap.service.com
```

**解決方案：**
1. 檢查 `transfile.txt` 中的 URL 設定
2. 確保 URL 包含協議（http:// 或 https://）
```
# 錯誤
MANAGE_TERMINAL,SOAP_SubProcess,soap.service.com/endpoint,30,N

# 正確
MANAGE_TERMINAL,SOAP_SubProcess,http://soap.service.com/endpoint,30,N
```

### 問題 2：DNS 解析失敗
**症狀：**
```
❌ 主機名稱無法解析: soap.service.com
```

**解決方案：**
1. 檢查主機名稱是否正確
2. 測試 DNS 解析：`nslookup soap.service.com`
3. 檢查網路連線
4. 考慮使用 IP 位址代替主機名稱

### 問題 3：連線被拒絕
**症狀：**
```
❌ 連線被拒絕: Connection refused
```

**解決方案：**
1. **檢查服務狀態**：確認 SOAP 服務是否啟動
2. **檢查埠號**：確認服務監聽的埠號正確
3. **檢查防火牆**：確認防火牆允許連線
4. **檢查網路路由**：確認網路路徑暢通

### 問題 4：連線逾時
**症狀：**
```
❌ 連線逾時: Read timed out
```

**解決方案：**
1. **增加逾時時間**：
```
# transfile.txt 中增加逾時時間
MANAGE_TERMINAL,SOAP_SubProcess,http://soap.service.com/endpoint,60,N
```

2. **檢查網路延遲**：
```bash
ping soap.service.com
```

3. **檢查服務效能**：確認服務回應時間

### 問題 5：HTTP 錯誤回應
**症狀：**
```
HTTP 回應碼: 500
HTTP 回應訊息: Internal Server Error
```

**解決方案：**
1. **檢查 SOAP 請求格式**：
   - 驗證 XML 格式正確
   - 檢查命名空間設定
   - 確認 SOAPAction 標頭

2. **檢查服務端日誌**：查看 SOAP 服務的錯誤日誌

3. **驗證請求內容**：
```xml
<!-- 檢查生成的 SOAP Envelope 格式 -->
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/">
    <SOAP-ENV:Body>
        <m:ManageTerminal xmlns:m="http://ticketxpress.com.tw/">
            <!-- 業務內容 -->
        </m:ManageTerminal>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>
```

### 問題 6：SSL/TLS 錯誤（HTTPS）
**症狀：**
```
❌ SSL handshake failed
```

**解決方案：**
1. **檢查憑證**：確認 SSL 憑證有效
2. **信任憑證**：將憑證加入 Java 信任庫
3. **檢查 TLS 版本**：確認支援的 TLS 版本
4. **暫時使用 HTTP**：測試時可先使用 HTTP

## 🧪 診斷工具使用

### 1. 完整診斷
```java
// 執行完整診斷
String serviceUrl = "http://soap.service.com/endpoint";
int timeout = 30;
String testContent = "<ManageTerminal>...</ManageTerminal>";

SOAPConnectionDiagnostic.DiagnosticResult result = 
    SOAPConnectionDiagnostic.diagnose(serviceUrl, timeout, testContent);

if (!result.isSuccess()) {
    System.out.println("診斷失敗: " + result.getSummary());
    for (String rec : result.getRecommendations()) {
        System.out.println("建議: " + rec);
    }
}
```

### 2. 快速測試
```java
// 快速連線測試
boolean isReachable = SOAPConnectionDiagnostic.quickTest(serviceUrl, timeout);
if (!isReachable) {
    System.out.println("服務無法連線");
}
```

## 📋 檢查清單

### 配置檢查
- [ ] `transfile.txt` 中的 URL 格式正確
- [ ] 逾時時間設定合理（建議 30-60 秒）
- [ ] 服務端點路徑正確

### 網路檢查
- [ ] DNS 解析正常
- [ ] 網路連線暢通
- [ ] 防火牆設定正確
- [ ] 代理伺服器設定（如適用）

### 服務檢查
- [ ] SOAP 服務正常運作
- [ ] 服務端點正確
- [ ] HTTP 方法支援 POST
- [ ] 接受 `text/xml` Content-Type

### 請求檢查
- [ ] SOAP Envelope 格式正確
- [ ] 命名空間設定正確
- [ ] SOAPAction 標頭正確
- [ ] 請求內容有效

## 🔧 進階故障排除

### 1. 網路層診斷
```bash
# 測試 DNS 解析
nslookup soap.service.com

# 測試網路連線
telnet soap.service.com 80

# 測試 HTTP 連線
curl -I http://soap.service.com/endpoint
```

### 2. 抓包分析
使用 Wireshark 或 tcpdump 分析網路封包：
1. 檢查 TCP 連線建立
2. 檢查 HTTP 請求和回應
3. 檢查 SOAP 內容格式

### 3. 服務端測試
使用 Postman 或 SoapUI 測試 SOAP 服務：
1. 驗證服務可用性
2. 測試請求格式
3. 檢查回應內容

## 📞 支援聯絡

如果問題持續存在，請提供以下資訊：

1. **完整的 Console 輸出**
2. **生成的 `_SOAP.txt` 日誌檔案**
3. **`transfile.txt` 配置**
4. **網路環境資訊**
5. **SOAP 服務文件**

---

**版本**：1.3  
**更新日期**：2024年  
**維護者**：CosmedApi OLTP 開發團隊
